on early-init

on post-fs
#ifdef OPLUS_FEATURE_CHG_BASIC
    setprop vendor.usb.vid "0x22d9"
#else
#    setprop vendor.usb.vid "0x0E8D"
#endif OPLUS_FEATURE_CHG_BASIC
    mkdir /dev/usb-ffs 0775 shell shell
    mkdir /dev/usb-ffs/adb 0770 shell shell
    mkdir /config/usb_gadget/g1 0770 shell shell
    write /config/usb_gadget/g1/idVendor ${vendor.usb.vid}
    write /config/usb_gadget/g1/bcdDevice 0x0223
    write /config/usb_gadget/g1/bcdUSB 0x0200
    write /config/usb_gadget/g1/os_desc/use 1
    mkdir /config/usb_gadget/g1/strings/0x409 0770
    write /config/usb_gadget/g1/strings/0x409/serialnumber ${ro.serialno}
    write /config/usb_gadget/g1/strings/0x409/manufacturer ${ro.product.manufacturer}
    write /config/usb_gadget/g1/strings/0x409/product ${ro.product.model}
    mkdir /config/usb_gadget/g1/functions/accessory.gs2
    mkdir /config/usb_gadget/g1/functions/audio_source.gs3
    mkdir /config/usb_gadget/g1/functions/ffs.adb
    mkdir /config/usb_gadget/g1/functions/ffs.mtp
    write /config/usb_gadget/g1/functions/ffs.mtp/os_desc/interface.MTP/compatible_id "MTP"
    mkdir /config/usb_gadget/g1/functions/ffs.ptp
    mkdir /config/usb_gadget/g1/functions/rndis.gs4
    mkdir /config/usb_gadget/g1/functions/midi.gs5
    mkdir /config/usb_gadget/g1/functions/acm.gs0
    mkdir /config/usb_gadget/g1/functions/acm.gs1
    mkdir /config/usb_gadget/g1/functions/acm.gs2
    mkdir /config/usb_gadget/g1/functions/acm.gs3
    mkdir /config/usb_gadget/g1/functions/mass_storage.usb0
    mkdir /config/usb_gadget/g1/functions/hid.gs0
    mkdir /config/usb_gadget/g1/functions/via_modem.gs0
    mkdir /config/usb_gadget/g1/functions/via_ets.gs0
    mkdir /config/usb_gadget/g1/functions/via_atc.gs0
    mkdir /config/usb_gadget/g1/configs/b.1 0770 shell shell
    mkdir /config/usb_gadget/g1/configs/b.1/strings/0x409 0770 shell shell
    write /config/usb_gadget/g1/os_desc/b_vendor_code 0x1
    write /config/usb_gadget/g1/os_desc/qw_sign "MSFT100"
    write /config/usb_gadget/g1/configs/b.1/MaxPower 500
    symlink /config/usb_gadget/g1/configs/b.1 /config/usb_gadget/g1/os_desc/b.1
    mount functionfs adb /dev/usb-ffs/adb rmode=0770,fmode=0660,uid=2000,gid=2000,no_disconnect=1
    mkdir /dev/usb-ffs/mtp 0770 mtp mtp
    mkdir /dev/usb-ffs/ptp 0770 mtp mtp
    mount functionfs mtp /dev/usb-ffs/mtp rmode=0770,fmode=0660,uid=1024,gid=1024,no_disconnect=1
    mount functionfs ptp /dev/usb-ffs/ptp rmode=0770,fmode=0660,uid=1024,gid=1024,no_disconnect=1

on boot
    setprop sys.usb.configfs 1
    setprop vendor.usb.controller ${sys.usb.controller}
    setprop vendor.usb.acm_cnt 0
    setprop vendor.usb.acm_port0 ""
    setprop vendor.usb.acm_port1 ""
    setprop vendor.usb.acm_enable 0
    # bind mtp on the big core for MT6893
    write /sys/class/android_usb/android0/f_mtp/cpu_mask 0x80
    write /sys/module/usb_f_mtp/parameters/mtp_rx_cont 1
    chmod 0664 /sys/class/android_usb/android0/iSerial
    chown root system /sys/class/android_usb/android0/iSerial

    chown radio system /sys/class/usb_rawbulk/data/enable
    chmod 0660 /sys/class/usb_rawbulk/data/enable
    chown radio system /sys/class/usb_rawbulk/ets/enable
    chmod 0660 /sys/class/usb_rawbulk/ets/enable
    chown radio system /sys/class/usb_rawbulk/atc/enable
    chmod 0660 /sys/class/usb_rawbulk/atc/enable
    chown radio system /sys/class/usb_rawbulk/pcv/enable
    chmod 0660 /sys/class/usb_rawbulk/pcv/enable
    chown radio system /sys/class/usb_rawbulk/gps/enable
    chmod 0660 /sys/class/usb_rawbulk/gps/enable
    chown system radio /dev/ttyGS0
    chmod 0660 /dev/ttyGS0
    chown system radio /dev/ttyGS1
    chmod 0660 /dev/ttyGS1
    chown system radio /dev/ttyGS2
    chmod 0660 /dev/ttyGS2
    chown system radio /dev/ttyGS3
    chmod 0660 /dev/ttyGS3

    # for UAC MAX dpidle time
    write /sys/module/xhci_hcd/parameters/dpidle_fs_max 0
    write /sys/module/xhci_hcd/parameters/dpidle_hs_max 0

    # for usb otg sdcard hot plug detection
    write /sys/module/block/parameters/events_dfl_poll_msecs 1000

on charger
    mkdir /config/usb_gadget/g1 0770 shell shell
    write /config/usb_gadget/g1/idVendor 0x0E8D
    write /config/usb_gadget/g1/bcdDevice 0x0223
    write /config/usb_gadget/g1/bcdUSB 0x0200
    write /config/usb_gadget/g1/os_desc/use 1
    mkdir /config/usb_gadget/g1/strings/0x409 0770
    write /config/usb_gadget/g1/strings/0x409/serialnumber ${ro.serialno}
    write /config/usb_gadget/g1/strings/0x409/manufacturer ${ro.product.manufacturer}
    write /config/usb_gadget/g1/strings/0x409/product ${ro.product.model}
    mkdir /config/usb_gadget/g1/functions/hid.gs0
    mkdir /config/usb_gadget/g1/configs/b.1 0770 shell shell
    mkdir /config/usb_gadget/g1/configs/b.1/strings/0x409 0770 shell shell
    write /config/usb_gadget/g1/configs/b.1/MaxPower 500
    mount functionfs adb /dev/usb-ffs/adb uid=2000,gid=2000
    setprop sys.usb.configfs 1
    setprop vendor.usb.controller ${sys.usb.controller}
    setprop vendor.usb.acm_cnt 0
    setprop vendor.usb.acm_port0 ""
    setprop vendor.usb.acm_port1 ""
    setprop vendor.usb.acm_enable 0
    setprop sys.usb.config hid

### more command at init.usb.configfs.rc ###
on property:sys.usb.config=none && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/idVendor ${vendor.usb.vid}
    rm /config/usb_gadget/g1/configs/b.1/f4
    rm /config/usb_gadget/g1/configs/b.1/f5
    write /sys/class/udc/${vendor.usb.controller}/device/saving 0

### HID for KPOC ###
on property:sys.usb.config=hid && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "HID"
    write /config/usb_gadget/g1/idProduct 0x20FF
    write /config/usb_gadget/g1/functions/hid.gs0/protocol 0
    write /config/usb_gadget/g1/functions/hid.gs0/subclass 0
    #write /config/usb_gadget/g1/functions/hid.gs0/report_length 4
    #write /config/usb_gadget/g1/functions/hid.gs0/report_desc \\x05\\x01\\x09\\x00\\xa1\\x01\\xc0
    symlink /config/usb_gadget/g1/functions/hid.gs0 /config/usb_gadget/g1/configs/b.1/f1
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

### main function : adb ###
### start adbd at init.usb.configfs.rc ###
on property:sys.usb.config=adb && property:vendor.usb.acm_cnt=0 && \
property:sys.usb.configfs=1
#ifdef OPLUS_FEATURE_CHG_BASIC
    setprop vendor.usb.pid 0x2769
#else
#    setprop vendor.usb.pid 0x201C
#endif OPLUS_FEATURE_CHG_BASIC
on property:sys.usb.config=adb && property:vendor.usb.acm_cnt=1 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x2006
    setprop vendor.usb.acm_port1 ""
on property:sys.usb.config=adb && property:vendor.usb.acm_cnt=2 && \
property:sys.usb.configfs=1
#ifdef OPLUS_FEATURE_CHG_BASIC
    setprop vendor.usb.pid 0x200E
#else
#    setprop vendor.usb.pid 0x2029
#endif OPLUS_FEATURE_CHG_BASIC

on property:sys.usb.ffs.ready=1 && property:sys.usb.config=adb && \
property:vendor.usb.acm_enable=1 && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "adb_acm"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    write /sys/class/udc/${vendor.usb.controller}/device/saving 1
    symlink /config/usb_gadget/g1/functions/mass_storage.usb0 /config/usb_gadget/g1/configs/b.1/f1
    symlink /config/usb_gadget/g1/functions/ffs.adb /config/usb_gadget/g1/configs/b.1/f2
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port0} /config/usb_gadget/g1/configs/b.1/f3
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port1} /config/usb_gadget/g1/configs/b.1/f4
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.ffs.ready=1 && property:sys.usb.config=adb && \
property:vendor.usb.acm_enable=0 && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "adb"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    symlink /config/usb_gadget/g1/functions/ffs.adb /config/usb_gadget/g1/configs/b.1/f1
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

### main function : mtp ###
on property:sys.usb.config=mtp && property:vendor.usb.acm_cnt=0 && \
property:sys.usb.configfs=1
#ifdef OPLUS_FEATURE_CHG_BASIC
    setprop vendor.usb.pid 0x2764
#else
#    setprop vendor.usb.pid 0x2008
#endif OPLUS_FEATURE_CHG_BASIC
on property:sys.usb.config=mtp && property:vendor.usb.acm_cnt=1 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x2012
    setprop vendor.usb.acm_port1 ""
on property:sys.usb.config=mtp && property:vendor.usb.acm_cnt=2 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x202A

on property:sys.usb.config=mtp && property:vendor.usb.acm_enable=1 && \
property:sys.usb.configfs=1 && property:vendor.usb.ffs.mtp.ready=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "mtp_acm"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    write /config/usb_gadget/g1/os_desc/use 1
    write /sys/class/udc/${vendor.usb.controller}/device/saving 1
    symlink /config/usb_gadget/g1/functions/ffs.mtp /config/usb_gadget/g1/configs/b.1/f1
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port0} /config/usb_gadget/g1/configs/b.1/f2
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port1} /config/usb_gadget/g1/configs/b.1/f3
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.config=mtp && property:vendor.usb.acm_enable=0 && \
property:sys.usb.configfs=1 && property:vendor.usb.ffs.mtp.ready=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "mtp"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    write /config/usb_gadget/g1/os_desc/use 1
    write /sys/class/udc/${vendor.usb.controller}/device/saving 2
    symlink /config/usb_gadget/g1/functions/ffs.mtp /config/usb_gadget/g1/configs/b.1/f1
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

### start adbd at init.usb.configfs.rc ###
on property:sys.usb.config=mtp,adb && property:vendor.usb.acm_cnt=0 && \
property:sys.usb.configfs=1
#ifdef OPLUS_FEATURE_CHG_BASIC
    setprop vendor.usb.pid 0x2765
#else
#    setprop vendor.usb.pid 0x201D
#endif OPLUS_FEATURE_CHG_BASIC
on property:sys.usb.config=mtp,adb && property:vendor.usb.acm_cnt=1 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x200A
    setprop vendor.usb.acm_port1 ""
on property:sys.usb.config=mtp,adb && property:vendor.usb.acm_cnt=2 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x2026

on property:sys.usb.ffs.ready=1 && property:sys.usb.config=mtp,adb && \
property:vendor.usb.acm_enable=1 && property:sys.usb.configfs=1 && property:vendor.usb.ffs.mtp.ready=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "mtp_adb_acm"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    write /config/usb_gadget/g1/os_desc/use 1
    write /sys/class/udc/${vendor.usb.controller}/device/saving 1
    symlink /config/usb_gadget/g1/functions/ffs.mtp /config/usb_gadget/g1/configs/b.1/f1
    symlink /config/usb_gadget/g1/functions/ffs.adb /config/usb_gadget/g1/configs/b.1/f2
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port0} /config/usb_gadget/g1/configs/b.1/f3
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port1} /config/usb_gadget/g1/configs/b.1/f4
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.ffs.ready=1 && property:sys.usb.config=mtp,adb && \
property:vendor.usb.acm_enable=0 && property:sys.usb.configfs=1 && property:vendor.usb.ffs.mtp.ready=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "mtp_adb"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    write /config/usb_gadget/g1/os_desc/use 1
    symlink /config/usb_gadget/g1/functions/ffs.mtp /config/usb_gadget/g1/configs/b.1/f1
    symlink /config/usb_gadget/g1/functions/ffs.adb /config/usb_gadget/g1/configs/b.1/f2
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

### main function : ptp ###
on property:sys.usb.config=ptp && property:vendor.usb.acm_cnt=0 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x200B
on property:sys.usb.config=ptp && property:vendor.usb.acm_cnt=1 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x2013
    setprop vendor.usb.acm_port1 ""
on property:sys.usb.config=ptp && property:vendor.usb.acm_cnt=2 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x202B

on property:sys.usb.config=ptp && property:vendor.usb.acm_enable=1 && \
property:sys.usb.configfs=1 && property:vendor.usb.ffs.ptp.ready=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "ptp_acm"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    write /sys/class/udc/${vendor.usb.controller}/device/saving 1
    symlink /config/usb_gadget/g1/functions/ffs.ptp /config/usb_gadget/g1/configs/b.1/f1
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port0} /config/usb_gadget/g1/configs/b.1/f2
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port1} /config/usb_gadget/g1/configs/b.1/f3
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.config=ptp && property:vendor.usb.acm_enable=0 && \
property:sys.usb.configfs=1 && property:vendor.usb.ffs.ptp.ready=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "ptp"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    write /sys/class/udc/${vendor.usb.controller}/device/saving 2
    symlink /config/usb_gadget/g1/functions/ffs.ptp /config/usb_gadget/g1/configs/b.1/f1
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

### start adbd at init.usb.configfs.rc ###
on property:sys.usb.config=ptp,adb && property:vendor.usb.acm_cnt=0 && \
property:sys.usb.configfs=1
#ifdef OPLUS_FEATURE_CHG_BASIC
    setprop vendor.usb.pid 0x2772
#else
#    setprop vendor.usb.pid 0x200C
#endif OPLUS_FEATURE_CHG_BASIC
on property:sys.usb.config=ptp,adb && property:vendor.usb.acm_cnt=1 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x200D
    setprop vendor.usb.acm_port1 ""
on property:sys.usb.config=ptp,adb && property:vendor.usb.acm_cnt=2 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x2027

on property:sys.usb.ffs.ready=1 && property:sys.usb.config=ptp,adb && \
property:vendor.usb.acm_enable=1 && property:sys.usb.configfs=1 && property:vendor.usb.ffs.ptp.ready=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "ptp_adb_acm"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    write /sys/class/udc/${vendor.usb.controller}/device/saving 1
    symlink /config/usb_gadget/g1/functions/ffs.ptp /config/usb_gadget/g1/configs/b.1/f1
    symlink /config/usb_gadget/g1/functions/ffs.adb /config/usb_gadget/g1/configs/b.1/f2
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port0} /config/usb_gadget/g1/configs/b.1/f3
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port1} /config/usb_gadget/g1/configs/b.1/f4
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.ffs.ready=1 && property:sys.usb.config=ptp,adb && \
property:vendor.usb.acm_enable=0 && property:sys.usb.configfs=1 && property:vendor.usb.ffs.ptp.ready=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "ptp_adb"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    symlink /config/usb_gadget/g1/functions/ffs.ptp /config/usb_gadget/g1/configs/b.1/f1
    symlink /config/usb_gadget/g1/functions/ffs.adb /config/usb_gadget/g1/configs/b.1/f2
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

### main function : rndis ###
on property:sys.usb.config=rndis && property:vendor.usb.acm_cnt=0 && \
property:sys.usb.configfs=1
#ifdef OPLUS_FEATURE_CHG_BASIC
    setprop vendor.usb.pid 0x276A
#else
#    setprop vendor.usb.pid 0x2004
#endif OPLUS_FEATURE_CHG_BASIC
on property:sys.usb.config=rndis && property:vendor.usb.acm_cnt=1 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x2011
    setprop vendor.usb.acm_port1 ""
on property:sys.usb.config=rndis && property:vendor.usb.acm_cnt=2 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x202C

on property:sys.usb.config=rndis && property:vendor.usb.acm_enable=1 && \
property:sys.usb.configfs=1
    mkdir /config/usb_gadget/g1/functions/rndis.gs4
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "ums_acm"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    write /sys/class/udc/${vendor.usb.controller}/device/saving 1
    symlink /config/usb_gadget/g1/functions/rndis.gs4 /config/usb_gadget/g1/configs/b.1/f1
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port0} /config/usb_gadget/g1/configs/b.1/f2
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port1} /config/usb_gadget/g1/configs/b.1/f3
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.config=rndis && property:vendor.usb.acm_enable=0 && \
property:sys.usb.configfs=1
    mkdir /config/usb_gadget/g1/functions/rndis.gs4
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "rndis"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    symlink /config/usb_gadget/g1/functions/rndis.gs4 /config/usb_gadget/g1/configs/b.1/f1
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.config=rndis,adb && property:sys.usb.configfs=1
    start adbd

on property:sys.usb.config=rndis,adb && property:vendor.usb.acm_cnt=0 && \
property:sys.usb.configfs=1
#ifdef OPLUS_FEATURE_CHG_BASIC
    setprop vendor.usb.pid 0x2766
#else
#    setprop vendor.usb.pid 0x2005
#endif OPLUS_FEATURE_CHG_BASIC
on property:sys.usb.config=rndis,adb && property:vendor.usb.acm_cnt=1 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x2010
    setprop vendor.usb.acm_port1 ""
on property:sys.usb.config=rndis,adb && property:vendor.usb.acm_cnt=2 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x2028

on property:sys.usb.ffs.ready=1 && property:sys.usb.config=rndis,adb && \
property:vendor.usb.acm_enable=1 && property:sys.usb.configfs=1
    mkdir /config/usb_gadget/g1/functions/rndis.gs4
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "rndis_adb_acm"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    write /sys/class/udc/${vendor.usb.controller}/device/saving 1
    symlink /config/usb_gadget/g1/functions/rndis.gs4 /config/usb_gadget/g1/configs/b.1/f1
    symlink /config/usb_gadget/g1/functions/ffs.adb /config/usb_gadget/g1/configs/b.1/f2
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port0} /config/usb_gadget/g1/configs/b.1/f3
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port1} /config/usb_gadget/g1/configs/b.1/f4
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.ffs.ready=1 && property:sys.usb.config=rndis,adb && \
property:vendor.usb.acm_enable=0 && property:sys.usb.configfs=1
    mkdir /config/usb_gadget/g1/functions/rndis.gs4
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "rndis_adb"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    symlink /config/usb_gadget/g1/functions/rndis.gs4 /config/usb_gadget/g1/configs/b.1/f1
    symlink /config/usb_gadget/g1/functions/ffs.adb /config/usb_gadget/g1/configs/b.1/f2
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

### main function : midi ###
on property:sys.usb.config=midi && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/idProduct 0x2046

on property:sys.usb.config=midi,adb && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/idProduct 0x2048

### main function : accessory ###
on property:sys.usb.config=accessory && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/idVendor 0x18d1
    write /config/usb_gadget/g1/idProduct 0x2d00

on property:sys.usb.config=accessory,adb && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/idVendor 0x18d1
    write /config/usb_gadget/g1/idProduct 0x2d01

### main function : audio_source ###
on property:sys.usb.config=audio_source && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/idVendor 0x18d1
    write /config/usb_gadget/g1/idProduct 0x2d02

on property:sys.usb.config=audio_source,adb && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/idVendor 0x18d1
    write /config/usb_gadget/g1/idProduct 0x2d03

### main function : accessory,audio_source ###
on property:sys.usb.config=accessory,audio_source && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/idVendor 0x18d1
    write /config/usb_gadget/g1/idProduct 0x2d04

on property:sys.usb.config=accessory,audio_source,adb && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/idVendor 0x18d1
    write /config/usb_gadget/g1/idProduct 0x2d05

### main function : mass_storage ###
on property:sys.usb.config=mass_storage && property:vendor.usb.acm_cnt=0 && \
property:sys.usb.configfs=1
#ifdef OPLUS_FEATURE_CHG_BASIC
    setprop vendor.usb.pid 0x2768
#else
#    setprop vendor.usb.pid 0x2002
#endif OPLUS_FEATURE_CHG_BASIC
on property:sys.usb.config=mass_storage && property:vendor.usb.acm_cnt=1 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x200F
    setprop vendor.usb.acm_port1 ""
on property:sys.usb.config=mass_storage && property:vendor.usb.acm_cnt=2 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x202D

on property:sys.usb.config=mass_storage && property:vendor.usb.acm_enable=1 && \
property:sys.usb.configfs=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "ums_acm"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    write /sys/class/udc/${vendor.usb.controller}/device/saving 1
    symlink /config/usb_gadget/g1/functions/mass_storage.usb0 /config/usb_gadget/g1/configs/b.1/f1
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port0} /config/usb_gadget/g1/configs/b.1/f2
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port1} /config/usb_gadget/g1/configs/b.1/f3
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.config=mass_storage && property:vendor.usb.acm_enable=0 && \
property:sys.usb.configfs=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "ums"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    symlink /config/usb_gadget/g1/functions/mass_storage.usb0 /config/usb_gadget/g1/configs/b.1/f1
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.config=mass_storage,adb && property:vendor.usb.acm_cnt=0 && \
property:sys.usb.configfs=1
#ifdef OPLUS_FEATURE_CHG_BASIC
    setprop vendor.usb.pid 0x2767
#else
#    setprop vendor.usb.pid 0x2003
#endif OPLUS_FEATURE_CHG_BASIC
on property:sys.usb.config=mass_storage,adb && property:vendor.usb.acm_cnt=1 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x2006
    setprop vendor.usb.acm_port1 ""
on property:sys.usb.config=mass_storage,adb && property:vendor.usb.acm_cnt=2 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x2029

on property:sys.usb.config=mass_storage,adb && property:sys.usb.configfs=1
    start adbd

on property:sys.usb.ffs.ready=1 && property:sys.usb.config=mass_storage,adb && \
property:vendor.usb.acm_enable=1 && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "ums_adb_acm"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    write /sys/class/udc/${vendor.usb.controller}/device/saving 1
    symlink /config/usb_gadget/g1/functions/mass_storage.usb0 /config/usb_gadget/g1/configs/b.1/f1
    symlink /config/usb_gadget/g1/functions/ffs.adb /config/usb_gadget/g1/configs/b.1/f2
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port0} /config/usb_gadget/g1/configs/b.1/f3
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port1} /config/usb_gadget/g1/configs/b.1/f4
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.ffs.ready=1 && property:sys.usb.config=mass_storage,adb && \
property:vendor.usb.acm_enable=0 && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "ums_adb"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    symlink /config/usb_gadget/g1/functions/mass_storage.usb0 /config/usb_gadget/g1/configs/b.1/f1
    symlink /config/usb_gadget/g1/functions/ffs.adb /config/usb_gadget/g1/configs/b.1/f2
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

### main function : bicr ###
on property:sys.usb.config=bicr && property:vendor.usb.acm_cnt=0 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x2002
on property:sys.usb.config=bicr && property:vendor.usb.acm_cnt=1 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x200F
    setprop vendor.usb.acm_port1 ""
on property:sys.usb.config=bicr && property:vendor.usb.acm_cnt=2 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x202D

on property:sys.usb.config=bicr && property:vendor.usb.acm_enable=1 && \
property:sys.usb.configfs=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "ums_acm"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    write /sys/class/udc/${vendor.usb.controller}/device/saving 1
    write /config/usb_gadget/g1/functions/mass_storage.usb0/lun.0/cdrom 1
    write /config/usb_gadget/g1/functions/mass_storage.usb0/lun.0/file "/dev/block/loop0"
    symlink /config/usb_gadget/g1/functions/mass_storage.usb0 /config/usb_gadget/g1/configs/b.1/f1
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port0} /config/usb_gadget/g1/configs/b.1/f2
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port1} /config/usb_gadget/g1/configs/b.1/f3
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.config=bicr && property:vendor.usb.acm_enable=0 && \
property:sys.usb.configfs=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "ums"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    write /config/usb_gadget/g1/functions/mass_storage.usb0/lun.0/cdrom 1
    write /config/usb_gadget/g1/functions/mass_storage.usb0/lun.0/file "/dev/block/loop0"
    symlink /config/usb_gadget/g1/functions/mass_storage.usb0 /config/usb_gadget/g1/configs/b.1/f1
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.config=bicr,adb && property:vendor.usb.acm_cnt=0 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x2003
on property:sys.usb.config=bicr,adb && property:vendor.usb.acm_cnt=1 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x2006
    setprop vendor.usb.acm_port1 ""
on property:sys.usb.config=bicr,adb && property:vendor.usb.acm_cnt=2 && \
property:sys.usb.configfs=1
    setprop vendor.usb.pid 0x2029

on property:sys.usb.config=bicr,adb && property:sys.usb.configfs=1
    start adbd

on property:sys.usb.ffs.ready=1 && property:sys.usb.config=bicr,adb && \
property:vendor.usb.acm_enable=1 && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "ums_adb_acm"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    write /sys/class/udc/${vendor.usb.controller}/device/saving 1
    write /config/usb_gadget/g1/functions/mass_storage.usb0/lun.0/cdrom 1
    write /config/usb_gadget/g1/functions/mass_storage.usb0/lun.0/file "/dev/block/loop0"
    symlink /config/usb_gadget/g1/functions/mass_storage.usb0 /config/usb_gadget/g1/configs/b.1/f1
    symlink /config/usb_gadget/g1/functions/ffs.adb /config/usb_gadget/g1/configs/b.1/f2
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port0} /config/usb_gadget/g1/configs/b.1/f3
    symlink /config/usb_gadget/g1/functions/acm.gs${vendor.usb.acm_port1} /config/usb_gadget/g1/configs/b.1/f4
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

on property:sys.usb.ffs.ready=1 && property:sys.usb.config=bicr,adb && \
property:vendor.usb.acm_enable=0 && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "ums_adb"
    write /config/usb_gadget/g1/idProduct ${vendor.usb.pid}
    write /config/usb_gadget/g1/functions/mass_storage.usb0/lun.0/cdrom 1
    write /config/usb_gadget/g1/functions/mass_storage.usb0/lun.0/file "/dev/block/loop0"
    symlink /config/usb_gadget/g1/functions/mass_storage.usb0 /config/usb_gadget/g1/configs/b.1/f1
    symlink /config/usb_gadget/g1/functions/ffs.adb /config/usb_gadget/g1/configs/b.1/f2
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}

### ACM APPEND SERIES ###
on property:persist.vendor.radio.port_index="0" && property:sys.boot_completed=1
    setprop vendor.usb.acm_idx ""
    setprop vendor.usb.acm_cnt 0
    setprop vendor.usb.acm_enable 0
    setprop sys.usb.config none
    setprop sys.usb.config ${sys.usb.state}

on property:persist.vendor.radio.port_index="1" && property:sys.boot_completed=1
    setprop sys.usb.config acm_gs0
on property:sys.usb.config=mass_storage,adb,acm
    setprop sys.usb.config acm_gs0
on property:sys.usb.config=acm_gs0
    setprop vendor.usb.acm_port0 0
    setprop vendor.usb.acm_port1 ""
    setprop vendor.usb.acm_cnt 1
    setprop vendor.usb.acm_enable 1
    setprop sys.usb.config none
    setprop sys.usb.config ${sys.usb.state}
    setprop vendor.usb.acm_idx "1"

on property:persist.vendor.radio.port_index="2" && property:sys.boot_completed=1
    setprop sys.usb.config acm_gs1
on property:sys.usb.config=acm_gs1
    setprop vendor.usb.acm_port0 1
    setprop vendor.usb.acm_port1 ""
    setprop vendor.usb.acm_cnt 1
    setprop vendor.usb.acm_enable 1
    setprop sys.usb.config none
    setprop sys.usb.config ${sys.usb.state}
    setprop vendor.usb.acm_idx "2"

on property:persist.vendor.radio.port_index="3" && property:sys.boot_completed=1
    setprop sys.usb.config acm_gs2
on property:sys.usb.config=acm_third
    setprop sys.usb.config acm_gs2
on property:sys.usb.config=acm_gs2
    setprop vendor.usb.acm_port0 2
    setprop vendor.usb.acm_port1 ""
    setprop vendor.usb.acm_cnt 1
    setprop vendor.usb.acm_enable 1
    setprop sys.usb.config none
    setprop sys.usb.config ${sys.usb.state}
    setprop vendor.usb.acm_idx "3"

on property:persist.vendor.radio.port_index="4" && property:sys.boot_completed=1
    setprop sys.usb.config acm_gs3
on property:sys.usb.config=gs3
    setprop sys.usb.config acm_gs3
on property:sys.usb.config=mtp,gs3
    setprop sys.usb.config acm_gs3
on property:sys.usb.config=acm_gs3
    setprop vendor.usb.acm_port0 3
    setprop vendor.usb.acm_port1 ""
    setprop vendor.usb.acm_cnt 1
    setprop vendor.usb.acm_enable 1
    setprop sys.usb.config none
    setprop sys.usb.config ${sys.usb.state}
    setprop vendor.usb.acm_idx "4"

on property:sys.usb.config=mtp,gs0gs1
    setprop sys.usb.config acm_gs0gs1
on property:sys.usb.config=gs0gs1
    setprop sys.usb.config acm_gs0gs1
on property:sys.usb.config=rndis,gs0gs1
    setprop sys.usb.config acm_gs0gs1
on property:sys.usb.config=rndis,adb,gs0gs1
    setprop sys.usb.config acm_gs0gs1
on property:sys.usb.config=acm_gs0gs1
    setprop vendor.usb.acm_port0 0
    setprop vendor.usb.acm_port1 1
    setprop vendor.usb.acm_cnt 2
    setprop vendor.usb.acm_enable 1
    setprop sys.usb.config none
    setprop sys.usb.config ${sys.usb.state}
    setprop vendor.usb.acm_idx "1,2"

on property:persist.vendor.radio.port_index="1,4" && property:sys.boot_completed=1
    setprop sys.usb.config acm_gs0gs3
on property:sys.usb.config=gs3,dual_acm
    setprop sys.usb.config acm_gs0gs3
on property:sys.usb.config=mtp,gs3,dual_acm
    setprop sys.usb.config acm_gs0gs3
on property:sys.usb.config=mtp,gs0gs3
    setprop sys.usb.config acm_gs0gs3
on property:sys.usb.config=gs0gs3
    setprop sys.usb.config acm_gs0gs3
on property:sys.usb.config=rndis,gs0gs3
    setprop sys.usb.config acm_gs0gs3
on property:sys.usb.config=rndis,adb,gs0gs3
    setprop sys.usb.config acm_gs0gs3
on property:sys.usb.config=acm_gs0gs3
    setprop vendor.usb.acm_port0 0
    setprop vendor.usb.acm_port1 3
    setprop vendor.usb.acm_cnt 2
    setprop vendor.usb.acm_enable 1
    setprop sys.usb.config none
    setprop sys.usb.config ${sys.usb.state}
    setprop vendor.usb.acm_idx "1,4"

on property:sys.usb.config=gs1gs3
    setprop sys.usb.config acm_gs1gs3
on property:sys.usb.config=mtp,gs1gs3
    setprop sys.usb.config acm_gs1gs3
on property:sys.usb.config=rndis,gs1gs3
    setprop sys.usb.config acm_gs1gs3
on property:sys.usb.config=acm_gs1gs3
    setprop vendor.usb.acm_port0 1
    setprop vendor.usb.acm_port1 3
    setprop vendor.usb.acm_cnt 2
    setprop vendor.usb.acm_enable 1
    setprop sys.usb.config none
    setprop sys.usb.config ${sys.usb.state}
    setprop vendor.usb.acm_idx "2,4"

# For ATM (Android Test Mode)
on property:vendor.usb.clear=1 && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/UDC "none"
    write /config/usb_gadget/g1/bDeviceClass 0
    write /config/usb_gadget/g1/bDeviceSubClass 0
    write /config/usb_gadget/g1/bDeviceProtocol 0
    rm /config/usb_gadget/g1/configs/b.1/f1
    rm /config/usb_gadget/g1/configs/b.1/f2
    rm /config/usb_gadget/g1/configs/b.1/f3
    rm /config/usb_gadget/g1/configs/b.1/f4
    rm /config/usb_gadget/g1/configs/b.1/f5
    rmdir /config/usb_gadget/g1/functions/rndis.gs4
    write /sys/class/udc/${vendor.usb.controller}/device/saving 0
    setprop vendor.usb.clear 2

# has permission issue when read from ${sys.usb.ffs.ready}
on property:sys.usb.ffs.ready=0
   setprop vendor.usb.ffs.ready 0
on property:sys.usb.ffs.ready=1
   setprop vendor.usb.ffs.ready 1

on property:sys.usb.config=atm_gs0 && property:sys.usb.configfs=1
    stop adbd
    setprop vendor.usb.ffs.ready 0
    setprop vendor.usb.clear 1

on property:vendor.usb.ffs.ready=0 && property:sys.usb.config=atm_gs0 && \
property:vendor.usb.clear=2 && property:sys.usb.configfs=1
    start adbd

on property:vendor.usb.ffs.ready=1 && property:sys.usb.config=atm_gs0 && \
property:vendor.usb.clear=2 && property:sys.usb.configfs=1
    setprop vendor.usb.acm_port0 0
    setprop vendor.usb.acm_port1 ""
    setprop vendor.usb.acm_cnt 1
    setprop vendor.usb.acm_enable 1
    write /sys/class/udc/${vendor.usb.controller}/device/saving 1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "atm_gs0"
    write /config/usb_gadget/g1/idProduct 0x200A
    symlink /config/usb_gadget/g1/functions/ffs.mtp /config/usb_gadget/g1/configs/b.1/f1
    symlink /config/usb_gadget/g1/functions/ffs.adb /config/usb_gadget/g1/configs/b.1/f2
    symlink /config/usb_gadget/g1/functions/acm.gs0 /config/usb_gadget/g1/configs/b.1/f3
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    #setprop sys.usb.state ${sys.usb.config}
    setprop vendor.usb.pid 0x200A
    #setprop vendor.usb.clear 0
    setprop vendor.usb.acm_idx "1"

on property:sys.usb.config=atm_gs0gs3 && property:sys.usb.configfs=1
    stop adbd
    setprop vendor.usb.ffs.ready 0
    setprop vendor.usb.clear 1

on property:vendor.usb.ffs.ready=0 && property:sys.usb.config=atm_gs0gs3 && \
property:vendor.usb.clear=2 && property:sys.usb.configfs=1
    start adbd

on property:vendor.usb.ffs.ready=1 && property:sys.usb.config=atm_gs0gs3 && \
property:vendor.usb.clear=2 && property:sys.usb.configfs=1
    setprop vendor.usb.acm_port0 0
    setprop vendor.usb.acm_port1 3
    setprop vendor.usb.acm_cnt 2
    setprop vendor.usb.acm_enable 1
    write /sys/class/udc/${vendor.usb.controller}/device/saving 1
#ifdef OPLUS_FEATURE_CHG_BASIC
    write /config/usb_gadget/g1/idProduct 0x200E
    symlink /config/usb_gadget/g1/functions/ffs.adb /config/usb_gadget/g1/configs/b.1/f1
    symlink /config/usb_gadget/g1/functions/acm.gs0 /config/usb_gadget/g1/configs/b.1/f2
    symlink /config/usb_gadget/g1/functions/acm.gs3 /config/usb_gadget/g1/configs/b.1/f3
#else
#    write /config/usb_gadget/g1/idProduct 0x2026
#    symlink /config/usb_gadget/g1/functions/ffs.mtp /config/usb_gadget/g1/configs/b.1/f1
#    symlink /config/usb_gadget/g1/functions/ffs.adb /config/usb_gadget/g1/configs/b.1/f2
#    symlink /config/usb_gadget/g1/functions/acm.gs0 /config/usb_gadget/g1/configs/b.1/f3
#    symlink /config/usb_gadget/g1/functions/acm.gs3 /config/usb_gadget/g1/configs/b.1/f4
#endif OPLUS_FEATURE_CHG_BASIC
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    #setprop sys.usb.state ${sys.usb.config}
    setprop vendor.usb.pid 0x2026
    #setprop vendor.usb.clear 0
    setprop vendor.usb.acm_idx "1,4"

### CHARGING ###
on property:sys.usb.config=cdp && property:sys.usb.configfs=1
    write /config/usb_gadget/g1/configs/b.1/strings/0x409/configuration "mtp"
    write /config/usb_gadget/g1/idProduct 0x2008
    write /config/usb_gadget/g1/os_desc/use 1
    symlink /config/usb_gadget/g1/functions/ffs.mtp /config/usb_gadget/g1/configs/b.1/f1
    write /config/usb_gadget/g1/UDC ${vendor.usb.controller}
    setprop sys.usb.state ${sys.usb.config}
    setprop vendor.usb.pid 0x2008

on property:sys.usb.config=charging_yes
    setprop vendor.usb.charging yes
    setprop sys.usb.config none
    setprop sys.usb.config ${sys.usb.state}

on property:sys.usb.config=charging_no
    setprop sys.usb.config none
    setprop sys.usb.config ${sys.usb.state}
    setprop vendor.usb.charging no

on property:vendor.usb.charging=yes
    write /sys/class/udc/${vendor.usb.controller}/device/mode 0
on property:vendor.usb.charging=no
    write /sys/class/udc/${vendor.usb.controller}/device/mode 1

on property:vendor.usb.bicr=yes
    write /sys/class/android_usb/android0/f_mass_storage/bicr 1
    write /sys/class/android_usb/android0/f_mass_storage/lun/file "/dev/block/loop0"
on property:vendor.usb.bicr=no
    write /sys/class/android_usb/android0/f_mass_storage/bicr 0
    write /sys/class/android_usb/android0/f_mass_storage/lun/file "off"

on property:vendor.em.usb.set=term_sel
    write /proc/mtk_usb/usb-phy0/u2_phy/term_sel ${vendor.em.usb.value}
on property:vendor.em.usb.set=vrt_sel
    write /proc/mtk_usb/usb-phy0/u2_phy/vrt_sel ${vendor.em.usb.value}
on property:vendor.em.usb.set=phy_rev6
    write /proc/mtk_usb/usb-phy0/u2_phy/phy_rev6 ${vendor.em.usb.value}
on property:vendor.em.usb.set=discth
    write /proc/mtk_usb/usb-phy0/u2_phy/discth ${vendor.em.usb.value}

### vendor process start ###
on property:vendor.usb.config=*
    setprop sys.usb.config ${vendor.usb.config}
### vendor process end ###

### test command start ###
on property:vendor.usb.test=*
    setprop sys.usb.config none
    setprop sys.usb.config ${vendor.usb.test}
### test command end ###

on property:vendor.usb.printk=*
	write /proc/sys/kernel/printk ${vendor.usb.printk}

on property:persist.vendor.usb.printk=*
	setprop vendor.usb.printk ${persist.vendor.usb.printk}

on property:vendor.usb.speed.mode=u3
	write /sys/class/udc/${vendor.usb.controller}/device/max_speed super-speed
	write /sys/class/udc/${vendor.usb.controller}/device/mode 0
	write /sys/class/udc/${vendor.usb.controller}/device/mode 1

on property:vendor.usb.speed.mode=u2
	write /sys/class/udc/${vendor.usb.controller}/device/max_speed high-speed
	write /sys/class/udc/${vendor.usb.controller}/device/mode 0
	write /sys/class/udc/${vendor.usb.controller}/device/mode 1

on property:persist.vendor.usb.speed.mode=u3
	write /sys/class/udc/${vendor.usb.controller}/device/max_speed super-speed
	write /sys/class/udc/${vendor.usb.controller}/device/mode 0
	write /sys/class/udc/${vendor.usb.controller}/device/mode 1

on property:persist.vendor.usb.speed.mode=u2
	write /sys/class/udc/${vendor.usb.controller}/device/max_speed high-speed
	write /sys/class/udc/${vendor.usb.controller}/device/mode 0
	write /sys/class/udc/${vendor.usb.controller}/device/mode 1

on property:vendor.usb.sib_enable=1
	write /proc/mtk_usb/usb-phy0/u3_phy/sib 1

on property:vendor.usb.sib_enable=0
	write /proc/mtk_usb/usb-phy0/u3_phy/sib 0

on property:vendor.usb.testmode=0
	write /proc/mtk_usb/testmode "0"
on property:vendor.usb.testmode=1
	write /proc/mtk_usb/testmode "test SE0 NAK"
on property:vendor.usb.testmode=2
	write /proc/mtk_usb/testmode "test J"
on property:vendor.usb.testmode=3
	write /proc/mtk_usb/testmode "test K"
on property:vendor.usb.testmode=4
	write /proc/mtk_usb/testmode "test packet"
### PLATFORM VARIANT, cat $rc_file | grep write | sort | grep -vE "android0|usb20_phy" | awk '{print $2}' | uniq ###
