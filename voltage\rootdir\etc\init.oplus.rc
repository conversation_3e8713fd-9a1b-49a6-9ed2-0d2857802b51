#
# Copyright (C) 2022 The LineageOS Project
#
# SPDX-License-Identifier: Apache-2.0
#

on post-fs && property:ro.vendor.hw.ram=8GB
    setprop dalvik.vm.heapstartsize 24m
    setprop dalvik.vm.heapgrowthlimit 256m
    setprop dalvik.vm.heapsize 512m
    setprop dalvik.vm.heaptargetutilization 0.46
    setprop dalvik.vm.heapminfree 8m
    setprop dalvik.vm.heapmaxfree 48m
on post-fs && property:ro.vendor.hw.ram=12GB
    setprop dalvik.vm.heapstartsize 24m
    setprop dalvik.vm.heapgrowthlimit 384m
    setprop dalvik.vm.heapsize 512m
    setprop dalvik.vm.heaptargetutilization 0.42
    setprop dalvik.vm.heapminfree 8m
    setprop dalvik.vm.heapmaxfree 56m

on boot
    # Power HAL
    setprop vendor.powerhal.init 1

on property:sys.boot_completed=1
    # Enable suspend to RAM
    write /sys/power/mem_sleep "deep"
