#Add for record engineermode test result
on boot
    chmod 664 /proc/oplusCustom/Sensor
    chown system root /proc/oplusCustom/Sensor

# MTK platform .rc configure
on post-fs-data
    # calibration
    mkdir /data/vendor/sensor 0774 system system
    # Sensor
    chmod 0660 /dev/hf_manager
    chown system system /dev/hf_manager
    #ifdef OPLUS_FEATURE_SENSOR
    chmod 0660 /dev/m_virtual_sensor_misc
    chown system system /dev/m_virtual_sensor_misc
    chmod 0660 /sys/class/oplus_sensor/m_virtual_sensor_misc/virtual_sensoractive
    chmod 0660 /sys/class/oplus_sensor/m_virtual_sensor_misc/virtual_sensordelay
    chmod 0660 /sys/class/oplus_sensor/m_virtual_sensor_misc/virtual_sensorbatch
    chmod 0660 /sys/class/oplus_sensor/m_virtual_sensor_misc/virtual_sensorflush
    chown system system /sys/class/oplus_sensor/m_virtual_sensor_misc/virtual_sensoractive
    chown system system /sys/class/oplus_sensor/m_virtual_sensor_misc/virtual_sensordelay
    chown system system /sys/class/oplus_sensor/m_virtual_sensor_misc/virtual_sensorbatch
    chown system system /sys/class/oplus_sensor/m_virtual_sensor_misc/virtual_sensorflush
    #endif