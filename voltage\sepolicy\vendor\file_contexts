# Camera
/data/vendor/camera_update(/.*)?                                                                        u:object_r:vendor_camera_update_data_file:s0
/mnt/vendor/persist/camera(/.*)?                                                                        u:object_r:persist_camera_file:s0
/(odm|vendor/odm)/bin/hw/vendor\.oplus\.hardware\.engcamera@1\.0-service                                u:object_r:mtk_hal_camera_exec:s0
/(odm|vendor/odm)/bin/hw/vendor\.oplus\.hardware\.cammidasservice@1\.0-service                          u:object_r:mtk_hal_camera_exec:s0
/(vendor|odm)/lib(64)?/android\.hardware\.graphics\.allocator@2\.0\.so                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/android\.hardware\.graphics\.allocator@3\.0\.so                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/android\.hardware\.graphics\.allocator@4\.0\.so                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/android\.hardware\.graphics\.common-V2-ndk_platform\.so                          u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/android\.hardware\.graphics\.common-V2-ndk\.so                                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/vendor\.oplus\.hardware\.ormsHalService-V1-ndk_platform\.so                      u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libAlgoProcess\.so                                                               u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libapsjpeg\.so                                                                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libapsexif\.so                                                                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libextendfile\.so                                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libapspng\.so                                                                    u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libpngwrapper\.so                                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libqdMetaData\.so                                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libVDBlurlessAPI_v2\.so                                                          u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmpbase\.so                                                                    u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_hdr_denoise_api\.so                                                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_high_dynamic_range\.so                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_smart_denoise\.so                                                     u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_low_light_hdr\.so                                                     u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libPerfectlyClearCrux\.so                                                        u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_super_night_raw\.so                                                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_hdrplus_hvx_stub\.so                                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmegvii_superiq\.so                                                            u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmegface\.so                                                                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libgnustl_shared\.so                                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/siq_ocl_cache\.so                                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/cache\.so                                                                        u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/superiq_model\.so                                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libVDSuperPhotoAPI\.so                                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libsdk_sr\.so                                                                    u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/sr_models\.bin                                                                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_distortion_correction\.so                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/lib_rectify\.so                                                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libtriplecam_image_optical_zoom\.so                                              u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libs5kgw1_triplecam_image_optical_zoom\.so                                       u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libimx586_triplecam_image_optical_zoom\.so                                       u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmtkcam_3rdparty\.arc\.common\.so                                              u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmtkcam_3rdparty\.arc\.fusion\.so                                              u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libstdc\+\+\.so                                                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_dualcam_bokeh_api\.so                                                 u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_dualcam_refocus_left\.so                                              u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_dualcam_refocus_right\.so                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_dualcam_refocus_uw\.so                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_dualcam_refocus_preview\.so                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_dualcam_bokeh_frt_api\.so                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_dualcam_refocus_frt_image\.so                                         u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_dualcam_refocus_frt_preview\.so                                       u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libOPAlgoCamSinglePortrait\.so                                                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_aicolor_image\.so                                                     u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_aicolor_video\.so                                                     u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_singlecam_aicolor_image\.so                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_singlecam_aicolor_video\.so                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_supernight\.so                                                        u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_super_night_se_raw\.so                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_portraitsupernight\.so                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libportrait_repair_ocl\.so                                                       u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libportrait_repair_ppl3_ocl\.so                                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libportrait_repair_apu\.so                                                       u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libneuron_runtime\.so                                                            u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libVDDualCameraBlurlessAPI\.so                                                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_hdr_couple_api\.so                                                    u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_hdr_couple_api_v4\.so                                                 u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_high_dynamic_range_couple\.so                                         u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_high_dynamic_range_couple_v4\.so                                      u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_low_light_hdr_for_neon\.so                                            u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_merge_hdr_denoise_api\.so                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libstblur_capture_api\.so                                                        u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libstblur_api\.so                                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libBokehPre\.so                                                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libaiseg\.so                                                                     u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libAncHumanSegFigureFusion\.so                                                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libancbase_rt_fusion\.so                                                         u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libdla_loader\.so                                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarmnn\.so                                                                     u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libcmdl\.so                                                                      u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/liblightgbm\.so                                                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_beautyshot\.so                                                        u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libFaceBeautyCap\.so                                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libOPAlgoCamFaceBeautyCap\.so                                                    u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libOPAlgoCamAiBeautyFaceRetouchCn\.so                                            u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libOPAlgoCamAiBeautyEyeRetouchCn\.so                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libOPAlgoCamAIBeautyBodyDetection\.so                                            u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libstface_fd_api\.so                                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libVDEyeEnhance\.so                                                              u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libPolarrRender\.so                                                              u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libFilterWrapper\.so                                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libVDUpScale\.so                                                                 u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libcdsprpc\.so                                                                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libadsprpc\.so                                                                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/vendor\.mediatek\.hardware\.camera\.isphal@1\.0\.so                              u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_sn_mtk_apu\.so                                                        u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmegface_rt_bokeh\.so                                                          u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libAncHumBokeh\.so                                                               u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libAncHumBokehPost\.so                                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libAncHumanRetain\.so                                                            u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libnp\-loader\.so                                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_scbokeh_image\.so                                                     u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_scbokeh_video\.so                                                     u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libCaptureBokeh\.so                                                              u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libtflite_mtk\.so                                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_relighting_pro_image\.so                                              u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libtflite_mtk_static\.so                                                         u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libtextclassifier_hash\.so                                                       u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libVDBayerHDR\.so                                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libVDExternal\.so                                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmtkisp_metadata\.so                                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmtkisp_metadata_v2\.so                                                        u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_portrait_super_night_raw\.so                                          u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_psn_mtk_apu\.so                                                       u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libVDPostureDetection\.so                                                        u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libaisd\.so                                                                      u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libml_util\.so                                                                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libVDBlurless\.so                                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libFaceBeautyPre\.so                                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libXDocProcessSDK\.so                                                            u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libSuperTextWrapper\.so                                                          u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libYTCommon\.so                                                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libalhDZ\.so                                                                     u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libAncSegmentSdk\.so                                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libFaceBeautyPICap\.so                                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libFaceDistortionCorrection\.so                                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libPerfectlyClearCruxOpt\.so                                                     u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libcvface_api\.so                                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libhci_face_camera_api\.so                                                       u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libRedeyeReduce\.so                                                              u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libSuperSensor\.so                                                               u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libSuperSensorFallback\.so                                                       u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libSuperSensorProcessor\.so                                                      u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libSuperSensorProcessorCWrapper\.so                                              u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libWaterMark\.so                                                                 u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libancbase_segment\.so                                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_lensstaindetection\.so                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libstfd_mobile_api\.so                                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libAlgoInterface\.so                                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libOPLUS_SCPortrait\.so                                                          u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_preview_antibanding\.so                                               u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libNamaWrapper\.so                                                               u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libCNamaSDK\.so                                                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libfuai\.so                                                                      u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libCNamaSDK_vendor\.so                                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libfuai_vendor\.so                                                               u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libStarMode\.so                                                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libc\+\+\_shared\.so                                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libbsproxy\.so                                                                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libbinder\.so                                                                    u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libormshalclient\.so                                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/vendor\.oplus\.hardware\.orms@1\.0\.so                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libapusys\.so                                                                    u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libosensehalclient\.so                                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/vendor\.oplus\.hardware\.osense\.client@1\.0\.so                                 u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libwatermark_photo\.so                                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libSkinUniformity_Cap\.so                                                        u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libSkinUniformity_Prv\.so                                                        u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libOppoCentorStage\.so                                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libAncHumVideoBase\.so                                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libAncHumVideoBasePost\.so                                                       u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libancbase_rt_retain\.so                                                         u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libancbase_rt_bokeh\.so                                                          u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libanc_np-loader\.so                                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_frc\.so                                                               u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libCOppLceTonemapAPI\.so                                                         u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libneuron_adapter\.so                                                            u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libperfctl_vendor\.so                                                            u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmidasserviceintf\.so                                                          u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/vendor\.oplus\.hardware\.cammidasservice@1\.0\.so                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmotionblur\.so                                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libancbase_segbase\.so                                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libAncSegBaseJni\.so                                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libAncSegBaseSdk\.so                                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libPerfectColor\.so                                                              u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libop-ai-beauty-body-detection\.so                                               u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libop-ai-beauty-body-detection-in\.so                                            u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libop-ai-beauty-faceretouch-cn\.so                                               u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libop-ai-beauty-faceretouch-in\.so                                               u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libyuv2\.so                                                                      u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libDeVIS\.so                                                                     u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libEIS\.so                                                                       u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libhyperlapse\.so                                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libOGLManager\.so                                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libui_oplus\.so                                                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libdiptest_iopipe_wpe\.so                                                        u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libalCFR\.so                                                                     u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/liboplusblur_capture_api\.so                                                     u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/lib2DSlender\.so                                                                 u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libFaceBeautyJni\.so                                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libimgClarityEvaluate\.so                                                        u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libFDClite\.so                                                                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libdla_loader_ins\.so                                                            u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/lib_oplus_eye_enhance_cap\.so                                                    u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/lib_oplus_eye_enhance_pre\.so                                                    u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_dualcam_wt_calibration\.so                                            u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_dualcam_wt_verification\.so                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libcalibverifyW_T\.so                                                            u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libsuperNight_mtk\.so                                                            u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_portrait_super_night_raw_mtk\.so                                      u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_super_night_raw_mtk\.so                                               u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libion_mtk_sys\.so                                                               u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libion_ulit_sys\.so                                                              u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libaisr\.so                                                                      u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libaisrsharpen\.so                                                               u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_hdr_denoise_api_v4\.so                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_high_dynamic_range_v4\.so                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_smart_denoise_v4\.so                                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_ultra_high_dynamic_range\.so                                          u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_long_exposure_capture\.so                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_long_exposure_preview\.so                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libaideblur\.so                                                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libaiboost_te\.so                                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libwrapper_te\.so                                                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/vendor\.oplus\.hardware\.cameraextension@1\.0-service-impl\.so                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/vendor\.oplus\.hardware\.cameraextension@1\.0\.so                                u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libPreviewDecisionOld\.so                                                        u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libeffect_custom\.so                                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libflatbuffers-cpp\.so                                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libruy\.so                                                                       u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libneuron_runtime\.5\.so                                                         u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libapu_mdw\.so                                                                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libapu_mdw_batch\.so                                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/mt6877/libneuron_runtime\.5\.so                                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/mt6983/libneuron_runtime\.5\.so                                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/mt6895/libneuron_runtime\.5\.so                                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/mt6893/libneuron_runtime\.5\.so                                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmvpu_cic_ci_compiler\.so                                                      u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmvpu_clc_cl_compiler\.so                                                      u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmvpu_clc_mvpu_debuginfo\.so                                                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmvpu_clc_mvpu_elf\.so                                                         u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmvpu_clc_mvpu_utility\.so                                                     u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmvpu_clc_vpu_isa\.so                                                          u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmvpu_config\.so                                                               u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmvpu_config_data\.so                                                          u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmvpu_engine\.so                                                               u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmvpu_engine_pub\.so                                                           u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmvpu_pattern\.so                                                              u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmvpu_pattern_pub\.so                                                          u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmvpu_runtime\.so                                                              u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libmvpu_runtime_pub\.so                                                          u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libcrypto\.so                                                                    u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libcutils\.so                                                                    u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libion_mtk\.so                                                                   u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libion\.so                                                                       u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/liblog\.so                                                                       u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libc\+\+\.so                                                                     u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libbase\.so                                                                      u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libArcNetMtk\.so                                                                 u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_super_night_raw_bin\.so                                               u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_turbo_fusion_raw_super_night\.so                                      u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_turbo_fusion_raw_portrait_super_night\.so                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libancbase_segmulti\.so                                                          u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libAncSegMultiSdk\.so                                                            u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarc\.ion\.so                                                                  u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_turbo_raw\.so                                                         u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_turbo_hdr_raw\.so                                                     u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libarcsoft_turbo_fusion_raw_grf\.so                                              u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libaiboost_sydet\.so                                                             u:object_r:same_process_hal_file:s0
/(vendor|odm)/lib(64)?/libautocropImg\.so                                                               u:object_r:same_process_hal_file:s0

# Charging
/dev/oplus_chg                                                                                          u:object_r:oplus_charger_device:s0
/(odm|vendor/odm)/bin/hw/vendor\.oplus\.hardware\.charger@1\.0-service                                  u:object_r:hal_charger_oplus_exec:s0

# Display
/dev/oplus_display                                                                                      u:object_r:oplus_display_device:s0
/sys/devices/platform/disp-leds/leds/lcd-backlight(/.*)?   u:object_r:sysfs_leds:s0

# Vibrator
/vendor/bin/hw/vendor\.qti\.hardware\.vibrator\.service                               u:object_r:hal_vibrator_default_exec:s0

# Nfc
/dev/nq-nci                                                                                             u:object_r:nfc_device:s0

# Sensors
/(vendor|system/vendor)/bin/hw/android\.hardware\.sensors@2\.0-service-multihal\.mt6893                 u:object_r:mtk_hal_sensors_exec:s0
/(odm|vendor/odm)/bin/hw/vendor\.oplus\.hardware\.oplusSensor@1\.0-service                              u:object_r:mtk_hal_sensors_exec:s0
/dev/m_virtual_sensor_misc                                                                              u:object_r:virtual_sensor_device:s0
/dev/block/sdc4                                                                                         u:object_r:oplus_block_device:s0
/dev/block/11270000.ufshci/by-name/oplusreserve*                                                        u:object_r:oplus_block_device:s0
/dev/block/soc/11270000.ufshci/by-name/oplusreserve*                                                    u:object_r:oplus_block_device:s0
/dev/block/platform/11270000.ufshci/by-name/reserve.*                                                   u:object_r:oplus_block_device:s0
/dev/block/platform/11270000.ufshci/by-name/oplusreserve.*                                              u:object_r:oplus_block_device:s0
/dev/block/platform/11270000.ufshci/by-name/oplus_custom                                                u:object_r:oplus_block_device:s0
/dev/block/by-name/oplusreserve.*                                                                       u:object_r:oplus_block_device:s0
/dev/block/platform/soc/11270000.ufshci/by-name/reserve.*                                               u:object_r:oplus_block_device:s0
/dev/block/platform/soc/11270000.ufshci/by-name/oplusreserve.*                                          u:object_r:oplus_block_device:s0
/dev/block/platform/soc/11270000.ufshci/by-name/oplus_custom                                            u:object_r:oplus_block_device:s0
/dev/block/by-name/cdt_engineering(_[ab])?                                                              u:object_r:oplus_block_device:s0
/dev/block/bootdevice/by-name/oplusreserve*                                                             u:object_r:oplus_block_device:s0
/dev/block/platform/bootdevice/by-name/reserve.*                                                        u:object_r:oplus_block_device:s0
/dev/block/platform/bootdevice/by-name/oplusreserve.*                                                   u:object_r:oplus_block_device:s0
/dev/block/sdc3                                                                                         u:object_r:oplus_block_device:s0
/dev/block/sdb3                                                                                         u:object_r:oplus_block_device:s0
/dev/block/platform/bootdevice/by-name/oplus_custom                                                     u:object_r:oplus_block_device:s0
/dev/block/sdc7                                                                                         u:object_r:oplus_block_device:s0
/dev/block/sdc16                                                                                        u:object_r:oplus_block_device:s0
/mnt/vendor/persist/engineermode(/.*)?                                                                  u:object_r:persist_engineer_file:s0
/storage/persist/engineermode(/.*)?                                                                     u:object_r:persist_engineer_file:s0

# Fingerprint
/(odm|vendor/odm)/bin/hw/vendor\.oplus\.hardware\.biometrics\.fingerprint@2\.1-service                  u:object_r:hal_fingerprint_default_exec:s0
/(vendor|system/vendor)/bin/hw/android\.hardware\.biometrics\.fingerprint@2\.3-service\.mt6893           u:object_r:hal_fingerprint_default_exec:s0
/mnt/vendor/persist/fingerprint/silead(/.*)?                                                            u:object_r:oplus_fingerprint_file:s0
/mnt/vendor/persist/fingerprint/jiiov(/.*)?	                                                        u:object_r:factorytestreport_vendor_data_file:s0
/data/vendor/optical_fingerprint(/.*)?                                                                  u:object_r:oplus_fingerprint_file:s0
/mnt/vendor/persist/fingerprint(/.*)?                                                                   u:object_r:oplus_fingerprint_file:s0
/data/vendor/fingerprint(/.*)?                                                                          u:object_r:oplus_fingerprint_file:s0
/data/vendor/silead(/.*)?                                                                               u:object_r:oplus_fingerprint_file:s0
/data/gf_data(/.*)?		                                                                        u:object_r:fingerprintd_data_file:s0

# Latency
/dev/cpu_dma_latency                                                                                    u:object_r:latency_device:s0

# Firmwares
/odm/vendor/firmware(/.*)?                                                                              u:object_r:vendor_firmware_file:s0
/odm/firmware(/.*)?                                                                                     u:object_r:vendor_firmware_file:s0

# ORMS Hal
/(vendor|odm)/bin/hw/vendor\.oplus\.hardware\.orms\.ormsHalService@1\.0-service                         u:object_r:oplus_hal_ormsHal_exec:s0
/(vendor|odm|vendor/odm)/bin/hw/vendor.oplus.hardware.ormsHalService-aidl-service                       u:object_r:oplus_orms_aidl_service_exec:s0

# Performance
/(odm|vendor/odm)/bin/hw/vendor\.oplus\.hardware\.performance@1\.0-service                              u:object_r:hal_performance_oplus_exec:s0

# Touch
/(vendor|system/vendor)/bin/hw/vendor\.lineage\.touch@[0-9]\.[0-9]-service\.oplus                       u:object_r:hal_lineage_touch_default_exec:s0

# Perfmgr
/(vendor|system/vendor)/bin/hw/android\.hardware\.power-service\.pixel-libperfmgr                       u:object_r:hal_power_default_exec:s0

# Device nodes for missing functionality
/dev/ccci2_tty2                                                                                         u:object_r:ccci_tty_device:s0
/dev/ccci_imsdc                                                                                         u:object_r:ccci_imsdc_device:s0
/dev/snd/controlC0                                                                                      u:object_r:snd_device:s0
/dev/snd/pcmC0D0c                                                                                       u:object_r:snd_device:s0
/dev/snd/pcmC0D0p                                                                                       u:object_r:snd_device:s0
/dev/snd/timer                                                                                          u:object_r:snd_device:s0

