# Cloning from the 'ossi' remote
git clone -b voltage https://github.com/soulspark666/android_device_oplus_mt6893-common.git device/oplus/mt6893-common
git clone -b lineage-22.1 https://github.com/soulspark666/proprietary_vendor_oplus_mt6893-common.git vendor/oplus/mt6893-common
git clone -b kernelsu-next https://github.com/soulspark666/android_kernel_oplus_mt6893.git kernel/oplus/mt6893 --recurse-submodules
git clone -b voltage https://github.com/soulspark666/android_device_oplus_cupida device/oplus/cupida
git clone -b lineage-22.1 https://github.com/oplus-ossi-development/proprietary_vendor_oplus_cupida vendor/oplus/cupida
git clone -b lineage-21 https://github.com/oplus-ossi-development/android_hardware_mediatek hardware/mediatek
git clone -b lineage-21 https://github.com/oplus-ossi-development/android_hardware_mediatek_wlan hardware/mediatek/wlan
git clone -b master --depth 1 https://gitlab.com/projectelixiros/android_prebuilts_clang_host_linux-x86_clang-r468909b prebuilts/clang/host/linux-x86/clang-r468909b
git clone -b lineage-21 https://github.com/oplus-ossi-development/android_device_mediatek_sepolicy_vndr device/mediatek/sepolicy_vndr

add cherry-pick
frameworks/base
git cherry-pick 077b413c5c4988b71e0daf2d8167353935008344
git cherry-pick b25e60e0f96373aefbe3978077d98bfe87c68b72

feat: enhance max charging calculation with configurable dividers



    public static int mCurrentDivider = 1000;
    public static int mVoltageDivider = 1000;
    
    public static BatteryStatus create(Context context, boolean incompatibleCharger) {
        final Intent batteryChangedIntent = BatteryUtils.getBatteryIntent(context);
        mCurrentDivider = context.getResources().getInteger(R.integer.config_currentInfoDivider);
        mVoltageDivider = context.getResources().getInteger(R.integer.config_voltageInfoDivider);
        return batteryChangedIntent == null
                ? null : new BatteryStatus(batteryChangedIntent, incompatibleCharger);
    }


    private static float calculateMaxChargingMicroWatt(int maxChargingMicroAmp,
            int maxChargingMicroVolt) {
        if (maxChargingMicroVolt <= 0) {
            maxChargingMicroVolt = DEFAULT_CHARGING_VOLTAGE_MICRO_VOLT;
        }
                
        if (maxChargingMicroAmp > 0) {
            // Calculating µW = mA * mV
            return (float) (maxChargingMicroAmp * mCurrentDivider * 0.001 * maxChargingMicroVolt * mVoltageDivider
                    * 0.001);
        } else {
            return -1;
        }
    }




grep -rnw '/path/to/somewhere/' -e 'config_hasDashCharger'


-r or -R is recursive,
-n is line number, and
-w stands for match the whole word.
-l (lower-case L) can be added to just give the file name of matching files.
-e is the pattern used during the search



git remote add origin https://<EMAIL>/himanshu0779/Himanshu_AzueExp/_git/ossi.git
git push -u origin --all

animesh

Azure:
9jrVUecX5ejLX586gmKRqSMWSSCGNAh8qPMM9vTiTBgUVVzcv7gtJQQJ99BAACAAAAAWrPUXAAASAZDOhAwz

OpenRouter:
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

Github:
****************************************

Gemeni:
AIzaSyAnOmfTRF2tzpJYcBK3ub9LyUPmbbtFkAQ