<?xml version="1.0" encoding="utf-8"?>
<!--
-->
<MediaCodecs>
    <Decoders>
        <Video name="c2.mtk.mpeg4.decoder"
               type="video/mp4v-es"
               driverIntf="v4l2"
               maxConcurrentInstances="15" />
        <Video name="c2.mtk.h263.decoder"
               type="video/3gpp"
               driverIntf="v4l2"
               maxConcurrentInstances="15" />
        <Video name="c2.mtk.avc.decoder"
               type="video/avc"
               driverIntf="v4l2"
               maxConcurrentInstances="15"
               metaBufferSize="4096" />
        <Video name="c2.mtk.avc.decoder.secure"
               type="video/avc"
               driverIntf="v4l2"
               maxConcurrentInstances="1" />
        <Video name="c2.mtk.hevc.decoder"
               type="video/hevc"
               driverIntf="v4l2"
               maxConcurrentInstances="15"
               metaBufferSize="4096" />
        <Video name="c2.mtk.hevc.decoder.secure"
               type="video/hevc"
               driverIntf="v4l2"
               maxConcurrentInstances="1"
               metaBufferSize="4096" />
        <Video name="c2.mtk.heif.decoder"
               type="image/vnd.android.heic"
               driverIntf="v4l2"
               maxConcurrentInstances="16" />
        <Video name="c2.mtk.vpx.decoder"
               type="video/x-vnd.on2.vp8"
               driverIntf="v4l2"
               maxConcurrentInstances="15" />
        <Video name="c2.mtk.vp9.decoder"
               type="video/x-vnd.on2.vp9"
               driverIntf="v4l2"
               maxConcurrentInstances="15" />
        <Video name="c2.mtk.vp9.decoder.secure"
               type="video/x-vnd.on2.vp9"
               driverIntf="v4l2"
               maxConcurrentInstances="1" />
        <Video name="c2.mtk.av1.decoder"
               type="video/av01"
               driverIntf="v4l2"
               maxConcurrentInstances="15" />
        <Video name="c2.mtk.av1.decoder.secure"
               type="video/av01"
               driverIntf="v4l2"
               maxConcurrentInstances="1" />
        <Video name="c2.mtk.mpeg2.decoder"
               type="video/mpeg2"
               driverIntf="v4l2"
               maxConcurrentInstances="15" />
        <Video name="c2.mtk.vc1.decoder"
               type="video/x-ms-wmv"
               driverIntf="v4l2"
               maxConcurrentInstances="15" />
    </Decoders>
    <Encoders>
        <Video name="c2.mtk.h263.encoder"
               type="video/3gpp"
               driverIntf="v4l2"
               canSwapWidthHeight="true"
               maxConcurrentInstances="10" />
        <Video name="c2.mtk.mpeg4.encoder"
               type="video/mp4v-es"
               driverIntf="v4l2"
               canSwapWidthHeight="true"
               maxConcurrentInstances="10" />
        <Video name="c2.mtk.avc.encoder"
               type="video/avc"
               driverIntf="v4l2"
               canSwapWidthHeight="true"
               maxConcurrentInstances="10" />
        <Video name="c2.mtk.avc.encoder.secure"
               type="video/avc"
               driverIntf="v4l2"
               maxConcurrentInstances="10"
               canSwapWidthHeight="true" />
        <Video name="c2.mtk.hevc.encoder"
               type="video/hevc"
               driverIntf="v4l2"
               canSwapWidthHeight="true"
               maxConcurrentInstances="10" />
        <Video name="c2.mtk.heif.encoder"
               type="image/vnd.android.heic"
               driverIntf="v4l2"
               maxConcurrentInstances="2" />
    </Encoders>
</MediaCodecs>
