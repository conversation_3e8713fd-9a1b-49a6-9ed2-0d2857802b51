05-25 09:15:23.715   572   572 I auditd  : SELinux: Loaded service context from:
05-25 09:15:23.715   572   572 I auditd  : 		/system/etc/selinux/plat_service_contexts
05-25 09:15:23.715   572   572 I auditd  : 		/system_ext/etc/selinux/system_ext_service_contexts
05-25 09:15:23.715   572   572 I auditd  : 		/product/etc/selinux/product_service_contexts
05-25 09:15:23.715   572   572 I auditd  : 		/vendor/etc/selinux/vendor_service_contexts
05-25 09:15:23.715   572   572 I auditd  : 		/odm/etc/selinux/odm_service_contexts
05-25 09:15:23.716   573   573 I hwservicemanager: getFrameworkHalManifest: Reading VINTF information.
05-25 09:15:23.724   573   573 I hwservicemanager: Loaded APEX Infos from /bootstrap-apex/apex-info-list.xml
05-25 09:15:23.724   573   573 I hwservicemanager: getDeviceHalManifest: Reading VINTF information.
05-25 09:15:23.748   573   573 I hwservicemanager: Loaded APEX Infos from /bootstrap-apex/apex-info-list.xml
05-25 09:15:23.751   573   573 I hwservicemanager: getDeviceHalManifest: Successfully processed VINTF information
05-25 09:15:23.751   573   573 I hwservicemanager: getFrameworkHalManifest: Successfully processed VINTF information
05-25 09:15:23.757   573   573 I hwservicemanager: hwservicemanager is ready now.
05-25 09:15:23.036     1     1 I auditd  : type=1107 audit(0.0:4): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_cam_stereo_camera_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-25 09:15:23.036     1     1 W /system/bin/init: type=1107 audit(0.0:4): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_cam_stereo_camera_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-25 09:15:23.036     1     1 I auditd  : type=1107 audit(0.0:5): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_cam_dualzoom_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-25 09:15:23.036     1     1 W /system/bin/init: type=1107 audit(0.0:5): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_cam_dualzoom_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-25 09:15:23.040     1     1 I auditd  : type=1107 audit(0.0:6): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_key_manager_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-25 09:15:23.040     1     1 W /system/bin/init: type=1107 audit(0.0:6): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.mtk_key_manager_support pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:default_prop:s0 tclass=property_service permissive=0'
05-25 09:15:23.040     1     1 I auditd  : type=1107 audit(0.0:7): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.build.fingerprint pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:fingerprint_prop:s0 tclass=property_service permissive=0'
05-25 09:15:23.040     1     1 W /system/bin/init: type=1107 audit(0.0:7): uid=0 auid=4294967295 ses=4294967295 subj=u:r:init:s0 msg='avc:  denied  { set } for property=ro.build.fingerprint pid=1 uid=0 gid=0 scontext=u:r:vendor_init:s0 tcontext=u:object_r:fingerprint_prop:s0 tclass=property_service permissive=0'
05-25 09:15:23.886     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvdata" to "5": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-25 09:15:23.891     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvdata" to "5": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-25 09:15:23.898     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvcfg" to "5": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-25 09:15:23.904     1     1 W libc    : Unable to set property "ro.boottime.init.fsck.nvcfg" to "5": PROP_ERROR_READ_ONLY_PROPERTY (0xb)
05-25 09:15:24.007   632   632 I HidlServiceManagement: Registered android.system.suspend@1.0::ISystemSuspend/default
05-25 09:15:24.007   632   632 I HidlServiceManagement: Removing namespace from process name android.system.suspend-service to suspend-service.
05-25 09:15:24.013   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.boot@1.0::IBootControl/default in either framework or device VINTF manifest.
05-25 09:15:24.019   634   634 I <EMAIL>: Trustonic Keymaster 4.1 Service starts
05-25 09:15:24.025   634   634 I HidlServiceManagement: Registered android.hardware.keymaster@4.1::IKeymasterDevice/default
05-25 09:15:24.026   634   634 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-25 09:15:24.026   634   634 I <EMAIL>: Trustonic Keymaster 4.1 Service registered
05-25 09:15:24.037   633   633 I keystore2: system/security/keystore2/src/keystore2_main.rs:154 - Successfully registered Keystore 2.0 service.
05-25 09:15:24.196   572   641 W libc    : Unable to set property "ctl.interface_start" to "aidl/android.system.keystore2.IKeystoreService/default": PROP_ERROR_HANDLE_CONTROL_MESSAGE (0x20)
05-25 09:15:24.215   652   652 I libperfmgr: Pixel Power HAL AIDL Service with Extension is starting with config: /vendor/etc/powerhint.json
05-25 09:15:24.218   652   652 I libperfmgr: Failed to read Node[18]'s ResetOnInit, set to 'false'
05-25 09:15:24.218   652   652 I libperfmgr: Failed to read Node[19]'s ResetOnInit, set to 'false'
05-25 09:15:24.218   652   652 I libperfmgr: Failed to read Node[20]'s ResetOnInit, set to 'false'
05-25 09:15:24.218   652   652 I libperfmgr: Failed to read Node[21]'s ResetOnInit, set to 'false'
05-25 09:15:24.219   652   652 I libperfmgr: PowerHint AUDIO_STREAMING_LOW_LATENCY has 3 node actions, and 0 hint actions parsed
05-25 09:15:24.220   652   652 I libperfmgr: Initialized HintManager from JSON config: /vendor/etc/powerhint.json
05-25 09:15:24.221   652   652 I powerhal-libperfmgr: Initialize PowerHAL
05-25 09:15:24.222   652   652 I powerhal-libperfmgr: Pixel Power HAL AIDL Service with Extension is started.
05-25 09:15:24.224   590   590 I vold    : fscrypt_initialize_systemwide_keys
05-25 09:15:24.273   590   590 I incfs   : Initial API level of the device: 30
05-25 09:15:24.306   633   633 E keystore2:     1: system/security/keystore2/src/globals.rs:264: Trying to get Legacy wrapper. Attempt to get keystore compat service for security level r#STRONGBOX
05-25 09:15:24.306   590   590 E vold    : keystore2 Keystore earlyBootEnded returned service specific error: -68
05-25 09:15:24.310   663   663 I tombstoned: tombstoned successfully initialized
05-25 09:15:24.617   804   804 I derive_sdk: extension ad_services version is 15
05-25 09:15:24.809   808   808 I derive_classpath: ReadClasspathFragment /apex/com.android.adservices/etc/classpaths/bootclasspath.pb
05-25 09:15:24.810   808   808 I derive_classpath: ReadClasspathFragment /apex/com.android.btservices/etc/classpaths/bootclasspath.pb
05-25 09:15:24.813   808   808 I derive_classpath: ReadClasspathFragment /apex/com.android.nfcservices/etc/classpaths/bootclasspath.pb
05-25 09:15:24.814   808   808 I derive_classpath: ReadClasspathFragment /apex/com.android.permission/etc/classpaths/bootclasspath.pb
05-25 09:15:24.817   808   808 I derive_classpath: ReadClasspathFragment /apex/com.android.adservices/etc/classpaths/systemserverclasspath.pb
05-25 09:15:24.818   808   808 I derive_classpath: ReadClasspathFragment /apex/com.android.btservices/etc/classpaths/systemserverclasspath.pb
05-25 09:15:24.820   808   808 I derive_classpath: ReadClasspathFragment /apex/com.android.permission/etc/classpaths/systemserverclasspath.pb
05-25 09:15:24.822   808   808 I derive_classpath: export BOOTCLASSPATH /apex/com.android.art/javalib/core-oj.jar:/apex/com.android.art/javalib/core-libart.jar:/apex/com.android.art/javalib/okhttp.jar:/apex/com.android.art/javalib/bouncycastle.jar:/apex/com.android.art/javalib/apache-xml.jar:/system/framework/framework.jar:/system/framework/framework-graphics.jar:/system/framework/framework-location.jar:/system/framework/framework-connectivity-b.jar:/system/framework/ext.jar:/system/framework/telephony-common.jar:/system/framework/voip-common.jar:/system/framework/ims-common.jar:/system/framework/framework-platformcrashrecovery.jar:/system/framework/framework-ondeviceintelligence-platform.jar:/system/framework/mediatek-common.jar:/system/framework/mediatek-framework.jar:/system/framework/mediatek-ims-base.jar:/system/framework/mediatek-ims-common.jar:/system/framework/mediatek-telecom-common.jar:/system/framework/mediatek-telephony-base.jar:/system/framework/mediatek-telephony-common.jar:/apex/com.android.i18n/javalib/core-icu4j.jar:/apex/com.android.adservices/javalib/framework-adservices.jar:/apex/com.android.adservices/javalib/framework-sdksandbox.jar:/apex/com.android.appsearch/javalib/framework-appsearch.jar:/apex/com.android.btservices/javalib/framework-bluetooth.jar:/apex/com.android.configinfrastructure/javalib/framework-configinfrastructure.jar:/apex/com.android.conscrypt/javalib/conscrypt.jar:/apex/com.android.devicelock/javalib/framework-devicelock.jar:/apex/com.android.healthfitness/javalib/framework-healthfitness.jar:/apex/com.android.ipsec/javalib/android.net.ipsec.ike.jar:/apex/com.android.media/javalib/updatable-media.jar:/apex/com.android.mediaprovider/javalib/framework-mediaprovider.jar:/apex/com.android.mediaprovider/javalib/framework-pdf.jar:/apex/com.android.mediaprovider/javalib/framework-pdf-v.jar:/apex/com.android.mediaprovider/javalib/framework-photopicker.jar:/apex/com.android.nfcservices/javalib/framework-nfc.jar:/apex/com.android.ondevicepersonalization/javalib/framework-ondevicepersonalization.jar:/apex/com.android.os.statsd/javalib/framework-statsd.jar:/apex/com.android.permission/javalib/framework-permission.jar:/apex/com.android.permission/javalib/framework-permission-s.jar:/apex/com.android.profiling/javalib/framework-profiling.jar:/apex/com.android.scheduling/javalib/framework-scheduling.jar:/apex/com.android.sdkext/javalib/framework-sdkextensions.jar:/apex/com.android.tethering/javalib/framework-connectivity.jar:/apex/com.android.tethering/javalib/framework-connectivity-t.jar:/apex/com.android.tethering/javalib/framework-tethering.jar:/apex/com.android.uwb/javalib/framework-uwb.jar:/apex/com.android.virt/javalib/framework-virtualization.jar:/apex/com.android.wifi/javalib/framework-wifi.jar
05-25 09:15:24.822   808   808 I derive_classpath: export SYSTEMSERVERCLASSPATH /system/framework/com.android.location.provider.jar:/system/framework/services.jar:/apex/com.android.adservices/javalib/service-adservices.jar:/apex/com.android.adservices/javalib/service-sdksandbox.jar:/apex/com.android.appsearch/javalib/service-appsearch.jar:/
05-25 09:15:24.852   809   809 I art_boot: Property persist.device_config.runtime_native_boot.useartservice not set
05-25 09:15:24.878   810   810 W odsign  : Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.system.keystore2.IKeystoreService/default
05-25 09:15:24.885   810   810 I odsign  : Initialized Keystore key.
05-25 09:15:25.532   821   821 I netdClient: Skipping libnetd_client init since *we* are netd
05-25 09:15:25.556   573   573 I hwservicemanager: getFrameworkHalManifest: Reloading VINTF information.
05-25 09:15:25.556   573   573 I hwservicemanager: getFrameworkHalManifest: Reading VINTF information.
05-25 09:15:25.559   821   821 I NetdUpdatable: libnetd_updatable_init: Initializing
05-25 09:15:25.561   573   573 I hwservicemanager: Loaded APEX Infos from /apex/apex-info-list.xml
05-25 09:15:25.561   573   573 I hwservicemanager: getDeviceHalManifest: Reloading VINTF information.
05-25 09:15:25.561   573   573 I hwservicemanager: getDeviceHalManifest: Reading VINTF information.
05-25 09:15:25.570   821   821 I NetdUpdatable: initMaps successfully
05-25 09:15:25.570   821   821 I netd    : libnetd_updatable_init success
05-25 09:15:25.578   573   573 I hwservicemanager: Loaded APEX Infos from /apex/apex-info-list.xml
05-25 09:15:25.581   573   573 I hwservicemanager: getDeviceHalManifest: Successfully processed VINTF information
05-25 09:15:25.581   573   573 I hwservicemanager: getFrameworkHalManifest: Successfully processed VINTF information
05-25 09:15:25.583   838   838 E android.hardware.gnss-service.mediatek: safe_sendto: safe_sendto() sendto() failed path=[mtk_hal2mnl] ret=-1 reason=[Connection refused]111
05-25 09:15:25.583   838   838 E android.hardware.gnss-service.mediatek: gps_device__get_gps_interface: hal2mnl_hal_reboot failed because of safe_sendto fail ,strerror:Connection refused 
05-25 09:15:25.587   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio@7.1::IDevicesFactory/default in either framework or device VINTF manifest.
05-25 09:15:25.588   833   833 I mtk.hal.bt@1.0-impl: Init IBluetoothHCI
05-25 09:15:25.593   832   832 E LegacySupport: Could not get passthrough implementation for android.hardware.audio@7.1::IDevicesFactory/default.
05-25 09:15:25.604   847   847 I android.hardware.usb@1.3-service-mediatekv2: UsbGadget
05-25 09:15:25.604   851   851 I vtservice_hidl: [VT][SRV]before VTService_HiDL_instantiate
05-25 09:15:25.611   838   838 E android.hardware.gnss-service.mediatek: safe_sendto: safe_sendto() sendto() failed path=[mtk_hal2mnl] ret=-1 reason=[Connection refused]111
05-25 09:15:25.611   838   838 E android.hardware.gnss-service.mediatek: gps_device__get_gps_interface: hal2mnl_hal_reboot failed because of safe_sendto fail ,strerror:Connection refused 
05-25 09:15:25.614   834   834 I HidlServiceManagement: Registered android.hardware.cas@1.2::IMediaCasService/default
05-25 09:15:25.614   834   834 I HidlServiceManagement: Removing namespace from process name android.hardware.cas@1.2-service to cas@1.2-service.
05-25 09:15:25.617   831   831 I HidlServiceManagement: Registered android.hidl.allocator@1.0::IAllocator/ashmem
05-25 09:15:25.617   831   831 I HidlServiceManagement: Removing namespace from process name android.hidl.allocator@1.0-service to allocator@1.0-service.
05-25 09:15:25.619   858   858 W <EMAIL>: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.hardware.power.IPower/default
05-25 09:15:25.620   858   858 I <EMAIL>: Connected to power AIDL HAL
05-25 09:15:25.620   841   841 I android.hardware.health@2.1-service: default instance initializing with healthd_config...
05-25 09:15:25.627   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_agps
05-25 09:15:25.631   836   836 I HidlServiceManagement: Registered android.hardware.drm@1.4::IDrmFactory/widevine
05-25 09:15:25.631   836   836 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-25 09:15:25.633   847   847 I HidlServiceManagement: Registered android.hardware.usb@1.3::IUsb/default
05-25 09:15:25.633   847   847 I HidlServiceManagement: Removing namespace from process name android.hardware.usb@1.3-service-mediatekv2 to usb@1.3-service-mediatekv2.
05-25 09:15:25.635   867   867 E vendor.oplus.hardware.charger@1.0-service: notifyScreenStatus: 0
05-25 09:15:25.642   867   867 E vendor.oplus.hardware.charger@1.0-service: setChgStatusToBcc: 0
05-25 09:15:25.644   821   821 I netd    : Initializing RouteController: 3212us
05-25 09:15:25.648   844   844 W <EMAIL>: ThermalHelper:tz_map_version 1
05-25 09:15:25.651   833   833 I HidlServiceManagement: Registered android.hardware.bluetooth@1.1::IBluetoothHci/default
05-25 09:15:25.651   833   833 I HidlServiceManagement: Removing namespace from process name android.hardware.bluetooth@1.1-service-mediatek to bluetooth@1.1-service-mediatek.
05-25 09:15:25.652   875   875 W MTK_FG_FUEL: fd < 0, init first!
05-25 09:15:25.652   875   875 W MTK_FG_FUEL: init failed, return!
05-25 09:15:25.652   875   875 W MTK_FG  : fd < 0, init first!
05-25 09:15:25.652   875   875 E MTK_FG  : init failed, return!
05-25 09:15:25.652   875   875 W MTK_FG  : fd < 0, init first!
05-25 09:15:25.652   875   875 E MTK_FG  : init failed, return!
05-25 09:15:25.652   875   875 W MTK_FG  : fd < 0, init first!
05-25 09:15:25.652   875   875 E MTK_FG  : init failed, return!
05-25 09:15:25.658   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkpower@1.2::IMtkPower/default
05-25 09:15:25.659   858   858 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-25 09:15:25.663   841   841 I HidlServiceManagement: Registered android.hardware.health@2.1::IHealth/default
05-25 09:15:25.664   841   841 I HidlServiceManagement: Removing namespace from process name android.hardware.health@2.1-service to health@2.1-service.
05-25 09:15:25.664   841   841 I android.hardware.health@2.1-service: default: Hal init done
05-25 09:15:25.666   844   844 I <EMAIL>: ThermalWatcherThread started
05-25 09:15:25.668   840   840 W hwcomposer: [DRMDEV] 0xcccccccc[64] property[MSYNC2_0_ENABLE] does not do initialize  
05-25 09:15:25.668   840   840 W hwcomposer: [DRMDEV] 0xcccccccc[64] property[SKIP_CONFIG] does not do initialize  
05-25 09:15:25.668   840   840 W hwcomposer: [DRMDEV] failed to initialize crtc[0]: 64  
05-25 09:15:25.668   840   840 W hwcomposer: [DRMDEV] 0xcccccccc[88] property[MSYNC2_0_ENABLE] does not do initialize  
05-25 09:15:25.668   840   840 W hwcomposer: [DRMDEV] 0xcccccccc[88] property[SKIP_CONFIG] does not do initialize  
05-25 09:15:25.668   840   840 W hwcomposer: [DRMDEV] failed to initialize crtc[1]: 88  
05-25 09:15:25.668   840   840 W hwcomposer: [DRMDEV] 0xcccccccc[99] property[MSYNC2_0_ENABLE] does not do initialize  
05-25 09:15:25.668   840   840 W hwcomposer: [DRMDEV] 0xcccccccc[99] property[SKIP_CONFIG] does not do initialize  
05-25 09:15:25.668   840   840 W hwcomposer: [DRMDEV] failed to initialize crtc[2]: 99  
05-25 09:15:25.668   840   840 W hwcomposer: [DRMDEV] failed to initialize all crtc: -19  
05-25 09:15:25.669   840   840 E hwcomposer: [DRMDEV] failed to initialize drm resource  
05-25 09:15:25.673   840   840 I hwcomposer: [PqXmlParser] [PQ_SERVICE] mDeviceVersion = AMS644VA04_MTK04_20615  
05-25 09:15:25.673   840   840 I hwcomposer: [PqXmlParser] [PQ_SERVICE] mDeviceManufacture = samsung1024  
05-25 09:15:25.673   840   840 I hwcomposer: [PqXmlParser] [PQ_SERVICE] prjName:20662  
05-25 09:15:25.673   840   840 I hwcomposer: [PqXmlParser] init: failed to open file: /vendor/etc/cust_pq.xml  
05-25 09:15:25.673   882   882 I credstore: Registered binder service
05-25 09:15:25.676   838   838 I HidlServiceManagement: Registered android.hardware.gnss@2.1::IGnss/default
05-25 09:15:25.676   838   838 I HidlServiceManagement: Removing namespace from process name android.hardware.gnss-service.mediatek to gnss-service.mediatek.
05-25 09:15:25.680   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_wlan
05-25 09:15:25.681   862   862 I HidlServiceManagement: Registered vendor.trustonic.tee@1.1::ITee/default
05-25 09:15:25.682   862   862 I HidlServiceManagement: Removing namespace from process name vendor.trustonic.tee@1.1-service to tee@1.1-service.
05-25 09:15:25.682   821   821 I netd    : Initializing XfrmController: 37851us
05-25 09:15:25.690   821   821 I resolv  : resolv_init: Initializing resolver
05-25 09:15:25.690   901   901 E ccci_mdinit: (1):main, fail to open ccci_dump, err(Permission denied)
05-25 09:15:25.691   866   866 I HidlServiceManagement: Registered vendor.oplus.hardware.cammidasservice@1.0::IMIDASService/default
05-25 09:15:25.691   843   843 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/acc_cali.json) open failed: -2 (No such file or directory)
05-25 09:15:25.691   866   866 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.cammidasservice@1.0-service to cammidasservice@1.0-service.
05-25 09:15:25.692   866   866 I vendor.oplus.hardware.cammidasservice@1.0-service: midasservice register successfully
05-25 09:15:25.692   901   901 I ccci_mdinit: (1):[main] drv_ver: 2
05-25 09:15:25.693   901   901 I ccci_mdinit: (1):[main] ccci_create_md_status_listen_thread
05-25 09:15:25.693   836   836 I HidlServiceManagement: Registered android.hardware.drm@1.4::ICryptoFactory/widevine
05-25 09:15:25.693   901   901 I ccci_mdinit: (1):md_init ver:2.30, sub:0, 1
05-25 09:15:25.693   901   901 I NVRAM   : MD1 set status: vendor.mtk.md1.status=init 
05-25 09:15:25.693   836   836 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-25 09:15:25.693   901   901 I ccci_mdinit: (1):MD0 set status: mtk.md0.status=init 
05-25 09:15:25.693   901   901 I NVRAM   : MD0 set status: mtk.md0.status=init 
05-25 09:15:25.693   901   901 E ccci_mdinit: (1):get property fail: ro.vendor.mtk_mipc_support
05-25 09:15:25.693   901   901 I ccci_mdinit: (1):service names: [init.svc.vendor.gsm0710muxd][init.svc.vendor.ril-daemon-mtk][init.svc.emdlogger1][init.svc.vendor.ccci_fsd]
05-25 09:15:25.693   901   901 I ccci_mdinit: (1):md_img_exist 0 0 0 0
05-25 09:15:25.694   847   847 I HidlServiceManagement: Registered android.hardware.usb.gadget@1.1::IUsbGadget/default
05-25 09:15:25.694   901   901 I ccci_mdinit: (1):MD1 set status: mtk.md1.status=reset 
05-25 09:15:25.694   901   901 E ccci_mdinit: (1):[get_mdini_killed_state] error: get mdinit killed: 25(-1)
05-25 09:15:25.694   847   847 I android.hardware.usb@1.3-service-mediatekv2: USB HAL Ready.
05-25 09:15:25.694   901   901 I ccci_mdinit: (1):MD_LOG_MEMDUMP_WAIT:ro.vendor.md_log_memdump_wait not exist, using default value
05-25 09:15:25.694   901   901 I ccci_mdinit: (1):MD_LOG_MEMDUMP_WAIT value: 0
05-25 09:15:25.694   901   901 I ccci_mdinit: (1):md0: mdl_mode=0
05-25 09:15:25.694   901   901 I ccci_mdinit: (1):check_nvram_ready(), property_get("vendor.service.nvram_init") = , read_nvram_ready_retry = 1
05-25 09:15:25.695   843   843 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/gyro_cali.json) open failed: -2 (No such file or directory)
05-25 09:15:25.696   873   873 I HidlServiceManagement: Registered vendor.oplus.hardware.performance@1.0::IPerformance/default
05-25 09:15:25.696   873   873 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.performance@1.0-service to performance@1.0-service.
05-25 09:15:25.700   867   867 I HidlServiceManagement: Registered vendor.oplus.hardware.charger@1.0::ICharger/default
05-25 09:15:25.700   867   867 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.charger@1.0-service to charger@1.0-service.
05-25 09:15:25.702   867   867 E vendor.oplus.hardware.charger@1.0-service: ERR:Failed to get bms heating config file
05-25 09:15:25.702   867   867 E vendor.oplus.hardware.charger@1.0-service: can't parse config, rc=-1
05-25 09:15:25.703   851   851 I HidlServiceManagement: Registered vendor.mediatek.hardware.videotelephony@1.0::IVideoTelephony/default
05-25 09:15:25.705   821   821 I netd    : Registering NetdNativeService: 580us
05-25 09:15:25.706   821   821 I netd    : Registering MDnsService: 712us
05-25 09:15:25.706   858   858 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkpower@1.2::IMtkPerf/default
05-25 09:15:25.706   858   858 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-25 09:15:25.709   869   869 I HidlServiceManagement: Registered vendor.oplus.hardware.oplusSensor@1.0::ISensorFeature/default
05-25 09:15:25.709   869   869 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.oplusSensor@1.0-service to oplusSensor@1.0-service.
05-25 09:15:25.711   573   573 I hwservicemanager: Since vendor.mediatek.hardware.pq@2.14::IPictureQuality/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-25 09:15:25.711   840   840 E hwcomposer: [IPqDevice] Can't get PQ service tried (0) times  
05-25 09:15:25.712   839   839 I HidlServiceManagement: Registered android.hardware.graphics.allocator@4.0::IAllocator/default
05-25 09:15:25.712   839   839 I HidlServiceManagement: Removing namespace from process name android.hardware.graphics.allocator@4.0-service-mediatek to allocator@4.0-service-mediatek.
05-25 09:15:25.713   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_bt
05-25 09:15:25.715   862   862 I HidlServiceManagement: Registered vendor.trustonic.tee.tui@1.0::ITui/default
05-25 09:15:25.717   844   844 I HidlServiceManagement: Registered android.hardware.thermal@2.0::IThermal/default
05-25 09:15:25.718   844   844 I HidlServiceManagement: Removing namespace from <NAME_EMAIL> to <EMAIL>.
05-25 09:15:25.718   843   843 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/als_cali.json) open failed: -2 (No such file or directory)
05-25 09:15:25.718   843   843 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/ps_cali.json) open failed: -2 (No such file or directory)
05-25 09:15:25.718   843   843 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/sar_cali.json) open failed: -2 (No such file or directory)
05-25 09:15:25.718   843   843 E sensors-hal-SensorSaved: settings file(/mnt/vendor/nvcfg/sensor/ois_cali.json) open failed: -2 (No such file or directory)
05-25 09:15:25.719   859   859 I HidlServiceManagement: Registered vendor.mediatek.hardware.nvram@1.1::INvram/default
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: failed to get path of fd 3: No such file or directory
05-25 09:15:25.720   859   859 I HidlServiceManagement: Removing namespace from process name vendor.mediatek.hardware.nvram@1.1-service to nvram@1.1-service.
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(15): previous definition here
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(38): previous definition here
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(69): previous definition here
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(70): previous definition here
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(26): previous definition here
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(37): previous definition here
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(45): previous definition here
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(46): previous definition here
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(53): previous definition here
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(56): previous definition here
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(59): previous definition here
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(60): previous definition here
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(61): previous definition here
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(74): previous definition here
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(15): previous definition here
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(88): previous definition here
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(89): previous definition here
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(90): previous definition here
05-25 09:15:25.720   842   842 W android.hardware.media.c2@1.2-mediatek: libminijail[842]: compile_file: <fd>(91): previous definition here
05-25 09:15:25.725   652   655 W powerhal-libperfmgr: Connecting to PPS daemon failed (No such file or directory)
05-25 09:15:25.726   573   945 I hwservicemanager: Tried to start vendor.mediatek.hardware.pq@2.14::IPictureQuality/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-25 09:15:25.726   832   832 I HidlServiceManagement: Registered android.hardware.audio@7.0::IDevicesFactory/default
05-25 09:15:25.727   832   832 I HidlServiceManagement: Removing namespace from process name android.hardware.audio.service to audio.service.
05-25 09:15:25.727   832   832 I LegacySupport: Registration complete for android.hardware.audio@7.0::IDevicesFactory/default.
05-25 09:15:25.728   821   821 I HidlServiceManagement: Registered android.system.net.netd@1.1::INetd/default
05-25 09:15:25.728   821   821 I netd    : Registering NetdHwService: 22700us
05-25 09:15:25.729   840   840 I HidlServiceManagement: Registered vendor.mediatek.hardware.composer_ext@1.0::IComposerExt/default
05-25 09:15:25.730   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_sensor
05-25 09:15:25.733   840   840 I hwcomposer: [HWC] IComposerExt service registration completed.  
05-25 09:15:25.735   842   842 I HidlServiceManagement: Registered android.hardware.media.c2@1.1::IComponentStore/default
05-25 09:15:25.735   842   842 I HidlServiceManagement: Removing namespace from process name android.hardware.media.c2@1.2-mediatek to c2@1.2-mediatek.
05-25 09:15:25.736   952   952 I gsid    : no DSU: No such file or directory
05-25 09:15:25.736   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_network
05-25 09:15:25.738   843   843 I HidlServiceManagement: Registered android.hardware.sensors@2.0::ISensors/default
05-25 09:15:25.738   843   843 I HidlServiceManagement: Removing namespace from <NAME_EMAIL>6893 to sensors@2.0-service-multihal.mt6893.
05-25 09:15:25.740   840   840 I HidlServiceManagement: Registered android.hardware.graphics.composer@2.3::IComposer/default
05-25 09:15:25.740   840   840 I HidlServiceManagement: Removing namespace from process name android.hardware.graphics.composer@2.3-service to composer@2.3-service.
05-25 09:15:25.740   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_ipaddr
05-25 09:15:25.742   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lppe_socket_lbs
05-25 09:15:25.743   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_framework2agps
05-25 09:15:25.744   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_agps2framework
05-25 09:15:25.745   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mnld2nlputils
05-25 09:15:25.746   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mnld2debugService
05-25 09:15:25.747   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_debugService2mnld
05-25 09:15:25.749   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_meta2mnld
05-25 09:15:25.752   832   832 I HidlServiceManagement: Registered android.hardware.audio.effect@7.0::IEffectsFactory/default
05-25 09:15:25.752   832   832 I LegacySupport: Registration complete for android.hardware.audio.effect@7.0::IEffectsFactory/default.
05-25 09:15:25.753   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_agpsd2debugService
05-25 09:15:25.753   926   926 I TeeMcDaemon: Initialise Secure World [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/MobiCoreDriverDaemon.cpp:260]
05-25 09:15:25.753   926   926 W TeeMcDaemon: Cannot open key SO  (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:445]
05-25 09:15:25.753   926   926 I TeeMcDaemon: Start services [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/MobiCoreDriverDaemon.cpp:325]
05-25 09:15:25.755   903   903 E ccci_mdinit: (3):main, fail to open ccci_dump, err(Permission denied)
05-25 09:15:25.757   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_debugService2agpsd
05-25 09:15:25.759   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/AgpsInterface
05-25 09:15:25.760   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/AgpsDebugInterface
05-25 09:15:25.761   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mnld2mtklogger
05-25 09:15:25.762   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_mtklogger2mnld
05-25 09:15:25.763   856   856 I HidlServiceManagement: Registered vendor.mediatek.hardware.lbs@1.0::ILbs/mtk_lbs_log_v2s
05-25 09:15:25.765   880   880 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-25 09:15:25.766   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio@7.1::IDevicesFactory/default in either framework or device VINTF manifest.
05-25 09:15:25.767   880   880 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-25 09:15:25.767   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-25 09:15:25.783   926   962 W TeeMcDaemon: Cannot open trustlet /data/vendor/mcRegistry/07050501000000000000000000000020.tlbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.789   926   962 W TeeMcDaemon: Cannot open trustlet /data/vendor/mcRegistry/08050500000000000000000000000000.tlbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.791   837   837 I HidlServiceManagement: Registered android.hardware.gatekeeper@1.0::IGatekeeper/default
05-25 09:15:25.791   837   837 I HidlServiceManagement: Removing namespace from process name android.hardware.gatekeeper@1.0-service to gatekeeper@1.0-service.
05-25 09:15:25.794   926   962 W TeeMcDaemon: Cannot open trustlet /data/vendor/mcRegistry/08050500000000000000000000000000.tlbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.801   880   880 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-25 09:15:25.801   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-25 09:15:25.803   880   880 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-25 09:15:25.803   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio@7.1::IDevicesFactory/default in either framework or device VINTF manifest.
05-25 09:15:25.808   880   880 W BatteryNotifier: batterystats service unavailable!
05-25 09:15:25.809   880   880 I ServiceManagerCppClient: Waiting for service 'media.metrics' on '/dev/binder'...
05-25 09:15:25.810   832   832 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-25 09:15:25.811   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.configstore@1.0::ISurfaceFlingerConfigs/default in either framework or device VINTF manifest.
05-25 09:15:25.814   892   892 I SurfaceFlinger: Using HWComposer service: default
05-25 09:15:25.822   892   892 I SurfaceFlinger: SurfaceFlinger's main thread ready to run. Initializing graphics H/W...
05-25 09:15:25.864   990   990 I bootstat: Service started: /system/bin/bootstat --set_system_boot_reason 
05-25 09:15:25.897   995   995 I perfetto:           probes.cc:104 Starting /system/bin/traced_probes service
05-25 09:15:25.902   995   995 I perfetto:  probes_producer.cc:373 Disconnected from tracing service
05-25 09:15:25.905   996   996 W perfetto:          service.cc:232 Started traced, listening on /dev/socket/traced_producer /dev/socket/traced_consumer
05-25 09:15:25.920  1003  1003 W MTK_FG_NVRAM: fd < 0, init first!
05-25 09:15:25.920  1003  1003 W MTK_FG_NVRAM: init failed, return!
05-25 09:15:25.922   926   926 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/05160000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.922   926   926 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/020b0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.923  1003  1003 W MTK_FG_NVRAM: fd < 0, init first!
05-25 09:15:25.923  1003  1003 W MTK_FG_NVRAM: init failed, return!
05-25 09:15:25.923  1003  1003 W MTK_FG_NVRAM: fd < 0, init first!
05-25 09:15:25.923  1003  1003 W MTK_FG_NVRAM: init failed, return!
05-25 09:15:25.927  1003  1003 W MTK_FG_NVRAM: fd < 0, init first!
05-25 09:15:25.927  1003  1003 W MTK_FG_NVRAM: init failed, return!
05-25 09:15:25.927  1003  1003 W MTK_FG_NVRAM: fd < 0, init first!
05-25 09:15:25.927  1003  1003 W MTK_FG_NVRAM: init failed, return!
05-25 09:15:25.927  1003  1003 W MTK_FG_NVRAM: fd < 0, init first!
05-25 09:15:25.927  1003  1003 W MTK_FG_NVRAM: init failed, return!
05-25 09:15:25.927  1003  1003 W MTK_FG_NVRAM: fd < 0, init first!
05-25 09:15:25.927  1003  1003 W MTK_FG_NVRAM: init failed, return!
05-25 09:15:25.927  1003  1003 W MTK_FG_NVRAM: fd < 0, init first!
05-25 09:15:25.927  1003  1003 W MTK_FG_NVRAM: init failed, return!
05-25 09:15:25.928  1003  1003 W MTK_FG_NVRAM: fd < 0, init first!
05-25 09:15:25.928  1003  1003 W MTK_FG_NVRAM: init failed, return!
05-25 09:15:25.928  1003  1003 W MTK_FG_NVRAM: fd < 0, init first!
05-25 09:15:25.928  1003  1003 W MTK_FG_NVRAM: init failed, return!
05-25 09:15:25.934   926   926 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/030b0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.934   926   926 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/03100000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.944   926   926 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/031c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.944   926   926 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/033c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.944   926   926 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/035c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.944   926   926 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/037c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.944   926   926 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/031c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.944   926   926 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/032c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.944   926   926 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/033c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.944   926   926 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/034c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.944   926   926 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/035c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.944   926   926 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/036c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.944   926   926 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/037c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.952   926   926 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/070c0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.952   926   926 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/090b0000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.952   926   926 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/0f5eed3c3b5a47afacca69a84bf0efad.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.952   926   926 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/07060000000000000000000000007169.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.952   926   926 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/4be4f7dc1f2c11e5b5f7727283247c7f.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.952   926   926 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/08070000000000000000000000008270.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.952   926   926 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/07070000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.952   926   926 W TeeMcDaemon: Cannot open trustlet /vendor/app/mcRegistry/07407000000000000000000000000000.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.956   926   926 W TeeMcDaemon: Cannot open trustlet /odm/vendor/app/mcRegistry/6b3f5fa0f8cf55a7be2582587d62d63a.drbin (No such file or directory) [vendor/mediatek/proprietary/trustzone/trustonic/source/external/mobicore/common/500/Daemon/src/SecureWorld.cpp:114]
05-25 09:15:25.990  1017  1017 I thermal_repeater: RilRPC_init 
05-25 09:15:25.997  1017  1017 I thermal_repeater: RilRPC_init dlopen fail: dlopen failed: library "librpcril.so" not found 
05-25 09:15:26.013  1019  1019 I thermal_src1: ta_daemon_init
05-25 09:15:26.014   880   880 I ServiceManagerCppClient: Waiting for service 'media.metrics' on '/dev/binder' successful after waiting 204ms
05-25 09:15:26.015  1011  1025 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder'...
05-25 09:15:26.016   880   880 E AudioFlinger: Function: getAudioPolicyConfig Line: 2720 Failed 
05-25 09:15:26.016  1015  1015 I android.hardware.media.omx@1.0-service: mediacodecservice starting
05-25 09:15:26.021  1019  1019 I thermal_src: u_CATM_ON == -1, get catm init val again
05-25 09:15:26.021  1019  1019 I thermal_src: ta_catm_init_flow
05-25 09:15:26.021  1019  1019 I thermal_src: u_CATM_ON == -1, get catm init val
05-25 09:15:26.023  1014  1014 I HidlServiceManagement: Registered android.system.wifi.keystore@1.0::IKeystore/default
05-25 09:15:26.039  1039  1039 I KernelSU Next: ksud::cli: command: Services
05-25 09:15:26.039  1039  1039 I KernelSU Next: ksud::init_event: on_services triggered!
05-25 09:15:26.039  1039  1039 I KernelSU Next: ksud::module: /data/adb/service.d not exists, skip
05-25 09:15:26.047  1023  1044 I MtkAgpsNative: Enter mtk_agps_up_init
05-25 09:15:26.049  1023  1044 E agps    : [agps] ERR: [MNL] bind failed path=[/data/agps_supl/mnl_to_agps] reason=[No such file or directory]
05-25 09:15:26.049  1023  1044 E agps    : [agps] ERR: [Default] mtk_expat_xml_load2() failed [fopen() No such file or directory] file [/data/vendor/agps_supl/agps_profiles_conf2.xml]
05-25 09:15:26.050  1023  1044 E agps    : [agps] ERR: [Default] mtk_expat_xml_load2() failed [fopen() No such file or directory] file [/data/agps_supl/agps_profiles_conf2.xml]
05-25 09:15:26.050  1023  1044 E agps    : [agps] ERR: [Default] mtk_expat_xml_load2() failed [fopen() No such file or directory] file [/vendor/etc/agps_profiles_conf2.xml]
05-25 09:15:26.051   847   847 I android.hardware.usb@1.3-service-mediatekv2: setCurrentUsbFunctions: skip first time for usbd
05-25 09:15:26.052   847   847 I android.hardware.usb@1.3-service-mediatekv2: Usb Gadget setcurrent functions failed
05-25 09:15:26.053  1023  1044 E mtk_socket: ERR: mtk_socket_connect_local() connect() failed reason=[Connection refused]111 for path=[mtk_agpsd2debugService]
05-25 09:15:26.053  1023  1044 E agps    : [agps] ERR: [CP] get_ccci_uart  open failed node=[/dev/ccci2_tty2] reason=[No such file or directory]
05-25 09:15:26.053  1023  1044 E agps    :  ERR: [AGPS2WIFI] bind failed path=[/data/agps_supl/wifi_2_agps] reason=[No such file or directory]
05-25 09:15:26.053  1023  1044 E agps    : [agps] ERR: [WIFI] wifi_mgr_init  create_wifi2agps_fd failed
05-25 09:15:26.084  1005  1005 I cameraserver: ServiceManager: 0xb400007730f0f390
05-25 09:15:26.085  1005  1005 I CameraService: CameraService started (pid=1005)
05-25 09:15:26.085  1005  1005 I CameraService: CameraService process starting
05-25 09:15:26.085  1005  1005 W BatteryNotifier: batterystats service unavailable!
05-25 09:15:26.086  1005  1005 W BatteryNotifier: batterystats service unavailable!
05-25 09:15:26.087   573   573 I hwservicemanager: Since android.hardware.camera.provider@2.4::ICameraProvider/internal/0 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-25 09:15:26.087  1005  1005 W CameraProviderManager: tryToInitializeHidlProviderLocked: HIDL Camera provider HAL 'internal/0' is not actually available
05-25 09:15:26.088  1005  1005 W CameraProviderManager: tryToInitializeAidlProviderLocked: AIDL Camera provider HAL 'android.hardware.camera.provider.ICameraProvider/virtual/0' is not actually available
05-25 09:15:26.090  1005  1005 I HidlServiceManagement: Registered android.frameworks.cameraservice.service@2.2::ICameraService/default
05-25 09:15:26.091  1005  1005 I CameraService: CameraService pinged cameraservice proxy
05-25 09:15:26.092  1005  1005 I cameraserver: ServiceManager: 0xb400007730f0f390 done instantiate
05-25 09:15:26.096   999   999 I vtservice: [VT][SRV]ServiceManager: 0xb4000079eb2753f0
05-25 09:15:26.096   999   999 I vtservice: [VT][SRV]before VTService_instantiate
05-25 09:15:26.103   995   995 I perfetto:  probes_producer.cc:332 Connected to the service
05-25 09:15:26.105   573  1098 I hwservicemanager: Tried to start android.hardware.camera.provider@2.4::ICameraProvider/internal/0 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-25 09:15:26.107  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: failed to get path of fd 5: No such file or directory
05-25 09:15:26.108  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: allowing syscall: clock_gettime
05-25 09:15:26.108  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: allowing syscall: connect
05-25 09:15:26.108  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: allowing syscall: fcntl64
05-25 09:15:26.108  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: allowing syscall: socket
05-25 09:15:26.108  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: allowing syscall: writev
05-25 09:15:26.108  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(59): syscall getrandom redefined here
05-25 09:15:26.108  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(15): previous definition here
05-25 09:15:26.114   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.114   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.115   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.115   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.115   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.115  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(1): syscall read redefined here
05-25 09:15:26.115  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(9): previous definition here
05-25 09:15:26.115   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.115  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(2): syscall write redefined here
05-25 09:15:26.115  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(5): previous definition here
05-25 09:15:26.115  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(3): syscall exit redefined here
05-25 09:15:26.115   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.115  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(40): previous definition here
05-25 09:15:26.115   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.115  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(4): syscall rt_sigreturn redefined here
05-25 09:15:26.115  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(45): previous definition here
05-25 09:15:26.115  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(6): syscall exit_group redefined here
05-25 09:15:26.115   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.115  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(44): previous definition here
05-25 09:15:26.115  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(7): syscall clock_gettime redefined here
05-25 09:15:26.115  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(7): previous definition here
05-25 09:15:26.115   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.115   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.115  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(8): syscall gettimeofday redefined here
05-25 09:15:26.115  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(47): previous definition here
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(9): syscall futex redefined here
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(3): previous definition here
05-25 09:15:26.116   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(10): syscall getrandom redefined here
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(15): previous definition here
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(13): syscall ppoll redefined here
05-25 09:15:26.116   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.116   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(13): previous definition here
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(14): syscall pipe2 redefined here
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(46): previous definition here
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(15): syscall openat redefined here
05-25 09:15:26.116   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(30): previous definition here
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(16): syscall dup redefined here
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(12): previous definition here
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(17): syscall close redefined here
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(10): previous definition here
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(18): syscall lseek redefined here
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(50): previous definition here
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(19): syscall getdents64 redefined here
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(58): previous definition here
05-25 09:15:26.116  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(20): syscall faccessat redefined here
05-25 09:15:26.117  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(38): previous definition here
05-25 09:15:26.117   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.117   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.117  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(27): syscall rt_sigprocmask redefined here
05-25 09:15:26.117  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(41): previous definition here
05-25 09:15:26.117  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(30): syscall prctl redefined here
05-25 09:15:26.117  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(6): previous definition here
05-25 09:15:26.117   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.117  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(31): syscall madvise redefined here
05-25 09:15:26.117   823   823 I zygote64: Initializing ART runtime metrics
05-25 09:15:26.117  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(29): previous definition here
05-25 09:15:26.117  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(32): syscall mprotect redefined here
05-25 09:15:26.117  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(28): previous definition here
05-25 09:15:26.117  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(33): syscall munmap redefined here
05-25 09:15:26.117  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(27): previous definition here
05-25 09:15:26.117  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(34): syscall getuid32 redefined here
05-25 09:15:26.118  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(34): previous definition here
05-25 09:15:26.118  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(35): syscall fstat64 redefined here
05-25 09:15:26.118  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(35): previous definition here
05-25 09:15:26.118  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(36): syscall mmap2 redefined here
05-25 09:15:26.118  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(14): previous definition here
05-25 09:15:26.118   573   573 I hwservicemanager: Since vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-25 09:15:26.118  1004  1004 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-25 09:15:26.119  1004  1004 E mnld_pwr_interface: mnld_pwr_init: mnld_pwr_open failed, No such file or directory
05-25 09:15:26.119  1004  1004 E MNL2AGPS: bind_udp_socket: bind failed path=[/data/agps_supl/agps_to_mnl] reason=[No such file or directory]
05-25 09:15:26.119  1004  1004 E mtk_lbs_utility: init_timer_id_alarm: timerfd_create  CLOCK_BOOTTIME_ALARM 
05-25 09:15:26.119  1004  1004 E MNLD    : mnld_init: mnl2hal_release_wakelock failed because of safe_sendto fail ,strerror:Connection refused 
05-25 09:15:26.119  1004  1004 E MNLD    : mnld_init: mnl2hal_mnld_reboot failed because of safe_sendto fail ,strerror:Connection refused 
05-25 09:15:26.119  1004  1004 E mnld    : mtk_socket_connect_local: mtk_socket_connect_local() connect() failed reason=[Connection refused]111 for path=[mtk_mnld2debugService]
05-25 09:15:26.119  1004  1124 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-25 09:15:26.119  1004  1124 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:0
05-25 09:15:26.120  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(66): syscall getpid redefined here
05-25 09:15:26.120  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(11): previous definition here
05-25 09:15:26.120  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(67): syscall gettid redefined here
05-25 09:15:26.120  1038  1038 E android.hardware.biometrics.fingerprint@2.1-service: fingerprint hwbinder service starting
05-25 09:15:26.120  1038  1038 I android.hardware.biometrics.fingerprint@2.1-service: read_proc_string_data sys_buffer_data=G_OPTICAL_G3S #end
05-25 09:15:26.121  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(12): previous definition here
05-25 09:15:26.121  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(74): syscall recvfrom redefined here
05-25 09:15:26.121  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(22): previous definition here
05-25 09:15:26.121  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(77): syscall sched_getaffinity redefined here
05-25 09:15:26.121  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(75): previous definition here
05-25 09:15:26.121  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(82): syscall sysinfo redefined here
05-25 09:15:26.121  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(24): previous definition here
05-25 09:15:26.121  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: <fd>(83): syscall setsockopt redefined here
05-25 09:15:26.121  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: compile_file: /system/etc/seccomp_policy/crash_dump.arm.policy(23): previous definition here
05-25 09:15:26.121  1015  1015 W android.hardware.media.omx@1.0-service: libminijail[1015]: logging seccomp filter failures
05-25 09:15:26.122  1038  1038 E android.hardware.biometrics.fingerprint@2.1-service: fp read fp_id_string = G_OPTICAL_G3S
05-25 09:15:26.123  1038  1038 E android.hardware.biometrics.fingerprint@2.1-service: optical goodix fingerprint
05-25 09:15:26.125  1038  1038 I android.hardware.biometrics.fingerprint@2.1-service: fpSettingsInit project name:0
05-25 09:15:26.125  1038  1038 I android.hardware.biometrics.fingerprint@2.1-service: read_proc_string_data sys_buffer_data=Device version:		AMS644VA04_MTK04_20615
05-25 09:15:26.125  1038  1038 I android.hardware.biometrics.fingerprint@2.1-service: Device manufacture:		samsung1024
05-25 09:15:26.125  1038  1038 I android.hardware.biometrics.fingerprint@2.1-service:  #end
05-25 09:15:26.125  1038  1038 I android.hardware.biometrics.fingerprint@2.1-service: fpSettingsInit lcd type:1
05-25 09:15:26.126  1038  1038 I android.hardware.biometrics.fingerprint@2.1-service: fpSettingsInit select config index is :0
05-25 09:15:26.131   573   573 I hwservicemanager: Since vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-25 09:15:26.132  1035  1035 I HidlServiceManagement: getService: Trying again for vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default...
05-25 09:15:26.133   573  1121 I hwservicemanager: Tried to start vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-25 09:15:26.134  1027  1027 I VPUD    : vdec_codec_service_init() block mode
05-25 09:15:26.134  1027  1027 I VPUD    : venc_codec_service_init()
05-25 09:15:26.134  1027  1027 I VPUD    : -- send_init_fin
05-25 09:15:26.134   824   824 I zygote  : Initializing ART runtime metrics
05-25 09:15:26.135  1038  1038 I android.hardware.biometrics.fingerprint@2.1-service: do nothing
05-25 09:15:26.137   573  1141 I hwservicemanager: Tried to start vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-25 09:15:26.137  1016  1016 I ULog    : ULog initialized: mode=0x1  filters: req=0x0 func=0x0/0x0 details=0xfffff000 level=3
05-25 09:15:26.137  1027  1145 I VPUD    : vdec_service_entry()
05-25 09:15:26.138  1027  1146 I VPUD    : venc service entry TID = 1146
05-25 09:15:26.142  1038  1038 E [GF_HAL][HalContext]: [init], init with G3 HAL.
05-25 09:15:26.174  1009  1009 I main_extractorservice: enable media.extractor memory limits
05-25 09:15:26.177   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.177   832   832 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.180  1009  1009 W mediaextractor: libminijail[1009]: failed to get path of fd 5: No such file or directory
05-25 09:15:26.180  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(38): previous definition here
05-25 09:15:26.181   832   832 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-25 09:15:26.181  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(18): previous definition here
05-25 09:15:26.181  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(6): previous definition here
05-25 09:15:26.181  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(27): previous definition here
05-25 09:15:26.181  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(29): previous definition here
05-25 09:15:26.181  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(28): previous definition here
05-25 09:15:26.182  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(4): previous definition here
05-25 09:15:26.182  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(32): previous definition here
05-25 09:15:26.182  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(12): previous definition here
05-25 09:15:26.182  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(9): previous definition here
05-25 09:15:26.182  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(8): previous definition here
05-25 09:15:26.182  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(23): previous definition here
05-25 09:15:26.182  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(41): previous definition here
05-25 09:15:26.182  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(25): previous definition here
05-25 09:15:26.182  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(56): previous definition here
05-25 09:15:26.182  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(5): previous definition here
05-25 09:15:26.183  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(14): previous definition here
05-25 09:15:26.183  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(13): previous definition here
05-25 09:15:26.183  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(11): previous definition here
05-25 09:15:26.183  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(15): previous definition here
05-25 09:15:26.183  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(16): previous definition here
05-25 09:15:26.183  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(10): previous definition here
05-25 09:15:26.187  1009  1009 W mediaextractor: libminijail[1009]: compile_file: /apex/com.android.media/etc/seccomp_policy/crash_dump.arm64.policy(7): previous definition here
05-25 09:15:26.187  1009  1009 W mediaextractor: libminijail[1009]: compile_file: <fd>(56): previous definition here
05-25 09:15:26.187  1009  1009 W mediaextractor: libminijail[1009]: compile_file: /apex/com.android.media/etc/seccomp_policy/crash_dump.arm64.policy(6): previous definition here
05-25 09:15:26.192   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.atrace@1.0::IAtraceDevice/default in either framework or device VINTF manifest.
05-25 09:15:26.194  1015  1015 I HidlServiceManagement: Registered android.hardware.media.omx@1.0::IOmx/default
05-25 09:15:26.194  1015  1015 I HidlServiceManagement: Removing namespace from process name android.hardware.media.omx@1.0-service to omx@1.0-service.
05-25 09:15:26.194  1015  1015 I android.hardware.media.omx@1.0-service: IOmx HAL service created.
05-25 09:15:26.195   901   901 E ccci_mdinit: (1):fail to open /mnt/vendor/nvdata/APCFG/APRDCL/CXP_SBP: 2
05-25 09:15:26.195   901   901 I ccci_mdinit: (1):get_cip_sbp_setting, file /custom/etc/firmware/CIP_MD_SBP NOT exists!
05-25 09:15:26.195   901   901 I ccci_mdinit: (1):PRJ_SBP_ID:ro.vendor.mtk_md_sbp_custom_value not exist, using default value
05-25 09:15:26.200  1015  1015 I HidlServiceManagement: Registered android.hardware.media.omx@1.0::IOmxStore/default
05-25 09:15:26.200   901   901 I ccci_mdinit: (1):SBP_SUB_ID:persist.vendor.operator.subid not exist
05-25 09:15:26.200  1015  1015 I HidlServiceManagement: Removing namespace from process name android.hardware.media.omx@1.0-service to omx@1.0-service.
05-25 09:15:26.200   901   901 I ccci_mdinit: (1):set md boot data:mdl=0 sbp=0 dbg_dump=-1 sbp_sub=0
05-25 09:15:26.200   901   901 E ccci_mdinit: [SYSENV]get_env_info():240 , env_buffer[0] : 0xb4000076cee8f030
05-25 09:15:26.201   901   901 I ccci_mdinit: (1):get md_type (null)
05-25 09:15:26.201   901   901 I ccci_mdinit: (1):MD1 set status: mtk.md1.status=bootup 
05-25 09:15:26.201   901   901 I ccci_mdinit: (1):md_id = 0; mdstatusfd = -1
05-25 09:15:26.207   832   832 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xd358fe37)
05-25 09:15:26.213   832   832 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x90359f7b)
05-25 09:15:26.215   832   832 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-25 09:15:26.216   832   832 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0x2bbf26bf)
05-25 09:15:26.218   832   832 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleUninit, handle = 0x0x4043b9e7)
05-25 09:15:26.233  1013  1013 I storaged: Unable to get AIDL health service, trying HIDL...
05-25 09:15:26.248   832   832 I HidlServiceManagement: Registered android.hardware.soundtrigger@2.3::ISoundTriggerHw/default
05-25 09:15:26.250   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-25 09:15:26.251   832   832 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.2::IBluetoothAudioProvidersFactory/default.
05-25 09:15:26.252   880   880 E libxml2 : I/O warning : failed to load "/odm/etc/virtual_audio_policy_configuration.xml": No such file or directory
05-25 09:15:26.252   880   880 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_ASSISTANT found, using default volume configuration
05-25 09:15:26.252   880   880 W APM::AudioPolicyEngine/Base: processParsingResult: No configuration of AUDIO_STREAM_CALL_ASSISTANT found, using default volume configuration
05-25 09:15:26.258   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-25 09:15:26.259   832   832 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.1::IBluetoothAudioProvidersFactory/default.
05-25 09:15:26.262  1026  1026 I HidlServiceManagement: Registered vendor.mediatek.hardware.pq@2.15::IPictureQuality/default
05-25 09:15:26.262  1026  1026 I HidlServiceManagement: Removing namespace from process name vendor.mediatek.hardware.pq@2.2-service to pq@2.2-service.
05-25 09:15:26.263  1013  1013 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder'...
05-25 09:15:26.263   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default in either framework or device VINTF manifest.
05-25 09:15:26.264   832   832 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.audio@2.0::IBluetoothAudioProvidersFactory/default.
05-25 09:15:26.264   832   832 W audiohalservice: Could not register Bluetooth Audio API
05-25 09:15:26.264   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default in either framework or device VINTF manifest.
05-25 09:15:26.264   832   832 E LegacySupport: Could not get passthrough implementation for android.hardware.bluetooth.a2dp@1.0::IBluetoothAudioOffload/default.
05-25 09:15:26.265   832   832 W audiohalservice: Could not register Bluetooth Audio Offload API
05-25 09:15:26.266  1012  1012 I mediaserver: ServiceManager: 0xf0d032b0
05-25 09:15:26.266  1012  1012 W BatteryNotifier: batterystats service unavailable!
05-25 09:15:26.267  1012  1012 W BatteryNotifier: batterystats service unavailable!
05-25 09:15:26.267   892   892 I HidlServiceManagement: Registered android.frameworks.displayservice@1.0::IDisplayService/default
05-25 09:15:26.309  1026  1184 E PQ      : [PQ_SERVICE] aisdr2hdr_pqindex is not found in cust_color.xml
05-25 09:15:26.320  1026  1184 I vendor.mediatek.hardware.pq@2.2-service: transferAIOutput(), register trs callback
05-25 09:15:26.366   832   948 E AudioALSADeviceParser: GetAllPcmAttribute fget error
05-25 09:15:26.368   832   832 I audiohalservice: createIBluetoothAudioProviderFactory() from android.hardware.bluetooth.audio-impl success
05-25 09:15:26.371   832   948 E AudioParamParser-vnd: open /proc/oplusVersion/prjVersion failed!!!
05-25 09:15:26.372   832   832 E audiohalservice: Failed to dlopen android.hardware.audio.sounddose-vendor-impl.so: dlopen failed: library "android.hardware.audio.sounddose-vendor-impl.so" not found
05-25 09:15:26.372   832   832 W audiohalservice: createISoundDoseFactory() from android.hardware.audio.sounddose-vendor-impl failed
05-25 09:15:26.378   832   948 W AudioParamParser-vnd: appHandleLoadDirFeatureOptionsInfo(), No /odm/etc/audio/audio_param/./AudioParamOptions_mgvi.xml file
05-25 09:15:26.379  1037  1037 I NetdagentFirewall: setupIptablesHooks done in oem_iptables_init
05-25 09:15:26.379  1037  1037 I NetdagentController: Initializing iptables: 156.4ms
05-25 09:15:26.379  1037  1037 I Netdagent:  Create CommandService  successfully
05-25 09:15:26.381  1037  1230 I HidlServiceManagement: Registered vendor.mediatek.hardware.netdagent@1.0::INetdagent/default
05-25 09:15:26.444  1000  1000 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-neuron
05-25 09:15:26.445  1000  1000 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-25 09:15:26.447  1000  1000 I ANNService: Registered service for mtk-neuron
05-25 09:15:26.448   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.448   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.448   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.448   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.448   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.448   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.449   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.449   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.449   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.449   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.449   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.449   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.449   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.449   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.449   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.450   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.450   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.450   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.463  1028  1028 I mediaswcodec: media swcodec service starting
05-25 09:15:26.464  1000  1000 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-dsp
05-25 09:15:26.465  1000  1000 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-25 09:15:26.465  1000  1000 I ANNService: Registered service for mtk-dsp
05-25 09:15:26.467  1028  1028 W mediaswcodec: libminijail[1028]: failed to get path of fd 5: No such file or directory
05-25 09:15:26.468  1028  1028 W mediaswcodec: libminijail[1028]: compile_file: <fd>(39): previous definition here
05-25 09:15:26.468  1000  1000 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-mdla
05-25 09:15:26.468  1000  1000 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-25 09:15:26.468  1000  1000 I ANNService: Registered service for mtk-mdla
05-25 09:15:26.471  1028  1028 I CodecServiceRegistrant: Creating software Codec2 service...
05-25 09:15:26.471  1000  1000 I HidlServiceManagement: Registered android.hardware.neuralnetworks@1.3::IDevice/mtk-gpu
05-25 09:15:26.471  1000  1000 I HidlServiceManagement: Removing namespace from process name android.hardware.neuralnetworks@1.3-service-mtk-neuron to neuralnetworks@1.3-service-mtk-neuron.
05-25 09:15:26.472  1000  1000 I ANNService: Registered service for mtk-gpu
05-25 09:15:26.480  1028  1028 I HidlServiceManagement: Registered android.hardware.media.c2@1.2::IComponentStore/software
05-25 09:15:26.484  1028  1028 I CodecServiceRegistrant: Preferred Codec2 HIDL store is set to "default".
05-25 09:15:26.484  1028  1028 I CodecServiceRegistrant: Software Codec2 service created and registered.
05-25 09:15:26.485  1000  1000 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.apusys@2.1::INeuronApusys/default
05-25 09:15:26.494   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.494   832   948 W AudioParamParser-vnd: audioTypeLoadParamFieldInfoHash(), No end bit string
05-25 09:15:26.495  1000  1000 I apuware_server: Start NeuronXrp 2.0 service 
05-25 09:15:26.496   832   948 W AudioParamParser-vnd: audioTypeLoadParamUnitHash(), No paramUnit element found! (AudioType = Playback)
05-25 09:15:26.497  1000  1000 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.xrp@2.0::INeuronXrp/default
05-25 09:15:26.502  1000  1000 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.hmp@1.0::IApuwareHmp/default
05-25 09:15:26.508  1000  1000 I HidlServiceManagement: Registered vendor.mediatek.hardware.apuware.utils@2.0::IApuwareUtils/default
05-25 09:15:26.512   901   934 E ccci_fsd(1): FS_OTP_init:otp_get_size:1048576, status=0, type=0!
05-25 09:15:26.515   832   948 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleParseXml, handle = 0x0xb68d5a2b)
05-25 09:15:26.515   832  1241 W AudioParamParser-vnd: utilMkdir(), mkdir fail (/data/vendor/audiohal/audio_param/)
05-25 09:15:26.516   832  1241 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleReloadCustXml, handle = 0x0xb68d5a2b)
05-25 09:15:26.517   832  1241 I AudioParamParser-vnd: unloadXmlOps(), dlclose successfully (caller = appHandleThreadLoop, handle = 0x0xb68d5a2b)
05-25 09:15:26.554   823   823 E ActivityRecognitionHardware: activity_recognition HAL is deprecated. class_init is effectively a no-op
05-25 09:15:26.579   832   948 E audio_messenger_ipi: audio_ipi_dma_cbk_register() ioctl fail! ret = -1, errno: 19
05-25 09:15:26.584   832   948 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-25 09:15:26.601   999   999 W AVSync  : initFD done, g_fd_name: /dev/ccci_imsdc
05-25 09:15:26.601   572   572 I auditd  : avc:  denied  { find } for pid=999 uid=1000 name=vendor.mediatek.hardware.videotelephony.IVideoTelephony/default scontext=u:r:vtservice:s0 tcontext=u:object_r:default_android_service:s0 tclass=service_manager permissive=0
05-25 09:15:26.602   999  1252 W ServiceManagerCppClient: Failed to get isDeclared for vendor.mediatek.hardware.videotelephony.IVideoTelephony/default: Status(-1, EX_SECURITY): 'SELinux denied for service.'
05-25 09:15:26.606   832   948 W GainTableParamParser: error: get audioType fail, audioTypeName = CRSVol
05-25 09:15:26.626   832   948 W AudioParamParser-vnd: appHandleGetFeatureOptionIntValue(), No MODEM_PCM_SAMPLE_RATE such feature option
05-25 09:15:26.626   832   948 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-25 09:15:26.626   832   948 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-25 09:15:26.631   832   948 E AudioDspStreamManager: -getDspRuntimeEn task_scene = 17
05-25 09:15:26.631   832   948 E AudioDspStreamManager: -getDspRuntimeEn Mixer of  = NULL!!
05-25 09:15:26.633   832   948 E oplus_audio_extern_config: get_oplus_audio_extern_config(), Error: get ctl 'OPLUS_AUDIO_EXTERN_CONFIG' null
05-25 09:15:26.640   832   948 I OplusBinauralRecordAudioHal: binauralRecordFactoryInit, enter.
05-25 09:15:26.641   832   948 E OplusBinauralRecordAudioHal: binauralRecordFactoryInit: dlopen failed for /odm/lib/libbluetooth_audio_extend_factory_client.so
05-25 09:15:26.642   880   880 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-25 09:15:26.642   832   832 E AudioMTKGainController: getGainDevice(), error, devices (0) not support, return GAIN_DEVICE_SPEAKER
05-25 09:15:26.642   832   832 E AudioMTKGainController: setNormalVolume(), invalid param, stream -1, mSceneIndex 0, index -1, devices 0, gainDevice 2, return
05-25 09:15:26.643   832   832 W AudioALSAStreamManager: setMode(), mAudioMode: 0 == 0, return
05-25 09:15:26.643   880   880 I AudioFlinger: loadHwModule() Loaded primary audio interface, handle 10
05-25 09:15:26.645   880   880 I AudioFlinger: openOutput() this 0xb400007d3c5d8840, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x2
05-25 09:15:26.646   880   880 I AudioFlinger: operator(): Using 3000 ms as standby time
05-25 09:15:26.647   880   880 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-25 09:15:26.648   880   880 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-25 09:15:26.649   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-25 09:15:26.650   880   880 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-25 09:15:26.650   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio@7.1::IDevicesFactory/default in either framework or device VINTF manifest.
05-25 09:15:26.665   823   823 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlags
05-25 09:15:26.665   823   823 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlagsImpl
05-25 09:15:26.665   823   823 W Zygote  : Class not found for preloading: android.media.audio.Flags
05-25 09:15:26.666   901   934 W ccci_mdinit: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.system.suspend.ISystemSuspend/default
05-25 09:15:26.668   832   832 E EffectsFactoryConfigLoader: loadLibrary Could not find library in effect directories: libaudiopreprocessing_mtk.so
05-25 09:15:26.669   832   832 E EffectsFactoryConfigLoader: EffectLoadXmlEffectConfig 4 errors during loading of configuration: /vendor/etc/audio_effects.xml
05-25 09:15:26.671   880  1282 I AudioFlinger: AudioFlinger's thread 0xb400007f9e4ce760 tid=1282 ready to run
05-25 09:15:26.671   880  1282 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-25 09:15:26.675   880   880 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-25 09:15:26.678   880  1282 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.679   880   880 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-25 09:15:26.679   880   880 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-25 09:15:26.679   880   880 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-25 09:15:26.679   880   880 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-25 09:15:26.679   880   880 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-25 09:15:26.679   880   880 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-25 09:15:26.680   880   880 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-25 09:15:26.680   880   880 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-25 09:15:26.680   880   880 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-25 09:15:26.680   880   880 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-25 09:15:26.680   880   880 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-25 09:15:26.680   880   880 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-25 09:15:26.680   880   880 E APM_AudioPolicyManager: invalid volume index range in the curve:
05-25 09:15:26.680   880   880 E APM_AudioPolicyManager:   Can be muted  Index Min  Index Max  Index Cur [device : index]...
05-25 09:15:26.680   880   880 E APM_AudioPolicyManager:   true          -1         -1         ******** : 00, 
05-25 09:15:26.682   880   880 W AudioFlinger: moveEffects() AUDIO_SESSION_OUTPUT_MIX not found in orphans, checking other mix
05-25 09:15:26.682   880   880 W AudioFlinger: moveEffects() bad srcIo 0
05-25 09:15:26.683   880   880 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-25 09:15:26.705   832  1281 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x2, output_devices == current_output_devices(0x00000002), return
05-25 09:15:26.709   880   880 I AudioFlinger: openOutput() this 0xb400007d3c5d8840, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x8
05-25 09:15:26.709   832  1281 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-25 09:15:26.711   880   880 I AudioFlinger: HAL output buffer size 2048 frames, normal sink buffer size 2048 frames
05-25 09:15:26.712   880  1289 I AudioFlinger: AudioFlinger's thread 0xb400007f9e431760 tid=1289 ready to run
05-25 09:15:26.712   880  1289 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.713   880  1289 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.719  1016  1016 I mtkcam-devicemgr: [initialize] +
05-25 09:15:26.722   880   880 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-25 09:15:26.787   832  1281 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x8, output_devices == current_output_devices(0x00000002), return
05-25 09:15:26.790   880   880 I AudioFlinger: openOutput() this 0xb400007d3c5d8840, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x4
05-25 09:15:26.790   832  1281 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-25 09:15:26.792   880   880 I AudioFlinger: HAL output buffer size 256 frames, normal sink buffer size 768 frames
05-25 09:15:26.793   880  1295 I AudioFlinger: AudioFlinger's thread 0xb400007f9e39e760 tid=1295 ready to run
05-25 09:15:26.794   880  1295 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.798   880   880 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-25 09:15:26.802   880  1295 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.821   832  1281 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x4, output_devices == current_output_devices(0x00000002), return
05-25 09:15:26.824   880   880 I AudioFlinger: openOutput() this 0xb400007d3c5d8840, module 10 Device AUDIO_DEVICE_OUT_SPEAKER, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x8000
05-25 09:15:26.824   832  1281 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-25 09:15:26.826   880   880 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-25 09:15:26.827   880  1296 I AudioFlinger: AudioFlinger's thread 0xb400007f9dd7d760 tid=1296 ready to run
05-25 09:15:26.827   880  1296 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.829   880  1296 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.831   880   880 I APM::AudioPolicyEngine: getDevicesForStrategyInt no device found for strategy 7
05-25 09:15:26.864   832  1281 W AudioALSAStreamManager: routingOutputDevice(), flag: 0x8000, output_devices == current_output_devices(0x00000002), return
05-25 09:15:26.867   880   880 I AudioFlinger: openOutput() this 0xb400007d3c5d8840, module 10 Device AUDIO_DEVICE_OUT_TELEPHONY_TX, @:, SamplingRate 48000, Format 0x000003, Channels 0x3, flags 0x10000
05-25 09:15:26.867   832  1281 W AudioALSASampleRateController: -setPrimaryStreamOutSampleRate(), sample_rate(48000) == mPrimaryStreamOutSampleRate(48000), return
05-25 09:15:26.869   880   880 I AudioFlinger: HAL output buffer size 1024 frames, normal sink buffer size 1024 frames
05-25 09:15:26.870   880  1297 I AudioFlinger: AudioFlinger's thread 0xb400007f9c160760 tid=1297 ready to run
05-25 09:15:26.871   880  1297 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.872   880  1297 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.921   880  1300 I AudioFlinger: AudioFlinger's thread 0xb400007f9da01a78 tid=1300 ready to run
05-25 09:15:26.921   880  1300 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.921   880  1300 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.925   880  1304 I AudioFlinger: AudioFlinger's thread 0xb400007f9da01a78 tid=1304 ready to run
05-25 09:15:26.926   880  1304 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.928   880  1304 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.931   880  1306 I AudioFlinger: AudioFlinger's thread 0xb400007f9da01a78 tid=1306 ready to run
05-25 09:15:26.931   880  1306 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.933   880  1306 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.938   880  1308 I AudioFlinger: AudioFlinger's thread 0xb400007f9da01a78 tid=1308 ready to run
05-25 09:15:26.938   880  1308 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.939   880  1308 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.945   880  1312 I AudioFlinger: AudioFlinger's thread 0xb400007f9da01a78 tid=1312 ready to run
05-25 09:15:26.946   832  1281 E DevicesFactoryHAL: loadAudioInterface couldn't load audio hw module audio.binaural_record (No such file or directory)
05-25 09:15:26.946   880   880 E AudioFlinger: loadHwModule() error -22 loading module binaural_record
05-25 09:15:26.946   880   880 W APM_AudioPolicyManager: could not load HW module binaural_record
05-25 09:15:26.951   880   880 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-25 09:15:26.952   880   880 I AudioFlinger: loadHwModule() Loaded bluetooth audio interface, handle 18
05-25 09:15:26.953   832  1281 E DevicesFactoryHAL: loadAudioInterface couldn't load audio hw module audio.a2dp (No such file or directory)
05-25 09:15:26.953   880   880 E AudioFlinger: loadHwModule() error -22 loading module a2dp
05-25 09:15:26.953   880   880 W APM_AudioPolicyManager: could not load HW module a2dp
05-25 09:15:26.953   832  1281 E DevicesFactoryHAL: loadAudioInterface couldn't load audio hw module audio.usb (No such file or directory)
05-25 09:15:26.953   880   880 E AudioFlinger: loadHwModule() error -22 loading module usb
05-25 09:15:26.953   880   880 W APM_AudioPolicyManager: could not load HW module usb
05-25 09:15:26.956   832  1281 I r_submix: adev_open(name=audio_hw_if)
05-25 09:15:26.957   880   880 W AudioFlinger: loadHwModule() sound dose reporting is not available
05-25 09:15:26.957   832  1281 I r_submix: adev_init_check()
05-25 09:15:26.958   880   880 I AudioFlinger: loadHwModule() Loaded r_submix audio interface, handle 26
05-25 09:15:26.960   880  1314 I AudioFlinger: AudioFlinger's thread 0xb400007f9da01a78 tid=1314 ready to run
05-25 09:15:26.961   880  1314 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.962   880  1314 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:26.965  1038  1038 E [GF_HAL][ShenzhenSensor]: [init] gainvalue: 150/100
05-25 09:15:26.965  1038  1038 E [GF_HAL][ShenzhenSensor]: [init] expotime 38
05-25 09:15:26.965  1038  1038 E [GF_HAL][ShenzhenSensor]: [init] @@@@@ mQRCode=Z918095013A0061493,len=18
05-25 09:15:26.966  1038  1038 E [GF_HAL][ShenzhenSensor]: [init] module_type = 0x6
05-25 09:15:26.966  1038  1038 E [GF_HAL][ShenzhenSensor]: [init] lens_type = 0xa
05-25 09:15:26.965   880   880 W FactoryHal: hasAidlHalService android.hardware.audio.effect.IFactory/default: false
05-25 09:15:26.966   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio.effect@7.1::IEffectsFactory/default in either framework or device VINTF manifest.
05-25 09:15:26.984   880   880 W FactoryHal: hasAidlHalService android.hardware.audio.core.IModule/default: false
05-25 09:15:26.984   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.audio@7.1::IDevicesFactory/default in either framework or device VINTF manifest.
05-25 09:15:27.120  1004  1124 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-25 09:15:27.120  1004  1124 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:1
05-25 09:15:27.127  1004  1112 E mnld    : thread_adc_capture_init: set IOCTL_EMI_MEMORY_INIT failed,(Success)
05-25 09:15:27.132  1035  1035 W HidlServiceManagement: Waited one second for vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default
05-25 09:15:27.133   573   573 I hwservicemanager: Since vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-25 09:15:27.133  1035  1035 I HidlServiceManagement: getService: Trying again for vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default...
05-25 09:15:27.134   573  1317 I hwservicemanager: Tried to start vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-25 09:15:27.493  1321  1330 W system_server: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: stats
05-25 09:15:27.495  1321  1330 W BpBinder: Linking to death on android.os.IStatsd but there are no threads (yet?) listening to incoming transactions. See ProcessState::startThreadPool and ProcessState::setThreadPoolMaxThreadCount. Generally you should setup the binder threadpool before other initialization steps.
05-25 09:15:27.502   822   826 I ServiceManagerCppClient: Waiting for service 'statscompanion' on '/dev/binder'...
05-25 09:15:27.681  1038  1038 E [GF_HAL][FingerprintCore]: [init_report_data] algo version is V03.02.02.230.005
05-25 09:15:27.681  1038  1038 E [GF_HAL][FingerprintCore]: [init_report_data] lcdtype_prop = SDC
05-25 09:15:27.681  1038  1038 E [GF_HAL][FingerprintCore]: [init_report_data] type = V03.02.02.230.005_S_SDC
05-25 09:15:27.704  1038  1038 I HidlServiceManagement: Registered vendor.oplus.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint/default
05-25 09:15:27.704  1038  1038 I HidlServiceManagement: Removing namespace from process name vendor.oplus.hardware.biometrics.fingerprint@2.1-service to fingerprint@2.1-service.
05-25 09:15:27.707   573   573 W hwservicemanager: Detected instance of android.hardware.biometrics.fingerprint@2.3::IBiometricsFingerprint (pid: 1035) registering over instance of or with base of android.hardware.biometrics.fingerprint@2.1::IBiometricsFingerprint (pid: 1038).
05-25 09:15:27.708  1035  1035 I HidlServiceManagement: Registered android.hardware.biometrics.fingerprint@2.3::IBiometricsFingerprint/default
05-25 09:15:27.708  1035  1035 I HidlServiceManagement: Removing namespace from <NAME_EMAIL>6893 to fingerprint@2.3-service.mt6893.
05-25 09:15:27.741  1321  1321 E system_server: memevent listener failed to initialize, not supported kernel
05-25 09:15:27.747  1321  1321 W Binder  : 	at android.os.IServiceManager$Stub$Proxy.checkService(IServiceManager.java:507)
05-25 09:15:27.747  1321  1321 W Binder  : 	at android.os.ServiceManagerProxy.checkService(ServiceManagerNative.java:73)
05-25 09:15:27.747  1321  1321 W Binder  : 	at android.os.ServiceManagerProxy.getService2(ServiceManagerNative.java:69)
05-25 09:15:27.747  1321  1321 W Binder  : 	at android.os.ServiceManager.rawGetService(ServiceManager.java:430)
05-25 09:15:27.747  1321  1321 W Binder  : 	at android.os.ServiceManager.getService(ServiceManager.java:175)
05-25 09:15:27.747  1321  1321 W Binder  : 	at android.app.ActivityThread.initializeSystemThread(ActivityThread.java:8715)
05-25 09:15:27.747  1321  1321 W Binder  : 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:597)
05-25 09:15:27.747  1321  1321 W Binder  : 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:889)
05-25 09:15:27.760  1321  1321 I SystemServerInitThreadPool: Creating instance with 8 threads
05-25 09:15:27.770  1321  1352 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-com.android.providers.media.module.xml
05-25 09:15:27.771  1321  1352 I SystemConfig: Reading permissions from /system/etc/sysconfig/app.grapheneos.gmscompat.xml
05-25 09:15:27.771  1321  1352 I SystemConfig: Reading permissions from /system/etc/sysconfig/app.grapheneos.networklocation.xml
05-25 09:15:27.772  1321  1352 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-platform-full-base.xml
05-25 09:15:27.772  1321  1352 I SystemConfig: Reading permissions from /system/etc/sysconfig/framework-sysconfig.xml
05-25 09:15:27.772  1321  1352 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-asl-files.xml
05-25 09:15:27.773  1321  1352 I SystemConfig: Reading permissions from /system/etc/sysconfig/hiddenapi-whitelist-co.aospa.sense.xml
05-25 09:15:27.773  1321  1352 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-strict-signature.xml
05-25 09:15:27.774  1321  1352 I SystemConfig: Reading permissions from /system/etc/sysconfig/backup.xml
05-25 09:15:27.774  1321  1352 I SystemConfig: Reading permissions from /system/etc/sysconfig/hiddenapi-package-whitelist.xml
05-25 09:15:27.775  1321  1352 I SystemConfig: Reading permissions from /system/etc/sysconfig/enhanced-confirmation.xml
05-25 09:15:27.775  1321  1352 I SystemConfig: Reading permissions from /system/etc/sysconfig/package-shareduid-allowlist.xml
05-25 09:15:27.776  1321  1352 I SystemConfig: Reading permissions from /system/etc/sysconfig/initial-package-stopped-states.xml
05-25 09:15:27.776  1321  1352 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-platform-handheld-system.xml
05-25 09:15:27.776  1321  1352 I SystemConfig: Reading permissions from /system/etc/sysconfig/preinstalled-packages-platform.xml
05-25 09:15:27.778  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/DigitalWellbeing.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.778  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/GoogleContacts.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.778  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/GoogleContactsSyncAdapter.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.778  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/Drive.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.778  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/GoogleMaps.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.778  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.intentresolver.xml
05-25 09:15:27.778  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/GoogleCalendar.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.779  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/CarrierServices.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.779  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/GoogleDialer.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.779  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/GoogleOneTimeInitializer.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.779  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.documentsui.xml
05-25 09:15:27.779  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/app.grapheneos.networklocation.xml
05-25 09:15:27.780  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/DeviceHealthServices.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.780  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/ExtraFiles.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.780  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/org.apache.http.legacy.xml
05-25 09:15:27.780  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.live_wallpaper.xml
05-25 09:15:27.781  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/GooglePlayStore.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.781  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/privapp-permissions-mediatek.xml
05-25 09:15:27.781  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/GBoard.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.781  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/GoogleKeep.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.781  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/GoogleRestore.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.781  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.nfc_extras.xml
05-25 09:15:27.782  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.networkstack.xml
05-25 09:15:27.782  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.credentials.xml
05-25 09:15:27.783  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/privapp-permissions-platform.xml
05-25 09:15:27.785  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/android.test.mock.xml
05-25 09:15:27.785  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/android.test.base.xml
05-25 09:15:27.785  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.webview.xml
05-25 09:15:27.786  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/android.hardware.biometrics.face.xml
05-25 09:15:27.786  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/GoogleMessages.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.786  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/app.grapheneos.logviewer.xml
05-25 09:15:27.786  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/GoogleServicesFramework.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.787  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/GoogleCalendarSyncAdapter.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.787  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/javax.obex.xml
05-25 09:15:27.787  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.location.provider.xml
05-25 09:15:27.788  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/GoogleLocationHistory.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.788  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/android.test.runner.xml
05-25 09:15:27.788  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/GooglePhotos.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.788  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.media.remotedisplay.xml
05-25 09:15:27.789  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.sip.voip.xml
05-25 09:15:27.789  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/android.software.window_magnification.xml
05-25 09:15:27.789  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/GoogleClock.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.789  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/GoogleCalculator.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.789  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.future.usb.accessory.xml
05-25 09:15:27.790  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/com.android.mediadrm.signer.xml
05-25 09:15:27.790  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/privapp_whitelist_co.aospa.sense.xml
05-25 09:15:27.790  1321  1352 I SystemConfig: Non-xml file /system/etc/permissions/GmsCore.prop in /system/etc/permissions directory, ignoring
05-25 09:15:27.790  1321  1352 I SystemConfig: Reading permissions from /system/etc/permissions/platform.xml
05-25 09:15:27.792  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.ese.xml
05-25 09:15:27.793  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.xml
05-25 09:15:27.793  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.location.gps.xml
05-25 09:15:27.794  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.multitouch.distinct.xml
05-25 09:15:27.794  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.verified_boot.xml
05-25 09:15:27.795  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.mediatek.hardware.vow.xml
05-25 09:15:27.795  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.accelerometer.xml
05-25 09:15:27.796  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.bluetooth_le.xml
05-25 09:15:27.796  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.flash-autofocus.xml
05-25 09:15:27.797  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.stepcounter.xml
05-25 09:15:27.797  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.gyroscope.xml
05-25 09:15:27.797  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.wifi.passpoint.xml
05-25 09:15:27.798  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.telephony.ims.xml
05-25 09:15:27.798  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.bluetooth.xml
05-25 09:15:27.798  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.raw.xml
05-25 09:15:27.799  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.android.nfc_extras.xml
05-25 09:15:27.799  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.vulkan.version.xml
05-25 09:15:27.799  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.front.xml
05-25 09:15:27.800  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.hifi_sensors.xml
05-25 09:15:27.800  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.opengles.deqp.level.xml
05-25 09:15:27.801  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.xml
05-25 09:15:27.801  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/nfc_features.xml
05-25 09:15:27.801  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.usb.accessory.xml
05-25 09:15:27.802  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.telephony.gsm.xml
05-25 09:15:27.802  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.wifi.xml
05-25 09:15:27.802  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.freeform_window_management.xml
05-25 09:15:27.803  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.hcef.xml
05-25 09:15:27.803  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.midi.xml
05-25 09:15:27.804  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.usb.host.xml
05-25 09:15:27.804  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.fingerprint.xml
05-25 09:15:27.804  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.multitouch.jazzhand.xml
05-25 09:15:27.805  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.multitouch.xml
05-25 09:15:27.805  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.ipsec_tunnels.xml
05-25 09:15:27.805  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.se.omapi.uicc.xml
05-25 09:15:27.806  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.opengles.aep.xml
05-25 09:15:27.806  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.se.omapi.ese.xml
05-25 09:15:27.806  1321  1321 I SystemServiceManager: Starting com.android.server.security.FileIntegrityService
05-25 09:15:27.806  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/handheld_core_hardware.xml
05-25 09:15:27.807  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.nfc.hce.xml
05-25 09:15:27.807  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.faketouch.xml
05-25 09:15:27.808  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.light.xml
05-25 09:15:27.808  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.audio.low_latency.xml
05-25 09:15:27.808  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.software.vulkan.deqp.level.xml
05-25 09:15:27.808  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.vulkan.level.xml
05-25 09:15:27.809  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.touchscreen.xml
05-25 09:15:27.809  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.vulkan.compute.xml
05-25 09:15:27.809  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.wifi.direct.xml
05-25 09:15:27.810  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.nxp.mifare.xml
05-25 09:15:27.810  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.stepdetector.xml
05-25 09:15:27.810  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.compass.xml
05-25 09:15:27.811  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.sensor.proximity.xml
05-25 09:15:27.811  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/com.mediatek.hardware.vow_dsp.xml
05-25 09:15:27.811  1321  1352 I SystemConfig: Reading permissions from /vendor/etc/permissions/android.hardware.camera.full.xml
05-25 09:15:27.812  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-overlays.xml
05-25 09:15:27.813  1321  1321 I SystemServiceManager: Starting com.android.server.pm.Installer
05-25 09:15:27.813  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/GoogleCamera_6gb_or_more_ram.xml
05-25 09:15:27.814  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/nga.xml
05-25 09:15:27.815  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-org.lineageos.glimpse.xml
05-25 09:15:27.815  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-google-install-constraints-package-allowlist.xml
05-25 09:15:27.816  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/quick_tap.xml
05-25 09:15:27.816  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/initial-package-stopped-states-org.lineageos.etar.xml
05-25 09:15:27.816  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/google.xml
05-25 09:15:27.817  1321  1352 I SystemConfig: Adding association: com.google.android.as <- com.android.bluetooth.services
05-25 09:15:27.817  1321  1352 I SystemConfig: Adding association: com.google.android.as <- com.google.android.bluetooth.services
05-25 09:15:27.818  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/pixel_experience_2017.xml
05-25 09:15:27.819  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-voltage-product.xml
05-25 09:15:27.819  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/pixel_2016_exclusive.xml
05-25 09:15:27.820  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/adaptivecharging.xml
05-25 09:15:27.820  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-handheld-product.xml
05-25 09:15:27.821  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/com.android.messaging.allowlist.xml
05-25 09:15:27.821  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-google-hiddenapi-package-whitelist.xml
05-25 09:15:27.821  1321  1321 I SystemServiceManager: Starting com.android.server.os.DeviceIdentifiersPolicyService
05-25 09:15:27.822  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/voltage-component-overrides.xml
05-25 09:15:27.822  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-google-staged-installer-whitelist.xml
05-25 09:15:27.823  1321  1321 I SystemServiceManager: Starting com.android.server.flags.FeatureFlagsService
05-25 09:15:27.823  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/google_build.xml
05-25 09:15:27.823  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-org.lineageos.etar.xml
05-25 09:15:27.823  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/com.android.deskclock_allowlist.xml
05-25 09:15:27.824  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/dreamliner.xml
05-25 09:15:27.824  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/initial-package-stopped-states-org.lineageos.glimpse.xml
05-25 09:15:27.824  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/initial-package-stopped-states-org.lineageos.aperture.xml
05-25 09:15:27.825  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/NikGapps-preinstalled-packages-product-pixel-2017-and-newer.xml
05-25 09:15:27.825  1321  1321 I SystemServiceManager: Starting com.android.server.uri.UriGrantsManagerService$Lifecycle
05-25 09:15:27.825  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/d2d_cable_migration_feature.xml
05-25 09:15:27.825  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-platform-telephony-product.xml
05-25 09:15:27.826  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/org.lineageos.etar.allowlist.xml
05-25 09:15:27.826  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/preinstalled-packages-org.lineageos.aperture.xml
05-25 09:15:27.827  1321  1352 I SystemConfig: Reading permissions from /product/etc/sysconfig/nexus.xml
05-25 09:15:27.827  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.maps.xml
05-25 09:15:27.828  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.widevine.software.drm.xml
05-25 09:15:27.829  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/privapp-permissions-hotword.xml
05-25 09:15:27.829  1321  1321 I SystemServiceManager: Starting com.android.server.powerstats.PowerStatsService
05-25 09:15:27.830  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/android.software.freeform_window_management.xml
05-25 09:15:27.830  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.gms.xml
05-25 09:15:27.831  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/privapp-permissions-googleapps-turbo.xml
05-25 09:15:27.832  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.wellbeing.xml
05-25 09:15:27.833  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/privapp-permissions-xhotword.xml
05-25 09:15:27.833  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.onetimeinitializer.xml
05-25 09:15:27.834  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.dialer.support.xml
05-25 09:15:27.834  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.keep.xml
05-25 09:15:27.835  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-split-permissions-google.xml
05-25 09:15:27.835  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.dialer.xml
05-25 09:15:27.836  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-privapp-permissions-google-p.xml
05-25 09:15:27.836   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.power.stats@1.0::IPowerStats/default in either framework or device VINTF manifest.
05-25 09:15:27.836  1321  1321 E PowerStatsService: Unable to get power.stats HAL service.
05-25 09:15:27.836  1321  1321 E PowerStatsService: nativeInit failed to connect to power.stats HAL
05-25 09:15:27.839  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.turbo.xml
05-25 09:15:27.839  1321  1321 I HidlServiceManagement: Registered android.frameworks.stats@1.0::IStats/default
05-25 09:15:27.839  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.vending.xml
05-25 09:15:27.840  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/privapp_whitelist_com.android.dialer-ext.xml
05-25 09:15:27.841  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.dialer.xml
05-25 09:15:27.841  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.media.effects.xml
05-25 09:15:27.842  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.ims.xml
05-25 09:15:27.842  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.messaging.xml
05-25 09:15:27.843  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/android.software.angle.xml
05-25 09:15:27.843  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.imsserviceentitlement.xml
05-25 09:15:27.844  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.settings.intelligence.xml
05-25 09:15:27.844  1321  1321 I SystemServiceManager: Starting com.android.server.permission.access.AccessCheckingService
05-25 09:15:27.844  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-privapp-permissions-google-se.xml
05-25 09:15:27.845  1321  1321 I SystemServiceManager: Starting com.android.server.wm.ActivityTaskManagerService$Lifecycle
05-25 09:15:27.846  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/NikGapps-privapp-permissions-google.xml
05-25 09:15:27.847  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/com.android.contacts.xml
05-25 09:15:27.847  1321  1352 I SystemConfig: Reading permissions from /product/etc/permissions/com.google.android.apps.restore.xml
05-25 09:15:27.848  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/sysconfig/allowlist_com.stevesoltys.seedvault.xml
05-25 09:15:27.848  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/sysconfig/android.telephony.satellite.xml
05-25 09:15:27.849  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.emergency.xml
05-25 09:15:27.850  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_whitelist_com.android.launcher3-ext.xml
05-25 09:15:27.850  1321  1321 I SystemServiceManager: Starting com.android.server.am.ActivityManagerService$Lifecycle
05-25 09:15:27.851  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_allowlist_com.libremobileos.freeform.xml
05-25 09:15:27.851  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.hotwordenrollment.common.util.xml
05-25 09:15:27.852  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/permissions_com.stevesoltys.seedvault.xml
05-25 09:15:27.852  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_whitelist_org.lineageos.setupwizard.xml
05-25 09:15:27.854  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/androidx.window.sidecar.xml
05-25 09:15:27.854  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.bluetooth.bthelper.xml
05-25 09:15:27.855  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_whitelist_io.chaldeaprjkt.gamespace.xml
05-25 09:15:27.855  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/android.software.theme_picker.xml
05-25 09:15:27.855  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.systemui.xml
05-25 09:15:27.856  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.settings.xml
05-25 09:15:27.856  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.carrierconfig.xml
05-25 09:15:27.857  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/androidx.window.extensions.xml
05-25 09:15:27.857  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp_allowlist_com.libremobileos.sidebar.xml
05-25 09:15:27.857  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/privapp-permissions-custom.xml
05-25 09:15:27.858  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.launcher3.xml
05-25 09:15:27.858  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.google.android.gsf.xml
05-25 09:15:27.859  1321  1352 I SystemConfig: Reading permissions from /system_ext/etc/permissions/com.android.storagemanager.xml
05-25 09:15:27.860  1321  1352 I SystemConfig: Reading permissions from /apex/com.android.tethering/etc/permissions/permissions.xml
05-25 09:15:27.861  1321  1352 I SystemConfig: Reading permissions from /apex/com.android.cellbroadcast/etc/permissions/com.android.cellbroadcastreceiver.module.xml
05-25 09:15:27.861  1321  1352 I SystemConfig: Reading permissions from /apex/com.android.cellbroadcast/etc/permissions/com.android.cellbroadcastservice.xml
05-25 09:15:27.863  1321  1352 I SystemConfig: Reading permissions from /apex/com.android.adservices/etc/permissions/com.android.adservices.api.xml
05-25 09:15:27.863  1321  1352 I SystemConfig: Reading permissions from /apex/com.android.mediaprovider/etc/permissions/com.android.photopicker.xml
05-25 09:15:27.864  1321  1352 I SystemConfig: Reading permissions from /apex/com.android.mediaprovider/etc/permissions/com.android.providers.media.module.xml
05-25 09:15:27.865  1321  1352 I SystemConfig: Reading permissions from /apex/com.android.nfcservices/etc/permissions/com.android.nfc.xml
05-25 09:15:27.866  1321  1352 I SystemConfig: Reading permissions from /apex/com.android.extservices/etc/permissions/android.ext_sminus.services.xml
05-25 09:15:27.867  1321  1352 I SystemConfig: Reading permissions from /apex/com.android.ipsec/etc/permissions/android.net.ipsec.ike.xml
05-25 09:15:27.868  1321  1352 I SystemConfig: Reading permissions from /apex/com.android.permission/etc/permissions/com.android.permissioncontroller.xml
05-25 09:15:27.869  1321  1352 I SystemConfig: Reading permissions from /apex/com.android.healthfitness/etc/permissions/com.android.healthconnect.controller.xml
05-25 09:15:27.869  1321  1352 I SystemConfig: Reading permissions from /apex/com.android.devicelock/etc/permissions/com.android.devicelockcontroller.xml
05-25 09:15:27.870  1321  1352 I SystemConfig: Reading permissions from /apex/com.android.apex.cts.shim/etc/permissions/signature-permission-allowlist.xml
05-25 09:15:27.871  1321  1352 I SystemConfig: Reading permissions from /apex/com.android.btservices/etc/permissions/com.android.bluetooth.xml
05-25 09:15:27.871  1321  1352 I incfs   : Initial API level of the device: 30
05-25 09:15:27.873  1321  1360 E system_server: memevent deregister all events failed, failure to initialize
05-25 09:15:27.873  1321  1360 E OomConnection: failed waiting for OOM events: java.lang.RuntimeException: Failed to initialize memevents listener
05-25 09:15:27.988  1321  1337 W android.permission.PermissionManager: Missing ActivityManager; assuming 1047 does not hold android.permission.MANAGE_APP_OPS_MODES
05-25 09:15:27.990   880   880 W ServiceManagerCppClient: Waited one second for activity (is service started? Number of threads started in the threadpool: 16. Are binder threads started and available?)
05-25 09:15:27.991  1321  1321 I SystemServiceManager: Starting com.android.server.pm.DataLoaderManagerService
05-25 09:15:28.001  1321  1321 I SystemServiceManager: Starting com.android.server.power.PowerManagerService
05-25 09:15:28.006  1321  1321 I PowerHalLoader: Successfully connected to Power HAL AIDL service.
05-25 09:15:28.009  1321  1321 I SystemServiceManager: Starting com.android.server.power.ThermalManagerService
05-25 09:15:28.014  1321  1321 I SystemServiceManager: Starting com.android.server.recoverysystem.RecoverySystemService$Lifecycle
05-25 09:15:28.014   901   901 I ccci_mdinit: (1):MD1 set status: mtk.md1.status=ready 
05-25 09:15:28.015   901   901 I ccci_mdinit: (1):start_service init.svc.emdlogger1, but returned 0, maybe has no this property
05-25 09:15:28.015   901   901 I ccci_mdinit: (1):wait_for_property: is_stop_wait: 0
05-25 09:15:28.017  1321  1321 I SystemServiceManager: Starting com.android.server.lights.LightsService
05-25 09:15:28.020  1321  1321 I SystemServiceManager: Starting com.android.server.display.DisplayManagerService
05-25 09:15:28.025   901   901 I ccci_mdinit: (1):start_service init.svc.vendor.gsm0710muxd, but returned 0, maybe has no this property
05-25 09:15:28.030   901   901 I ccci_mdinit: (1):wait_for_property: is_stop_wait: 0
05-25 09:15:28.030   901   901 I ccci_mdinit: (1):wait_for_property:success(init.svc.vendor.gsm0710muxd=running), loop:600
05-25 09:15:28.055  1321  1321 I SystemServiceManager: Starting phase 100
05-25 09:15:28.096  1321  1321 I UserManagerService: Upgrading users from userVersion 11 to 11
05-25 09:15:28.120  1004  1124 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-25 09:15:28.120  1004  1124 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:2
05-25 09:15:28.247  1321  1321 W android.permission.PermissionManager: Missing ActivityManager; assuming 1000 holds android.permission.SET_PREFERRED_APPLICATIONS
05-25 09:15:28.285  1321  1321 W PackageManager: No package known for package restrictions com.android.adservices
05-25 09:15:28.300  1321  1321 W PackageManager: No package known for package restrictions com.android.permission
05-25 09:15:28.346  1321  1321 W PackageManager: No package known for package restrictions com.android.btservices
05-25 09:15:28.407  1321  1321 W PackageManager: No package known for package restrictions com.android.extservices
05-25 09:15:28.420  1321  1321 W PackageManager: No package known for package restrictions com.android.nfcservices
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.noCutout on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.font.sanfrancisco on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package org.omnirom.omnijaws on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.android.systemui.clocks.metro on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package in.zeta.android on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.android.cts.priv.ctsshim on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package org.voltage.theme.font.dosis on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.google.android.youtube on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.android.uwb.resources on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.nikgapps.overlay.messages on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.corner on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.android.adservices.api on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.double on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.themepicker on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.rifsxd.ksunext on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package app.grapheneos.gmscompat.config on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.settings on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.truecaller on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.android.healthconnect.controller on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.manhwabuddy on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.luascans on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.xperia.settings on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.gradicon.android on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.google.android.onetimeinitializer on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.android.health.connect.backuprestore on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.android.virtualmachine.res on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.mxtech.videoplayer.pro on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.systemui on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.android.managedprovisioning.auto_generated_rro_product__ on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package org.omnirom.omnijaws.auto_generated_rro_product__ on user 0
05-25 09:15:28.426  1321  1321 W PackageSettings: Missing permission state for package com.apkupdater on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.settings on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.narrow on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.systemui on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.systemui on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.settings on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.android on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.documentsui on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.externalstorage on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.server.deviceconfig.resources on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.settings on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.nikgapps.overlay.googlelocationhistory on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.whatsapp on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.companiondevicemanager on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.coderstory.toolkit on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package io.github.jica98 on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.gourmetscans on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package app.grapheneos.logviewer on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.systemui on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.systemui.auto_generated_rro_product__ on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.google.android.apps.messaging on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.mediatek.engineermode on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.federatedcompute.services on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.android on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.carrierconfig.mt6893 on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package io.chaldeaprjkt.gamespace.auto_generated_rro_product__ on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.lonelycatgames.Xplore on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.system.monet.snowpaintdrop on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.scyllascans on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.nikgapps.overlay.googlephotos on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.xperia.systemui on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package app.komikku on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package jp.pxv.android on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.systemui on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.themepicker on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package net.thunderbird.android on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.systemui.clocks.bignum on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.avatarpicker on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.xayah.databackup.foss on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.systemui on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.font.rookery on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.snowmtl on user 0
05-25 09:15:28.427  1321  1321 W PackageSettings: Missing permission state for package com.android.systemui.plugin.globalactions.wallet on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.safetycenter.resources on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package org.zwanoo.android.speedtest on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.system.monet.vivid on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.vending on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.pacprocessor on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.simappdialog on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package org.protonaosp.deviceconfig.auto_generated_rro_product__ on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package eu.darken.sdmse on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.systemui on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.systemui.clocks.growth on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.connectivity.resources on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.hole on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.tall on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.wide on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.ancient.telephonyoverlay on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.networkstack.overlay on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package org.lineageos.glimpse.frameworksbaseoverlay on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.modulemetadata on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.certinstaller on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.carrierconfig on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.launcher on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.lorn.android on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.internal.systemui.navbar.threebutton on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.brave.browser on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.aurorascans on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.talkback on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.wifi.dialog on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.nikgapps.overlay.gmscore on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.hotwordenrollment.xgoogle on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.oneplusparts.overlay.rm on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package ru.mike.updatelocker on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.launcher on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.launcher on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.philiascans on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package co.aospa.sense.auto_generated_rro_product__ on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.kewnscans on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.shojoscans on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.settings on user 0
05-25 09:15:28.428  1321  1321 W PackageSettings: Missing permission state for package org.lineageos.cupida.frameworkresoverlay on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.android on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package ru.andr7e.deviceinfohw on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.android.egg on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.android.launcher3 on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package livio.pack.lang.en_US on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.google.android.trichromelibrary_710306033 on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package org.lineageos.settings.doze.overlay on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.android.backupconfirm on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.font.fluidsans on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.axiel7.anihyou on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.android.settings.auto_generated_rro_vendor__ on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package org.voltage.theme.font.opposans on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.android.systemui.auto_generated_rro_vendor__ on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.google.android.deskclock on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package org.protonaosp.deviceconfig on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.wmods.wppenhacer on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.android.systemui.clocks.numoverlap on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.android.statementservice on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.xperia.android on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.google.android.gm on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package org.calyxos.backup.contacts on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.launcher on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.voltageos.colorstub on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.webtoons on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.oplus.doze.overlay_system on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.settings on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.android.settings.intelligence on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.android.systemui.clocks.calligraphy on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.android.systemui.accessibility.accessibilitymenu on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package org.voltage.theme.font.linotte on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.oplus.doze.overlay_systemui on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.themepicker on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package org.adaway on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.zeptoconsumerapp on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.f0x1d.logfox on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.ancient.frameworkresoverlay.mt6893 on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package ru.tech.imageresizershrinker on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.mangademon on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.ancient.systemuioverlay.mt6893 on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package org.lineageos.setupwizard.auto_generated_rro_product__ on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.android.sharedstoragebackup on user 0
05-25 09:15:28.429  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.launcher on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.printspooler on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.hotwordenrollment.okgoogle on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.emergency.auto_generated_rro_product__ on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.settings on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.dreams.basic on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.settings.overlay.oplus.target on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.launcher on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.providers.settings.auto_generated_rro_product__ on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package org.mozilla.focus on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.photopicker on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.systemui on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.webview on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.permissioncontroller.overlay on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package app.grapheneos.networklocation on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.google.android.apps.wellbeing on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.coffeemanga on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.rkpdapp on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.google.android.dialer on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.launcher on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.bips on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.oos.themepicker on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.settings on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.intentresolver.auto_generated_rro_product__ on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.outline.android on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package org.eu.droid_ng.jellyfish on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.musicfx on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package app.vitune.android on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.google.android.apps.docs on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package ellipi.messenger on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.systemui on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.asurascans on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package app.grapheneos.gmscompat.lib on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package duy.com.text_converter on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.customization.themes on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.font.googlesans on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package net.one97.paytm on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.cellbroadcastreceiver on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.google.android.webview on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package android.ext.shared on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.android.bluetooth.bthelper.auto_generated_rro_product__ on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.google.android.contactkeys on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.google.android.contacts on user 0
05-25 09:15:28.430  1321  1321 W PackageSettings: Missing permission state for package com.google.android.syncadapters.contacts on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.system.monet.expresso on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.nikgapps.overlay.googleclock on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package org.calyxos.datura on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.themepicker on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.systemui.clocks.inflate on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.google.android.calculator on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.adultwebtoon on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package org.voltage.theme.font.manrope on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.printservice.recommendation on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package app.grapheneos.AppCompatConfig on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package me.jmh.authenticatorpro on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.systemui on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.mangadex on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.kaiscans on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.google.android.gms on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.google.android.ims on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.system.theme.black on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package android.ext.services on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.wifi.resources on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.plumpy.systemui on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.cameraextensions on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.packageinstaller on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.carrierdefaultapp on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.magusmanga on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.gradicon.systemui on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.necroscans on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.font.opsans on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.batoto on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.credentialmanager on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.android on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.font.notoserifsource on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.android on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.proxyhandler on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.launcher3.auto_generated_rro_product__ on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.waterfall on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.intentresolver on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.systemui on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package io.github.muntashirakon.AppManager on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.internal.systemui.navbar.transparent on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.providers.settings.overlay on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.android on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.google.android.apps.photos on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.android on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package com.android.managedprovisioning on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.aeinscans on user 0
05-25 09:15:28.431  1321  1321 W PackageSettings: Missing permission state for package io.github.dovecoteescapee.byedpi on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.lorn.systemui on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.dreams.phototable on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package org.lineageos.settings.doze.auto_generated_rro_product__ on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.networkstack.tethering.mt6893 on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.kai.launcher on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package org.voltage.theme.font.recursive_casual on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package air.kukulive.mailnow on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.looker.droidify on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.android on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.smspush on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.role.notes.enabled on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.berdik.letmedowngrade on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.wallpaper.livepicker on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package org.lineageos.aperture on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.cellbroadcastreceiver.module on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.systemui.clocks.flex on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.apps.tag on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.inputmethod.latin.auto_generated_rro_product__ on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.power.hub.udfps.icons on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.imsserviceentitlement on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.appsearch.apk on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.launcher on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.valirscans on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package org.voltage.theme.font.recursive_linear on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.internal.display.cutout.emulation.avoidAppsInCutout on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package org.lineageos.cupida.wifioverlay on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package idm.internet.download.manager.plus on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.plumpy.android on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.arvenscans on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.melody on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.android on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.storagemanager on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.zerodha.kite3 on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.android.bookmarkprovider on user 0
05-25 09:15:28.432  1321  1321 W PackageSettings: Missing permission state for package com.fitbit.FitbitMobile on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.aurora.systemui on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.launcher on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package uk.akane.omni on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package org.protonaosp.theme.font.linotte on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.google.android.apps.googlecamera.fishfood on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.voltage.overlay.customization.keyboard.nonavbar on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.google.android.apps.turbo on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.enryumanga on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.google.android.safetycore on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.whalemanga on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package app.grapheneos.gmscompat on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.pui.themepicker on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.android.wifi.resources.mt6893 on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package proton.android.pass on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.launcher on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.android.wallpaper on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.nikgapps.overlay.turbo on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.android.vpndialogs on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.goping.user on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.nyxscans on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.google.android.keep on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.android.angle on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.mangareadorg on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.linkbox.plus.android on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.filled.themepicker on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.android.sdksandbox on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.android.wallpaperbackup on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.stevesoltys.seedvault.auto_generated_rro_product__ on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.android.settings.auto_generated_rro_product__ on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.voltageos.Covers on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.android.providers.media.module on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.power.hub.udfps.animations on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package in.swiggy.android on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.android.hotspot2.osulogin on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.all.solarmtl on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.google.android.gms.location.history on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.intsig.camscanner on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.android.internal.systemui.navbar.gestural on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package co.aospa.sense.settings.overlay on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.victor.themepicker on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package com.wstxda.viper4android on user 0
05-25 09:15:28.433  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.harimanga on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.mangadistrict on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.android.bluetoothmidiservice on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.ancient.settingsoverlay.mt6893 on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package org.akanework.gramophone on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.android.permissioncontroller on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.android.storagemanager.auto_generated_rro_product__ on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.zerodha.coin on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.android.dialer.auto_generated_rro_vendor__ on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package app.customerportal.tachyon1 on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.android.imsserviceentitlement.auto_generated_rro_product__ on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.android.phone.auto_generated_rro_product__ on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package android.auto_generated_rro_product__ on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.ezmanga on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.templescan on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.android.dialer.auto_generated_rro_product__ on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.nostalgic.settings on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.sam.themepicker on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.android.ondevicepersonalization.services on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.android.documentsui.overlay on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.anisascans on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.android.captiveportallogin on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.aurora.android on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.android.devicelockcontroller on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.acherus.settings on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.tukann.confinedandhorny on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.rounded.settings on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package eu.kanade.tachiyomi.extension.en.likemanga on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.nikgapps.overlay.wellbeing on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.nikgapps.overlay.dialer on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.google.android.inputmethod.latin on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package org.lineageos.aperture.frameworksbaseoverlay on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.nikgapps.overlay.contacts on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package android.auto_generated_rro_vendor__ on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.android.theme.icon_pack.circular.android on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for package com.google.android.apps.restore on user 0
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for shared user: android.uid.log
05-25 09:15:28.434  1321  1321 W PackageSettings: Missing permission state for shared user: android.uid.uwb
05-25 09:15:28.466  1408  1408 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/slot1
05-25 09:15:28.466  1408  1413 E SchedPolicy: open of /dev/cpuctl/bg_non_interactive/tasks failed: No such file or directory
05-25 09:15:28.468  1408  1408 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSlot1
05-25 09:15:28.472  1408  1416 I RmcVsim : [0] RmcVsimUrcHandler init slot: 0, ch id 0
05-25 09:15:28.473  1408  1408 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/imsAospSlot1
05-25 09:15:28.473  1408  1418 I RmcVsim : [1] RmcVsimUrcHandler init slot: 1, ch id 0
05-25 09:15:28.474  1408  1408 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/imsSlot1
05-25 09:15:28.475  1408  1408 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/se1
05-25 09:15:28.476  1408  1408 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSe1
05-25 09:15:28.478  1408  1408 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/em1
05-25 09:15:28.479  1408  1408 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkEm1
05-25 09:15:28.479  1408  1408 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkAssist1
05-25 09:15:28.482  1408  1408 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRcs1
05-25 09:15:28.488  1408  1413 I WpfaCppUtils: initialRuleContainer!
05-25 09:15:28.490  1408  1408 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkCap1
05-25 09:15:28.493  1408  1441 I RmcDcImsDc2ReqHandler: [0][handleImsBearerNotify] urc=+EIMSPDN: "init"
05-25 09:15:28.494  1408  1413 I WpfaCppUtils: initialA2MRingBuffer!
05-25 09:15:28.494  1408  1408 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSmartRatSwitch1
05-25 09:15:28.512  1408  1408 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRsu1
05-25 09:15:28.512  1408  1413 W mtkfusionrild: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.system.suspend.ISystemSuspend/default
05-25 09:15:28.514  1408  1408 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/slot2
05-25 09:15:28.515  1408  1408 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSlot2
05-25 09:15:28.516  1408  1408 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/imsAospSlot2
05-25 09:15:28.517  1408  1408 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/imsSlot2
05-25 09:15:28.519  1408  1486 I RmcDcImsDc2ReqHandler: [1][handleImsBearerNotify] urc=+EIMSPDN: "init"
05-25 09:15:28.524  1408  1408 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/se2
05-25 09:15:28.525  1408  1408 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSe2
05-25 09:15:28.536  1408  1408 I HidlServiceManagement: Registered android.hardware.radio@1.6::IRadio/em2
05-25 09:15:28.537  1408  1408 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkEm2
05-25 09:15:28.540  1408  1408 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkAssist2
05-25 09:15:28.549  1408  1408 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRcs2
05-25 09:15:28.551  1408  1408 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkCap2
05-25 09:15:28.552  1408  1408 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkSmartRatSwitch2
05-25 09:15:28.558  1408  1408 I HidlServiceManagement: Registered vendor.mediatek.hardware.mtkradioex@3.0::IMtkRadioEx/mtkRsu2
05-25 09:15:28.559  1408  1408 I RILC-OplusRadioAIDL: radio::registerService starting IOplusRadio vendor.oplus.hardware.radio.IRadioStable/OplusRadio0
05-25 09:15:28.560  1408  1408 I RILC-OplusRadioAIDL: radio::registerService IOplusRadio status: 0
05-25 09:15:28.560  1408  1408 I RILC-OplusRadioAIDL: radio::registerService starting IOplusRadio vendor.oplus.hardware.radio.IRadioStable/OplusRadio1
05-25 09:15:28.560  1408  1408 I RILC-OplusRadioAIDL: radio::registerService IOplusRadio status: 0
05-25 09:15:28.560  1408  1408 I RILC-OplusAppRadio: radio::registerService starting IOplusAppRadio oplus_app_slot1
05-25 09:15:28.561  1408  1408 I HidlServiceManagement: Registered vendor.oplus.hardware.appradio@1.0::IOplusAppRadio/oplus_app_slot1
05-25 09:15:28.562  1408  1408 I RILC-OplusAppRadio: radio::registerService IOplusAppRadio status: 0
05-25 09:15:28.562  1408  1408 I RILC-OplusAppRadio: radio::registerService starting IOplusAppRadio oplus_app_slot2
05-25 09:15:28.563  1408  1408 I HidlServiceManagement: Registered vendor.oplus.hardware.appradio@1.0::IOplusAppRadio/oplus_app_slot2
05-25 09:15:28.563  1408  1408 I RILC-OplusAppRadio: radio::registerService IOplusAppRadio status: 0
05-25 09:15:28.565  1408  1408 I HidlServiceManagement: Registered android.hardware.radio.config@1.3::IRadioConfig/default
05-25 09:15:28.567  1321  1321 I PackageManager: /system/apex/com.android.btservices.apex changed; collecting certs
05-25 09:15:28.567  1408  1408 I HidlServiceManagement: Registered android.hardware.radio@1.2::ISap/slot1
05-25 09:15:28.569  1321  1321 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-25 09:15:28.569  1408  1408 I HidlServiceManagement: Registered android.hardware.radio@1.2::ISap/slot2
05-25 09:15:28.579  1321  1321 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-25 09:15:28.600  1321  1321 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-25 09:15:28.602   999   999 I AVSync  : avInit, st 1e7d092ae, int=8, frac=2f341eb9
05-25 09:15:28.602   999   999 I vtservice: [VT][SRV]after VTService_instantiate
05-25 09:15:28.606   999  1495 I AVSync  : avInit, st 1e8108278, int=8, frac=30470c37
05-25 09:15:28.610   851  1264 I VT HIDL : [IVT] [VT THREAD] [VT_Bind] des = volte_imsvt1 initialize communication
05-25 09:15:28.622  1321  1321 I PackageManager: /data/apex/decompressed/<EMAIL> changed; collecting certs
05-25 09:15:28.637  1321  1332 I system_server: Compiler allocated 4688KB to compile com.android.server.pm.ScanResult com.android.server.pm.ScanPackageUtils.scanPackageOnly(com.android.server.pm.ScanRequest, com.android.server.pm.PackageManagerServiceInjector, boolean, long)
05-25 09:15:28.847  1321  1469 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-25 09:15:28.847  1321  1469 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-25 09:15:28.847  1321  1469 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-25 09:15:28.859  1321  1469 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-25 09:15:28.864  1321  1469 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-25 09:15:28.864  1321  1469 W PackageParsing: android defines a knownSigner permission but the provided knownCerts resource is null
05-25 09:15:28.877  1408  1457 E libmnlUtils: No action: deInitReaderLoop can't get mMnlsocket
05-25 09:15:28.877  1408  1457 I wpfa    : initReaderLoop() done, buf_size=67583
05-25 09:15:28.877  1408  1457 I wpfa    : WPFA_DL initialized
05-25 09:15:28.893  1321  1470 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.CAMERA in package: com.mediatek.engineermode at: Binary XML file line #30
05-25 09:15:28.971  1321  1469 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.SATELLITE_COMMUNICATION in package: com.android.shell at: Binary XML file line #775
05-25 09:15:28.973  1321  1469 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE in package: com.android.shell at: Binary XML file line #874
05-25 09:15:28.973  1321  1469 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE_SYSTEM_EXEMPTED in package: com.android.shell at: Binary XML file line #875
05-25 09:15:28.991   880   880 W ServiceManagerCppClient: Waited one second for activity (is service started? Number of threads started in the threadpool: 16. Are binder threads started and available?)
05-25 09:15:29.010  1321  1468 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.POST_NOTIFICATIONS in package: com.android.phone at: Binary XML file line #171
05-25 09:15:29.120  1004  1124 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-25 09:15:29.120  1004  1124 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:3
05-25 09:15:29.137  1016  1016 I KEYFILE : [INFO   ] CustomCommon.cpp:161 init() ro.vendor.config.oplus.low_ram = 0
05-25 09:15:29.140  1016  1016 I KEYFILE : [INFO   ] CustomCommon.cpp:162 init() vendor.debug.camera.bss.aishutter.weighting = 100,98,96,94,92,90,90,90
05-25 09:15:29.141  1016  1016 I KEYFILE : [INFO   ] CustomCommon.cpp:163 init() vendor.debug.tpi.s.semi.run = 0
05-25 09:15:29.142  1016  1016 I KEYFILE : [INFO   ] CustomCommon.cpp:164 init() vendor.debug.camera.FDAsync = true
05-25 09:15:29.142  1016  1016 E KEYFILE : [ERROR   ] CustomMetadata.cpp:375 init() PROP_SYS_CAM_PACKNAME err 0!
05-25 09:15:29.142  1016  1016 I KEYFILE : [INFO   ] CustomerData.cpp:93 init() 0xb40000718d7e7900, size: 288 byte
05-25 09:15:29.146  1408  1413 E RILC-OplusRadioAIDL: oplusRadioServiceAIDL: oplusRadioServiceAIDL[0]->mRadioIndicationOplus == NULL
05-25 09:15:29.168  1321  1321 W PackageManager: Failed to scan /product/priv-app/CarrierServices: Package com.google.android.ims at /product/priv-app/CarrierServices ignored: updated version 31144015 better than this 30939330
05-25 09:15:29.322  1408  1413 E RILC-OplusRadioAIDL: oplusRadioServiceAIDL: oplusRadioServiceAIDL[1]->mRadioIndicationOplus == NULL
05-25 09:15:29.775  1321  1470 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.CONFIGURE_WIFI_DISPLAY in package: com.android.systemui at: Binary XML file line #174
05-25 09:15:29.776  1321  1470 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.GET_RUNTIME_PERMISSIONS in package: com.android.systemui at: Binary XML file line #252
05-25 09:15:29.776  1321  1470 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.READ_DEVICE_CONFIG in package: com.android.systemui at: Binary XML file line #372
05-25 09:15:29.776  1321  1470 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.MODIFY_AUDIO_SETTINGS in package: com.android.systemui at: Binary XML file line #405
05-25 09:15:29.777  1321  1470 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FORCE_STOP_PACKAGES in package: com.android.systemui at: Binary XML file line #426
05-25 09:15:29.895  1321  1321 I ApexManager: Registering com.android.safetycenter.resources as apk-in-apex of com.android.permission
05-25 09:15:29.934  1321  1321 I ApexManager: Registering com.android.permissioncontroller as apk-in-apex of com.android.permission
05-25 09:15:29.947  1321  1468 W PackageParsing: Unknown element under <manifest>: meta-data at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #102
05-25 09:15:29.947  1321  1468 W PackageParsing: Unknown element under <manifest>: provider at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #106
05-25 09:15:29.947  1321  1468 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #116
05-25 09:15:29.947  1321  1468 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #122
05-25 09:15:29.947  1321  1468 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #129
05-25 09:15:29.947  1321  1468 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #135
05-25 09:15:29.947  1321  1468 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #140
05-25 09:15:29.947  1321  1468 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #147
05-25 09:15:29.947  1321  1468 W PackageParsing: Unknown element under <manifest>: activity at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #153
05-25 09:15:29.947  1321  1468 W PackageParsing: Unknown element under <manifest>: receiver at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #158
05-25 09:15:29.947  1321  1468 W PackageParsing: Unknown element under <manifest>: service at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #166
05-25 09:15:29.947  1321  1468 W PackageParsing: Unknown element under <manifest>: provider at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #168
05-25 09:15:29.947  1321  1468 W PackageParsing: Unknown element under <manifest>: receiver at /apex/com.android.nfcservices/priv-app/NfcNciApex@BP1A.250505.005/NfcNciApex.apk Binary XML file line #177
05-25 09:15:29.951  1321  1321 I ApexManager: Registering com.android.nfc as apk-in-apex of com.android.nfcservices
05-25 09:15:29.992   880   880 W ServiceManagerCppClient: Waited one second for activity (is service started? Number of threads started in the threadpool: 16. Are binder threads started and available?)
05-25 09:15:30.031  1321  1321 I ApexManager: Registering com.android.bluetooth as apk-in-apex of com.android.btservices
05-25 09:15:30.041  1321  1321 I ApexManager: Registering com.android.ondevicepersonalization.services as apk-in-apex of com.android.ondevicepersonalization
05-25 09:15:30.049  1321  1321 I ApexManager: Registering com.android.federatedcompute.services as apk-in-apex of com.android.ondevicepersonalization
05-25 09:15:30.083  1321  1321 I ApexManager: Registering android.ext.services as apk-in-apex of com.android.extservices
05-25 09:15:30.098  1321  1468 W PackageParsing: com.android.healthconnect.controller defines a background permission. Only the 'android' packages can do that.
05-25 09:15:30.098  1321  1468 W PackageParsing: com.android.healthconnect.controller defines a background permission. Only the 'android' packages can do that.
05-25 09:15:30.098  1321  1468 W PackageParsing: com.android.healthconnect.controller defines a background permission. Only the 'android' packages can do that.
05-25 09:15:30.120  1004  1124 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-25 09:15:30.120  1004  1124 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:4
05-25 09:15:30.152  1321  1321 I ApexManager: Registering com.android.cellbroadcastservice as apk-in-apex of com.android.cellbroadcast
05-25 09:15:30.283  1408  1413 E RadioConfig_service: radioConfigService[0] or mRadioConfigIndication is NULL
05-25 09:15:30.476  1321  1468 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.RECEIVE_BOOT_COMPLETED in package: com.android.adservices.api at: Binary XML file line #159
05-25 09:15:30.482  1321  1321 I ApexManager: Registering com.android.adservices.api as apk-in-apex of com.android.adservices
05-25 09:15:30.491  1321  1321 I ApexManager: Registering com.android.sdksandbox as apk-in-apex of com.android.adservices
05-25 09:15:30.719  1321  1470 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.MANAGE_OWN_CALLS in package: com.truecaller at: Binary XML file line #141
05-25 09:15:30.740  1321  1468 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.READ_EXTERNAL_STORAGE in package: ellipi.messenger at: Binary XML file line #111
05-25 09:15:30.740  1321  1468 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WRITE_EXTERNAL_STORAGE in package: ellipi.messenger at: Binary XML file line #179
05-25 09:15:30.809  1321  1468 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #9
05-25 09:15:30.809  1321  1468 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #10
05-25 09:15:30.809  1321  1468 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #11
05-25 09:15:30.809  1321  1468 W PackageParsing: Unknown element under <manifest>: uses-permission at /data/app/~~sseLeg-4c2NWRbOaNXgFEQ==/com.brave.browser-KyL25HAFZ4V9g-VLPmm-mg==/base.apk Binary XML file line #12
05-25 09:15:30.838  1321  1469 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.INTERNET in package: com.intsig.camscanner at: Binary XML file line #34
05-25 09:15:30.844  1016  1016 I mtkcam-devicemgr: [initialize] -
05-25 09:15:30.845  1016  1016 I mtkcam-camprovider: [initialize] +
05-25 09:15:30.845  1016  1016 I mtkcam-camprovider: [initialize] -
05-25 09:15:30.848  1016  1016 I HidlServiceManagement: Registered android.hardware.camera.provider@2.6::ICameraProvider/internal/0
05-25 09:15:30.863  1005  1097 I CameraService: onDeviceStatusChanged: Status changed for cameraId=4, newStatus=1
05-25 09:15:30.863  1005  1097 I CameraService: onDeviceStatusChanged: Unknown camera ID 4, a new camera is added
05-25 09:15:30.863  1005  1097 I CameraService: onDeviceStatusChanged: Status changed for cameraId=3, newStatus=1
05-25 09:15:30.863  1005  1097 I CameraService: onDeviceStatusChanged: Unknown camera ID 3, a new camera is added
05-25 09:15:30.864  1005  1097 I CameraService: onDeviceStatusChanged: Status changed for cameraId=2, newStatus=1
05-25 09:15:30.864  1005  1097 I CameraService: onDeviceStatusChanged: Unknown camera ID 2, a new camera is added
05-25 09:15:30.864  1005  1097 I CameraService: onDeviceStatusChanged: Status changed for cameraId=1, newStatus=1
05-25 09:15:30.864  1005  1097 I CameraService: onDeviceStatusChanged: Unknown camera ID 1, a new camera is added
05-25 09:15:30.864  1005  1097 I CameraService: onDeviceStatusChanged: Status changed for cameraId=0, newStatus=1
05-25 09:15:30.864  1005  1097 I CameraService: onDeviceStatusChanged: Unknown camera ID 0, a new camera is added
05-25 09:15:30.874  1016  1016 I HidlServiceManagement: Registered vendor.mediatek.hardware.camera.isphal@1.0::IISPModule/internal/0
05-25 09:15:30.880  1016  1016 I MtkCam/BGService: IBGService  into HIDL_FETCH_IBGService
05-25 09:15:30.882  1016  1016 I HidlServiceManagement: Registered vendor.mediatek.hardware.camera.bgservice@1.1::IBGService/internal/0
05-25 09:15:30.882  1016  1016 I LegacySupport: Registration complete for vendor.mediatek.hardware.camera.bgservice@1.1::IBGService/internal/0.
05-25 09:15:30.895  1016  1016 I HidlServiceManagement: Registered vendor.mediatek.hardware.camera.atms@1.0::IATMs/default
05-25 09:15:30.993   880   880 W ServiceManagerCppClient: Waited one second for activity (is service started? Number of threads started in the threadpool: 16. Are binder threads started and available?)
05-25 09:15:31.049  1011  1025 W ServiceManagerCppClient: Service package_native didn't start. Returning NULL
05-25 09:15:31.049  1011  1025 E ServiceUtilities: getCachedInfo: Cannot find package_native
05-25 09:15:31.120  1004  1124 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-25 09:15:31.120  1004  1124 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:5
05-25 09:15:31.326  1013  1013 W ServiceManagerCppClient: Service package_native didn't start. Returning NULL
05-25 09:15:31.326  1013  1013 E storaged: getService package_native failed
05-25 09:15:31.334  1013  1516 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder'...
05-25 09:15:31.994   880   880 W ServiceManagerCppClient: Waited one second for activity (is service started? Number of threads started in the threadpool: 16. Are binder threads started and available?)
05-25 09:15:32.121  1004  1124 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-25 09:15:32.121  1004  1124 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:6
05-25 09:15:32.440  1321  1468 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-25 09:15:32.442  1321  1468 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-25 09:15:32.444  1321  1468 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-25 09:15:32.445  1321  1468 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #34
05-25 09:15:32.446  1321  1468 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-25 09:15:32.447  1321  1468 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-25 09:15:32.448  1321  1468 W PackageParsing: Unknown element under <manifest>: permission at /data/app/~~5M2cnFIt3LwIfoDiIAsBAQ==/com.google.android.gms-Og3nYCoy-vG2_qm3hVC8YQ==/base.apk Binary XML file line #24
05-25 09:15:32.450  1321  1470 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.USE_FINGERPRINT in package: org.mozilla.focus at: Binary XML file line #73
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.ACCESS_NETWORK_STATE in package: com.google.android.gm at: Binary XML file line #311
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE in package: com.google.android.gm at: Binary XML file line #314
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE_DATA_SYNC in package: com.google.android.gm at: Binary XML file line #315
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.INTERNET in package: com.google.android.gm at: Binary XML file line #316
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.RECEIVE_BOOT_COMPLETED in package: com.google.android.gm at: Binary XML file line #317
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.VIBRATE in package: com.google.android.gm at: Binary XML file line #318
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WAKE_LOCK in package: com.google.android.gm at: Binary XML file line #319
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WRITE_EXTERNAL_STORAGE in package: com.google.android.gm at: Binary XML file line #321
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.POST_NOTIFICATIONS in package: com.google.android.gm at: Binary XML file line #322
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.c2dm.permission.RECEIVE in package: com.google.android.gm at: Binary XML file line #324
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.GET_PACKAGE_SIZE in package: com.google.android.gm at: Binary XML file line #325
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.providers.gsf.permission.READ_GSERVICES in package: com.google.android.gm at: Binary XML file line #326
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.providers.gsf.permission.READ_GSERVICES in package: com.google.android.gm at: Binary XML file line #331
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.GET_PACKAGE_SIZE in package: com.google.android.gm at: Binary XML file line #332
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.ACCESS_NETWORK_STATE in package: com.google.android.gm at: Binary XML file line #334
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.CAMERA in package: com.google.android.gm at: Binary XML file line #345
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE in package: com.google.android.gm at: Binary XML file line #360
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.INTERNET in package: com.google.android.gm at: Binary XML file line #361
05-25 09:15:32.503  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.RECORD_AUDIO in package: com.google.android.gm at: Binary XML file line #363
05-25 09:15:32.504  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.WAKE_LOCK in package: com.google.android.gm at: Binary XML file line #364
05-25 09:15:32.504  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.POST_NOTIFICATIONS in package: com.google.android.gm at: Binary XML file line #373
05-25 09:15:32.504  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.FOREGROUND_SERVICE_DATA_SYNC in package: com.google.android.gm at: Binary XML file line #379
05-25 09:15:32.504  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.gm.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION in package: com.google.android.gm at: Binary XML file line #385
05-25 09:15:32.504  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.VIBRATE in package: com.google.android.gm at: Binary XML file line #386
05-25 09:15:32.504  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: android.permission.READ_CONTACTS in package: com.google.android.gm at: Binary XML file line #387
05-25 09:15:32.504  1321  1471 W PackageParsing: Ignoring duplicate uses-permissions/uses-permissions-sdk-m: com.google.android.hangouts.START_HANGOUT in package: com.google.android.gm at: Binary XML file line #388
05-25 09:15:32.535   822   826 W ServiceManagerCppClient: Service statscompanion didn't start. Returning NULL
05-25 09:15:32.535   822   826 E statsd  : Uid 1000 does not have the android.permission.REGISTER_STATS_PULL_ATOM permission when registering atom 10205 (-1)
05-25 09:15:32.995   880   880 W ServiceManagerCppClient: Waited one second for activity (is service started? Number of threads started in the threadpool: 16. Are binder threads started and available?)
05-25 09:15:33.050  1023  1377 W mtk_agpsd: Thread Pool max thread count is 0. Cannot cache binder as linkToDeath cannot be implemented. serviceName: android.system.suspend.ISystemSuspend/default
05-25 09:15:33.121  1004  1124 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-25 09:15:33.121  1004  1124 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:7
05-25 09:15:33.996   880   880 W ServiceManagerCppClient: Waited one second for activity (is service started? Number of threads started in the threadpool: 16. Are binder threads started and available?)
05-25 09:15:34.063  1321  1321 W AppIdPermissionPolicy: Ignoring permission com.google.android.gtalkservice.permission.GTALK_SERVICE declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-25 09:15:34.063  1321  1321 W AppIdPermissionPolicy: Ignoring permission com.android.vending.INTENT_VENDING_ONLY declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-25 09:15:34.063  1321  1321 W AppIdPermissionPolicy: Ignoring permission com.google.android.providers.settings.permission.WRITE_GSETTINGS declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-25 09:15:34.064  1321  1321 W AppIdPermissionPolicy: Ignoring permission com.google.android.providers.gsf.permission.WRITE_GSERVICES declared in system package com.google.android.gsf: already declared in another system package com.google.android.gms
05-25 09:15:34.064  1321  1321 W AppIdPermissionPolicy: Ignoring permission lineageos.permission.MANAGE_REMOTE_PREFERENCES declared in system package com.android.settings: already declared in another system package io.chaldeaprjkt.gamespace
05-25 09:15:34.066  1321  1321 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_TOPICS declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-25 09:15:34.066  1321  1321 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_ATTRIBUTION declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-25 09:15:34.066  1321  1321 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_CUSTOM_AUDIENCE declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-25 09:15:34.066  1321  1321 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_AD_SELECTION declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-25 09:15:34.066  1321  1321 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_PROTECTED_SIGNALS declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-25 09:15:34.066  1321  1321 W AppIdPermissionPolicy: Ignoring permission android.permission.ACCESS_ADSERVICES_AD_ID declared in system package android.ext.services: already declared in another system package com.android.adservices.api
05-25 09:15:34.121  1004  1124 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-25 09:15:34.121  1004  1124 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:8
05-25 09:15:34.189  1321  1321 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.adservices/appid
05-25 09:15:34.191  1321  1321 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.permission/appid
05-25 09:15:34.195  1321  1321 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.btservices/appid
05-25 09:15:34.205  1321  1321 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.extservices/appid
05-25 09:15:34.209  1321  1321 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.nfcservices/appid
05-25 09:15:34.302  1321  1321 I SystemServiceManager: Starting com.android.server.pm.UserManagerService$LifeCycle
05-25 09:15:34.350  1013  1516 I ServiceManagerCppClient: Waiting for service 'package_native' on '/dev/binder' successful after waiting 3016ms
05-25 09:15:34.403  1321  1561 W PackageManager: Skipping preparing app data for com.android.adservices
05-25 09:15:34.403  1321  1561 W PackageManager: Skipping preparing app data for com.android.permission
05-25 09:15:34.404  1321  1561 W PackageManager: Skipping preparing app data for com.android.btservices
05-25 09:15:34.405  1321  1561 W PackageManager: Skipping preparing app data for com.android.extservices
05-25 09:15:34.405  1321  1561 W PackageManager: Skipping preparing app data for com.android.nfcservices
05-25 09:15:34.560  1321  1321 I SystemServiceManager: Starting com.android.server.sensors.SensorService
05-25 09:15:34.561  1321  1321 I SystemServiceManager: Starting com.android.server.SystemConfigService
05-25 09:15:34.563  1321  1321 I SystemServiceManager: Starting com.android.server.BatteryService
05-25 09:15:34.566  1011  1511 W AudioAnalytics: onAudioServerStart: (key=audio.policy) AudioPolicy ctor, loadTimeMs:8550.027344
05-25 09:15:34.570   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.sensors@2.1::ISensors/default in either framework or device VINTF manifest.
05-25 09:15:34.572   880   880 I audioserver: main: initialization done in 8809.219 ms, joining thread pool
05-25 09:15:34.584  1321  1571 W SensorService: lsm6dso ACCELEROMETER's max range 78.453201293945 is not a multiple of the resolution 0.001200000057 - updated to 78.453605651855
05-25 09:15:34.584  1321  1571 I SensorService: lsm6dso ACCELEROMETER's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.584  1321  1571 W SensorService: mmc5603 MAGNETOMETER's max range 4912.000000000000 is not a multiple of the resolution 0.150000005960 - updated to 4912.050292968750
05-25 09:15:34.584  1321  1571 I SensorService: mmc5603 MAGNETOMETER's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.584  1321  1571 W SensorService: lsm6dso GYROSCOPE's max range 34.906600952148 is not a multiple of the resolution 0.001099999994 - updated to 34.906299591064
05-25 09:15:34.584  1321  1571 I SensorService: lsm6dso GYROSCOPE's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.584  1321  1571 I SensorService: tcs3701 PROXIMITY's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 W SensorService: UNCALI_MAG's max range 4912.000000000000 is not a multiple of the resolution 0.150000005960 - updated to 4912.050292968750
05-25 09:15:34.585  1321  1571 I SensorService: UNCALI_MAG's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 W SensorService: UNCALI_GYRO's max range 34.906600952148 is not a multiple of the resolution 0.001099999994 - updated to 34.906299591064
05-25 09:15:34.585  1321  1571 I SensorService: UNCALI_GYRO's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 I SensorService: SIGNIFICANT_MOTION's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 I SensorService: STEP_DETECTOR's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 I SensorService: STEP_COUNTER's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 I SensorService: DEVICE_ORIENTATION's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 I SensorService: STATIONARY_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 I SensorService: MOTION_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 W SensorService: UNCALI_ACC's max range 39.226600646973 is not a multiple of the resolution 0.001200000057 - updated to 39.226802825928
05-25 09:15:34.585  1321  1571 I SensorService: UNCALI_ACC's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 I SensorService: tcs3701 LIGHT's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 I SensorService: RAW_MAG's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 I SensorService: mn29005 rear_als's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 I SensorService: ai_shutter's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 I SensorService: STEP_DETECTOR_WAKEUP's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 I SensorService: PICKUP_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 I SensorService: FP_DISPLAY's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 I SensorService: LUX_AOD's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 I SensorService: PEDO_MINUTE's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 I SensorService: OPLUS_ACTIVITY_RECOGNITION's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.585  1321  1571 I SensorService: ELEVATOR_DETECT's reported power 0.000000 invalid, clamped to 0.001000
05-25 09:15:34.587   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.light@2.0::ILight/default in either framework or device VINTF manifest.
05-25 09:15:34.593  1321  1321 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-25 09:15:34.595  1321  1321 I HealthServiceWrapperHidl: health: HealthServiceWrapper listening to instance default
05-25 09:15:34.595  1321  1321 I BatteryService: health: Waited 0ms and received the update.
05-25 09:15:34.598  1321  1321 I SystemServiceManager: Starting com.android.server.usage.UsageStatsService
05-25 09:15:34.611  1321  1321 I SystemServiceManager: Starting com.android.server.webkit.WebViewUpdateService
05-25 09:15:34.616  1321  1321 I SystemServiceManager: Starting com.android.server.CachedDeviceStateService
05-25 09:15:34.617  1321  1321 I SystemServiceManager: Starting com.android.server.BinderCallsStatsService$LifeCycle
05-25 09:15:34.619  1321  1321 I SystemServiceManager: Starting com.android.server.LooperStatsService$Lifecycle
05-25 09:15:34.620  1321  1321 I SystemServiceManager: Starting com.android.server.rollback.RollbackManagerService
05-25 09:15:34.635  1321  1321 I SystemServiceManager: Starting com.android.server.os.NativeTombstoneManagerService
05-25 09:15:34.637  1321  1321 I SystemServiceManager: Starting com.android.server.os.BugreportManagerService
05-25 09:15:34.639  1321  1321 I SystemServiceManager: Starting com.android.server.gpu.GpuService
05-25 09:15:34.639  1321  1321 I SystemServiceManager: Starting com.android.server.security.rkp.RemoteProvisioningService
05-25 09:15:34.643  1321  1321 I SystemServiceManager: Starting com.android.server.security.KeyChainSystemService
05-25 09:15:34.644  1321  1321 I SystemServiceManager: Starting com.android.server.BinaryTransparencyService
05-25 09:15:34.646  1321  1321 I TransparencyService: Started BinaryTransparencyService
05-25 09:15:34.650  1321  1321 I SystemServiceManager: Starting com.android.server.telecom.TelecomLoaderService
05-25 09:15:34.658  1321  1321 I SystemServiceManager: Starting com.android.server.accounts.AccountManagerService$Lifecycle
05-25 09:15:34.663  1321  1321 I SystemServiceManager: Starting com.android.server.content.ContentService$Lifecycle
05-25 09:15:34.680  1321  1584 I SchedulingPolicyService: Moving 1028 back to group default
05-25 09:15:34.798  1321  1321 I Freezer : Cannot open freezer path "/sys/fs/cgroup/uid_1000/pid_1321/frozen/freezer.state": No such file or directory
05-25 09:15:34.798  1321  1321 I SystemServiceManager: Starting com.android.server.deviceconfig.DeviceConfigInit$Lifecycle
05-25 09:15:34.800  1321  1321 I SystemServiceManager: Starting com.android.server.DropBoxManagerService
05-25 09:15:34.802  1321  1321 I SystemServiceManager: Starting com.android.ecm.EnhancedConfirmationService
05-25 09:15:34.810  1321  1321 I SystemServiceManager: Starting com.android.server.power.hint.HintManagerService
05-25 09:15:34.815   824   824 E ActivityRecognitionHardware: activity_recognition HAL is deprecated. class_init is effectively a no-op
05-25 09:15:34.819  1321  1321 I SystemServiceManager: Starting com.android.role.RoleService
05-25 09:15:34.825  1321  1321 I SystemServiceManager: Starting com.android.server.vibrator.VibratorManagerService$Lifecycle
05-25 09:15:34.843  1321  1321 I SystemServiceManager: Starting com.android.server.alarm.AlarmManagerService
05-25 09:15:34.874  1321  1321 I InputManager: Initializing input manager, mUseDevInputEventForAudioJack=true
05-25 09:15:34.875  1321  1321 I SystemServiceManager: Starting com.android.server.devicestate.DeviceStateManagerService
05-25 09:15:34.879  1321  1321 E DeviceStateManagerService: Cannot notify device state info change before the initial state has been committed.
05-25 09:15:34.879  1321  1321 I DeviceStateManagerService: Cannot notify device state info change when pending state is present.
05-25 09:15:34.882  1321  1321 I SystemServiceManager: Starting com.android.server.camera.CameraServiceProxy
05-25 09:15:34.885  1321  1321 I SystemServiceManager: Starting phase 200
05-25 09:15:34.910   824   824 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlags
05-25 09:15:34.910   824   824 W Zygote  : Class not found for preloading: android.media.audio.FeatureFlagsImpl
05-25 09:15:34.910   824   824 W Zygote  : Class not found for preloading: android.media.audio.Flags
05-25 09:15:35.004  1321  1321 I SystemServiceManager: Starting com.android.server.bluetooth.BluetoothService
05-25 09:15:35.005  1321  1597 I HidlServiceManagement: Registered android.frameworks.schedulerservice@1.0::ISchedulingPolicyService/default
05-25 09:15:35.007  1321  1596 I HidlServiceManagement: Registered android.frameworks.sensorservice@1.0::ISensorManager/default
05-25 09:15:35.015  1321  1321 I SystemServiceManager: Starting com.android.server.connectivity.IpConnectivityMetrics
05-25 09:15:35.016  1321  1321 I SystemServiceManager: Starting com.android.server.net.watchlist.NetworkWatchlistService$Lifecycle
05-25 09:15:35.020  1321  1321 I SystemServiceManager: Starting com.android.server.pinner.PinnerService
05-25 09:15:35.023  1321  1321 I SystemServiceManager: Starting com.android.server.integrity.AppIntegrityManagerService
05-25 09:15:35.024  1321  1321 I SystemServiceManager: Starting com.android.server.logcat.LogcatManagerService
05-25 09:15:35.044  1321  1321 I SystemServiceManager: Starting com.android.server.inputmethod.InputMethodManagerService$Lifecycle
05-25 09:15:35.055  1321  1321 I SystemServiceManager: Starting com.android.server.accessibility.AccessibilityManagerService$Lifecycle
05-25 09:15:35.071  1321  1321 I SystemServiceManager: Starting com.android.server.StorageManagerService$Lifecycle
05-25 09:15:35.073  1321  1356 W PinnerService: Could not find pinlist.meta for "/system_ext/priv-app/SystemUI/SystemUI.apk": pinning as blob
05-25 09:15:35.083  1321  1321 I SystemServiceManager: Starting com.android.server.usage.StorageStatsService$Lifecycle
05-25 09:15:35.087  1321  1321 I SystemServiceManager: Starting com.android.server.UiModeManagerService
05-25 09:15:35.089  1321  1321 I SystemServiceManager: Starting com.android.server.locales.LocaleManagerService
05-25 09:15:35.092  1321  1321 I SystemServiceManager: Starting com.android.server.grammaticalinflection.GrammaticalInflectionService
05-25 09:15:35.093  1321  1321 I SystemServiceManager: Starting com.android.server.apphibernation.AppHibernationService
05-25 09:15:35.097  1321  1321 I SystemServiceManager: Starting com.android.server.locksettings.LockSettingsService$Lifecycle
05-25 09:15:35.106  1321  1321 I SystemServiceManager: Starting com.android.server.pdb.PersistentDataBlockService
05-25 09:15:35.107  1321  1321 I SystemServiceManager: Starting com.android.server.testharness.TestHarnessModeService
05-25 09:15:35.107  1321  1321 I SystemServiceManager: Starting com.android.server.oemlock.OemLockService
05-25 09:15:35.109   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.oemlock@1.0::IOemLock/default in either framework or device VINTF manifest.
05-25 09:15:35.114  1321  1321 I SystemServiceManager: Starting com.android.server.DeviceIdleController
05-25 09:15:35.119  1321  1352 I PersistentDataBlockService: Writing default FRP secret
05-25 09:15:35.119  1321  1352 I PersistentDataBlockService: Writing FRP secret magic
05-25 09:15:35.120  1321  1321 I SystemServiceManager: Starting com.android.server.devicepolicy.DevicePolicyManagerService$Lifecycle
05-25 09:15:35.121  1004  1124 W mnld_pwr_interface: mnld_pwr_open: mnld_pwr_open, /dev/gps_pwr failed:No such file or directory
05-25 09:15:35.121  1004  1124 E mnld_pwr_interface: mnld_pwr_control_thread: mnld_pwr_open failed, No such file or directory, retry:9
05-25 09:15:35.136  1321  1321 I SystemServiceManager: Starting com.android.server.systemcaptions.SystemCaptionsManagerService
05-25 09:15:35.136  1321  1321 I SystemServiceManager: Starting com.android.server.texttospeech.TextToSpeechManagerService
05-25 09:15:35.137  1321  1321 I SystemServiceManager: Starting com.android.server.wearable.WearableSensingManagerService
05-25 09:15:35.139  1321  1321 I SystemServiceManager: Starting com.android.server.ondeviceintelligence.OnDeviceIntelligenceManagerService
05-25 09:15:35.140  1321  1321 I SystemServiceManager: Starting com.android.server.speech.SpeechRecognitionManagerService
05-25 09:15:35.141  1321  1321 I SystemServiceManager: Starting com.android.server.appprediction.AppPredictionManagerService
05-25 09:15:35.142  1321  1321 I SystemServiceManager: Starting com.android.server.contentsuggestions.ContentSuggestionsManagerService
05-25 09:15:35.143  1321  1321 I SystemServiceManager: Starting com.android.server.contextualsearch.ContextualSearchManagerService
05-25 09:15:35.148  1321  1321 I FontManagerService: Using optimized boot-time font loading.
05-25 09:15:35.149  1321  1321 I SystemServiceManager: Starting com.android.server.textservices.TextServicesManagerService$Lifecycle
05-25 09:15:35.150  1321  1321 I SystemServiceManager: Starting com.android.server.textclassifier.TextClassificationManagerService$Lifecycle
05-25 09:15:35.153  1321  1321 I SystemServiceManager: Starting com.android.server.NetworkScoreService$Lifecycle
05-25 09:15:35.154  1321  1321 I NetworkScoreService: Registering network_score
05-25 09:15:35.155  1321  1321 I SystemServiceManager: Starting com.android.server.NetworkStatsServiceInitializer
05-25 09:15:35.184  1321  1321 I NetworkStatsServiceInitializer: Registering netstats
05-25 09:15:35.190  1321  1321 I SystemServiceManager: Starting com.android.server.wifi.WifiService
05-25 09:15:35.193  1321  1321 I WifiContext: Got resolveInfos for com.android.server.wifi.intent.action.SERVICE_WIFI_RESOURCES_APK: [ResolveInfo{c5d1741 com.android.wifi.resources/android.app.Activity m=0x108000 userHandle=UserHandle{0}}]
05-25 09:15:35.198  1321  1321 E WifiScoringParams: 	at com.android.server.wifi.ScoringParams.<init>(ScoringParams.java:262)
05-25 09:15:35.198  1321  1321 E WifiScoringParams: 	at com.android.server.wifi.WifiInjector.<init>(WifiInjector.java:319)
05-25 09:15:35.198  1321  1321 E WifiScoringParams: 	at com.android.server.wifi.WifiService.<init>(WifiService.java:44)
05-25 09:15:35.198  1321  1321 E WifiScoringParams: 	at com.android.server.SystemServiceManager.startService(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:9)
05-25 09:15:35.198  1321  1321 E WifiScoringParams: 	at com.android.server.SystemServiceManager.startServiceFromJar(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:88)
05-25 09:15:35.198  1321  1321 E WifiScoringParams: 	at com.android.server.SystemServer.startOtherServices(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:322)
05-25 09:15:35.198  1321  1321 E WifiScoringParams: 	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:597)
05-25 09:15:35.198  1321  1321 E WifiScoringParams: 	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:889)
05-25 09:15:35.203  1321  1321 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.2::IServiceManager/default
05-25 09:15:35.209  1321  1321 I SupplicantStaIfaceHal: Initializing SupplicantStaIfaceHal using AIDL implementation.
05-25 09:15:35.211  1321  1321 I SupplicantP2pIfaceHal: Initializing SupplicantP2pIfaceHal using AIDL implementation.
05-25 09:15:35.232  1321  1356 W PinnerService: Could not find pinlist.meta for "/system_ext/priv-app/Launcher3QuickStep/Launcher3QuickStep.apk": pinning as blob
05-25 09:15:35.239  1321  1321 I WifiService: Registering wifi
05-25 09:15:35.240  1321  1321 I SystemServiceManager: Starting com.android.server.wifi.scanner.WifiScanningService
05-25 09:15:35.240  1321  1321 I WifiScanningService: Creating wifiscanner
05-25 09:15:35.243  1321  1321 I WifiScanningService: Publishing wifiscanner
05-25 09:15:35.244  1321  1321 I SystemServiceManager: Starting com.android.server.wifi.p2p.WifiP2pService
05-25 09:15:35.245  1321  1321 I WifiContext: Got resolveInfos for com.android.server.wifi.intent.action.SERVICE_WIFI_RESOURCES_APK: [ResolveInfo{8a57297 com.android.wifi.resources/android.app.Activity m=0x108000 userHandle=UserHandle{0}}]
05-25 09:15:35.248  1321  1321 I WifiP2pService: Registering wifip2p
05-25 09:15:35.250  1321  1321 I SystemServiceManager: Starting com.android.server.ConnectivityServiceInitializer
05-25 09:15:35.277  1321  1321 I MulticastRoutingCoordinatorService: socket created for multicast routing: java.io.FileDescriptor@c1d7b2a
05-25 09:15:35.279  1321  1630 W BroadcastLoopers: Found previously unknown looper Thread[NsdService,5,main]
05-25 09:15:35.285  1321  1321 I ConnectivityServiceInitializer: Registering ethernet
05-25 09:15:35.285  1321  1321 I ConnectivityServiceInitializer: Registering connectivity
05-25 09:15:35.286  1321  1321 I ConnectivityServiceInitializer: Registering ipsec
05-25 09:15:35.286  1321  1321 I ConnectivityServiceInitializer: Registering connectivity_native
05-25 09:15:35.286  1321  1321 I ConnectivityServiceInitializer: Registering servicediscovery
05-25 09:15:35.286  1321  1321 I ConnectivityServiceInitializer: Registering nearby
05-25 09:15:35.289  1321  1321 I SystemServiceManager: Starting com.android.server.ConnectivityServiceInitializerB
05-25 09:15:35.291  1321  1321 I ConnectivityServiceInitializerB: Registering vcn_management
05-25 09:15:35.292  1321  1321 I SystemUpdateManagerService: No existing info file /data/system/system-update-info.xml
05-25 09:15:35.293  1321  1321 I SystemServiceManager: Starting com.android.server.notification.NotificationManagerService
05-25 09:15:35.359  1321  1321 I NotificationManagerService.NotificationListeners: Read notification listener permissions from xml
05-25 09:15:35.360  1321  1321 W ActivityManager: Unable to start service Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener } U=0: not found
05-25 09:15:35.360  1321  1321 W NotificationManagerService.NotificationListeners: Unable to bind notification listener service: Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener (has extras) } in user 0
05-25 09:15:35.360  1321  1321 I NotificationManagerService.NotificationAssistants: Read notification assistant permissions from xml
05-25 09:15:35.364  1321  1321 I ConditionProviders: Read condition provider permissions from xml
05-25 09:15:35.364  1321  1321 I ConditionProviders: Read condition provider permissions from xml
05-25 09:15:35.365  1321  1321 I ConditionProviders:  Allowing condition provider android.ext.services/android.ext.services.notification.Assistant (userSet: true)
05-25 09:15:35.373  1321  1321 W SystemServiceManager: Service com.android.server.notification.NotificationManagerService took 78 ms in onStart
05-25 09:15:35.376  1321  1321 I SystemServiceManager: Starting com.android.server.storage.DeviceStorageMonitorService
05-25 09:15:35.378  1321  1321 I SystemServiceManager: Starting com.android.server.timedetector.TimeDetectorService$Lifecycle
05-25 09:15:35.382  1321  1321 I SystemServiceManager: Starting com.android.server.location.LocationManagerService$Lifecycle
05-25 09:15:35.388  1321  1321 I SystemServiceManager: Starting com.android.server.timezonedetector.TimeZoneDetectorService$Lifecycle
05-25 09:15:35.391  1321  1321 I SystemServiceManager: Starting com.android.server.location.altitude.AltitudeService$Lifecycle
05-25 09:15:35.393  1321  1321 I SystemServiceManager: Starting com.android.server.timezonedetector.location.LocationTimeZoneManagerService$Lifecycle
05-25 09:15:35.393  1321  1321 I SystemServiceManager: Starting com.android.server.search.SearchManagerService$Lifecycle
05-25 09:15:35.394  1321  1321 I SystemServiceManager: Starting com.android.server.wallpaper.WallpaperManagerService$Lifecycle
05-25 09:15:35.397  1321  1321 I SystemServiceManager: Starting com.android.server.audio.AudioService$Lifecycle
05-25 09:15:35.403   880  1296 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:35.403   880  1282 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:35.403   880  1295 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:35.403   880  1289 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:35.403   880  1297 W AudioFlinger: no wake lock to update, system not ready yet
05-25 09:15:35.411   832   832 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-25 09:15:35.411   832   832 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-25 09:15:35.411   832   832 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-25 09:15:35.411   832   832 W audio_engineer_test: unknown enum value string receiver for ctl TFA98XX Profile
05-25 09:15:35.411   832   832 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-25 09:15:35.411   832   832 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-25 09:15:35.412   832   832 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-25 09:15:35.412   832   832 W audio_engineer_test: unknown enum value string speaker for ctl TFA98XX Profile
05-25 09:15:35.420  1321  1321 I AS.AudioService: Stream 5: using max vol of 7
05-25 09:15:35.420  1321  1321 I AS.AudioService: Stream 5: using default vol of 5
05-25 09:15:35.420  1321  1321 I AS.AudioService: Stream 2: using max vol of 7
05-25 09:15:35.420  1321  1321 I AS.AudioService: Stream 2: using default vol of 5
05-25 09:15:35.424  1321  1321 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-25 09:15:35.445  1321  1643 I AS.AudioService: updateIndexFactors() stream:0 index min/max:1/15 indexStepFactor:2.3333333
05-25 09:15:35.446  1321  1321 I SystemServiceManager: Starting com.android.server.soundtrigger_middleware.SoundTriggerMiddlewareService$Lifecycle
05-25 09:15:35.449  1321  1643 I AS.AudioService: updateIndexFactors() stream:1 index min/max:0/7 indexStepFactor:1.0
05-25 09:15:35.450  1321  1643 I AS.AudioService: updateIndexFactors() stream:2 index min/max:0/7 indexStepFactor:1.0
05-25 09:15:35.453  1321  1643 I AS.AudioService: updateIndexFactors() stream:3 index min/max:0/15 indexStepFactor:1.0
05-25 09:15:35.456  1321  1643 I AS.AudioService: updateIndexFactors() stream:4 index min/max:1/7 indexStepFactor:1.0
05-25 09:15:35.457  1321  1643 I AS.AudioService: updateIndexFactors() stream:5 index min/max:0/7 indexStepFactor:1.0
05-25 09:15:35.458  1321  1643 I AS.AudioService: updateIndexFactors() stream:7 index min/max:0/7 indexStepFactor:1.0
05-25 09:15:35.458  1321  1643 I AS.AudioService: updateIndexFactors() stream:8 index min/max:0/15 indexStepFactor:1.0
05-25 09:15:35.459  1321  1643 I AS.AudioService: updateIndexFactors() stream:9 index min/max:0/15 indexStepFactor:1.0
05-25 09:15:35.460  1321  1643 I AS.AudioService: updateIndexFactors() stream:10 index min/max:1/15 indexStepFactor:1.0
05-25 09:15:35.460  1321  1643 I AS.AudioService: updateIndexFactors() stream:11 index min/max:0/15 indexStepFactor:1.0
05-25 09:15:35.463  1321  1321 I SystemServiceManager: Starting com.android.server.DockObserver
05-25 09:15:35.466  1321  1321 W WiredAccessoryManager: This kernel does not have usb audio support
05-25 09:15:35.467  1321  1321 W WiredAccessoryManager: This kernel does not have HDMI audio support
05-25 09:15:35.468  1321  1321 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/3/0 does not have DP audio support
05-25 09:15:35.468  1321  1321 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/2/0 does not have DP audio support
05-25 09:15:35.468  1321  1321 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/1/0 does not have DP audio support
05-25 09:15:35.469  1321  1321 W WiredAccessoryManager: Conn soc:qcom,msm-ext-disp/0/0 does not have DP audio support
05-25 09:15:35.469  1321  1321 I SystemServiceManager: Starting com.android.server.midi.MidiService$Lifecycle
05-25 09:15:35.472  1321  1321 I SystemServiceManager: Starting com.android.server.adb.AdbService$Lifecycle
05-25 09:15:35.479  1321  1321 I SystemServiceManager: Starting com.android.server.usb.UsbService$Lifecycle
05-25 09:15:35.480  1321  1321 I SystemServiceManager: Starting com.android.server.SerialService$Lifecycle
05-25 09:15:35.481  1321  1643 W BroadcastLoopers: Found previously unknown looper Thread[AudioService,5,main]
05-25 09:15:35.483  1321  1321 I HardwarePropertiesManagerService-JNI: Thermal AIDL service is not declared, trying HIDL
05-25 09:15:35.488  1321  1321 I SystemServiceManager: Starting com.android.server.twilight.TwilightService
05-25 09:15:35.489  1321  1321 I SystemServiceManager: Starting com.android.server.display.color.ColorDisplayService
05-25 09:15:35.493  1321  1584 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-25 09:15:35.493  1321  1321 I SystemServiceManager: Starting com.android.server.job.JobSchedulerService
05-25 09:15:35.495  1321  1355 I UsbDeviceManager: Usb gadget hal service started android.hardware.usb.gadget@1.0::IUsbGadget default
05-25 09:15:35.502  1321  1584 W StorageManagerService: No primary storage defined yet; hacking together a stub
05-25 09:15:35.505  1321  1347 W JobInfo : Job 'com.google.android.setupwizard/.deviceorigin.provider.DeviceOriginWipeOutJobService#8580' has a deadline with no functional constraints. The deadline won't improve job execution latency. Consider removing the deadline.
05-25 09:15:35.507   880  1566 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_HEARING_AID, connection: wireless}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-25 09:15:35.507   880  1566 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x8000000, enabled 1, streamToDriveAbs 3
05-25 09:15:35.508   880  1566 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_HEADSET, connection: bt-le}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-25 09:15:35.508   880  1566 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x20000000, enabled 1, streamToDriveAbs 3
05-25 09:15:35.508   880  1566 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_SPEAKER, connection: bt-le}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-25 09:15:35.508   880  1566 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x20000001, enabled 1, streamToDriveAbs 3
05-25 09:15:35.508   880  1566 I AudioPolicyInterfaceImpl: setDeviceAbsoluteVolumeEnabled: deviceAidl AudioDevice{type: AudioDeviceDescription{type: OUT_BROADCAST, connection: bt-le}, address: AudioDeviceAddress{id: }}, enabled 1, streamToDriveAbsAidl 3
05-25 09:15:35.508   880  1566 I APM_AudioPolicyManager: setDeviceAbsoluteVolumeEnabled: deviceType 0x20000002, enabled 1, streamToDriveAbs 3
05-25 09:15:35.510  1321  1643 E BluetoothAdapter: Bluetooth service is null
05-25 09:15:35.511  1321  1643 E BluetoothAdapter: Bluetooth service is null
05-25 09:15:35.512  1321  1643 E BluetoothAdapter: Bluetooth service is null
05-25 09:15:35.512  1321  1643 E BluetoothAdapter: Bluetooth service is null
05-25 09:15:35.513  1321  1643 I AS.SpatializerHelper: init effectExpected=false
05-25 09:15:35.513  1321  1643 I AS.SpatializerHelper: init(): setting state to STATE_NOT_SUPPORTED due to effect not expected
05-25 09:15:35.515  1321  1584 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-25 09:15:35.519  1321  1355 I UsbPortManager: Usb hal service started android.hardware.usb@1.0::IUsb default
05-25 09:15:35.521  1321  1321 I SystemServiceManager: Starting com.android.server.soundtrigger.SoundTriggerService
05-25 09:15:35.521  1321  1347 W JobInfo : Job 'android/com.android.server.usage.UsageStatsIdleService#0' has a deadline with no functional constraints. The deadline won't improve job execution latency. Consider removing the deadline.
05-25 09:15:35.524  1321  1321 I SystemServiceManager: Starting com.android.server.trust.TrustManagerService
05-25 09:15:35.525   847   847 I android.hardware.usb@1.3-service-mediatekv2: Registering 1.2 callback
05-25 09:15:35.525   847   847 I android.hardware.usb@1.3-service-mediatekv2: registering callback
05-25 09:15:35.527   847  1658 E android.hardware.usb@1.3-service-mediatekv2: creating thread
05-25 09:15:35.527  1321  1321 I SystemServiceManager: Starting com.android.server.backup.BackupManagerService$Lifecycle
05-25 09:15:35.528   847   847 I android.hardware.usb@1.3-service-mediatekv2: port0
05-25 09:15:35.530   847   847 I android.hardware.usb@1.3-service-mediatekv2: HAL version V1_2
05-25 09:15:35.530   847   847 I android.hardware.usb@1.3-service-mediatekv2: 0:port0 connected:1 canChangeMode:1 canChagedata:0 canChangePower:0 supportedModes:3
05-25 09:15:35.530   847   847 I android.hardware.usb@1.3-service-mediatekv2: ContaminantDetectionStatus:2 ContaminantProtectionStatus:0
05-25 09:15:35.531  1321  1321 I SystemServiceManager: Starting com.android.server.appwidget.AppWidgetService
05-25 09:15:35.543  1321  1321 I SystemServiceManager: Starting com.android.server.voiceinteraction.VoiceInteractionManagerService
05-25 09:15:35.562  1321  1321 I SystemServiceManager: Starting com.android.server.GestureLauncherService
05-25 09:15:35.563  1321  1321 I SystemServiceManager: Starting com.android.server.SensorNotificationService
05-25 09:15:35.580  1321  1321 I SystemServiceManager: Starting com.android.server.emergency.EmergencyAffordanceService
05-25 09:15:35.582  1321  1321 I SystemServiceManager: Starting com.android.server.blob.BlobStoreManagerService
05-25 09:15:35.589  1321  1321 I SystemServiceManager: Starting com.android.server.dreams.DreamManagerService
05-25 09:15:35.597   822   826 I ServiceManagerCppClient: Waiting for service 'statscompanion' on '/dev/binder'...
05-25 09:15:35.599  1321  1321 I SystemServiceManager: Starting com.android.server.print.PrintManagerService
05-25 09:15:35.603  1321  1321 I SystemServiceManager: Starting com.android.server.security.AttestationVerificationManagerService
05-25 09:15:35.605  1321  1321 I SystemServiceManager: Starting com.android.server.companion.CompanionDeviceManagerService
05-25 09:15:35.622  1321  1321 I SystemServiceManager: Starting com.android.server.companion.virtual.VirtualDeviceManagerService
05-25 09:15:35.629  1321  1321 I SystemServiceManager: Starting com.android.server.restrictions.RestrictionsManagerService
05-25 09:15:35.631  1321  1321 I SystemServiceManager: Starting com.android.server.media.MediaSessionService
05-25 09:15:35.640  1321  1321 I SystemServiceManager: Starting com.android.server.media.MediaResourceMonitorService
05-25 09:15:35.651  1321  1321 I SystemServiceManager: Starting com.android.server.biometrics.sensors.face.FaceService
05-25 09:15:35.655  1321  1321 I SystemServiceManager: Starting com.android.server.biometrics.sensors.fingerprint.FingerprintService
05-25 09:15:35.659  1321  1321 I SystemServiceManager: Starting com.android.server.biometrics.BiometricService
05-25 09:15:35.663  1321  1321 I CameraManagerGlobal: Connecting to camera service
05-25 09:15:35.671  1321  1321 I SystemServiceManager: Starting com.android.server.biometrics.AuthService
05-25 09:15:35.672  1321  1321 I FingerprintService: Before:getDeclaredInstances: IFingerprint instance found, a.length=0
05-25 09:15:35.672  1321  1321 I FingerprintService: After:getDeclaredInstances: a.length=1
05-25 09:15:35.673  1321  1321 I FaceService: Before:getDeclaredInstances: IFace instance found, a.length=0
05-25 09:15:35.673  1321  1321 I FaceService: After:getDeclaredInstances: a.length=1
05-25 09:15:35.673  1321  1321 E AuthService: Unknown modality: 2
05-25 09:15:35.675  1321  1321 I SystemServiceManager: Starting com.android.server.security.authenticationpolicy.AuthenticationPolicyService
05-25 09:15:35.676  1321  1321 I SystemServiceManager: Starting com.android.server.app.AppLockManagerService$Lifecycle
05-25 09:15:35.680  1321  1321 I SystemServiceManager: Starting com.android.server.display.FreeformService
05-25 09:15:35.697  1321  1321 I SystemServiceManager: Starting com.android.server.pm.ShortcutService$Lifecycle
05-25 09:15:35.701  1321  1321 I SystemServiceManager: Starting com.android.server.pm.LauncherAppsService
05-25 09:15:35.706  1321  1321 I SystemServiceManager: Starting com.android.server.pm.CrossProfileAppsService
05-25 09:15:35.707  1321  1321 I SystemServiceManager: Starting com.android.server.pocket.PocketService
05-25 09:15:35.719  1321  1321 I SystemServiceManager: Starting com.android.server.people.PeopleService
05-25 09:15:35.720  1321  1321 I SystemServiceManager: Starting com.android.server.media.metrics.MediaMetricsManagerService
05-25 09:15:35.722  1321  1321 I SystemServiceManager: Starting com.android.server.pm.BackgroundInstallControlService
05-25 09:15:35.724  1321  1321 I SystemServiceManager: Starting com.android.server.voltage.CustomDeviceConfigService
05-25 09:15:35.725  1321  1321 I SystemServiceManager: Starting com.android.server.custom.LineageHardwareService
05-25 09:15:35.727  1321  1321 I SystemServiceManager: Starting com.android.server.custom.display.LiveDisplayService
05-25 09:15:35.732  1321  1321 I SystemServiceManager: Starting com.android.server.custom.health.HealthInterfaceService
05-25 09:15:35.733  1321  1321 I SystemServiceManager: Starting com.android.server.HideAppListService
05-25 09:15:35.733  1321  1321 I HideAppListService: Starting HideAppListService
05-25 09:15:35.733  1321  1321 I SystemServiceManager: Starting com.android.server.GameSpaceManagerService
05-25 09:15:35.734  1321  1321 I SystemServiceManager: Starting com.android.server.media.projection.MediaProjectionManagerService
05-25 09:15:35.740  1321  1321 I SystemServiceManager: Starting com.android.server.slice.SliceManagerService$Lifecycle
05-25 09:15:35.743  1321  1321 I SystemServiceManager: Starting com.android.server.stats.StatsCompanion$Lifecycle
05-25 09:15:35.746  1321  1321 I SystemServiceManager: Starting com.android.server.scheduling.RebootReadinessManagerService$Lifecycle
05-25 09:15:35.752  1321  1321 I SystemServiceManager: Starting com.android.server.stats.pull.StatsPullAtomService
05-25 09:15:35.752  1321  1321 I SystemServiceManager: Starting com.android.server.stats.bootstrap.StatsBootstrapAtomService$Lifecycle
05-25 09:15:35.753  1321  1321 I SystemServiceManager: Starting com.android.server.incident.IncidentCompanionService
05-25 09:15:35.755  1321  1321 I SystemServiceManager: Starting com.android.server.sdksandbox.SdkSandboxManagerService$Lifecycle
05-25 09:15:35.763  1321  1321 I SystemServiceManager: Starting com.android.server.adservices.AdServicesManagerService$Lifecycle
05-25 09:15:35.766  1321  1321 I SystemServiceManager: Starting com.android.server.ondevicepersonalization.OnDevicePersonalizationSystemService$Lifecycle
05-25 09:15:35.767  1321  1321 I ondevicepersonalization: OnDevicePersonalizationSystemService started!
05-25 09:15:35.768  1321  1321 I SystemServiceManager: Starting android.os.profiling.ProfilingService$Lifecycle
05-25 09:15:35.774  1321  1321 I SystemServiceManager: Starting com.android.server.MmsServiceBroker
05-25 09:15:35.775  1321  1321 I SystemServiceManager: Starting com.android.server.autofill.AutofillManagerService
05-25 09:15:35.778  1321  1321 E AutofillManagerServiceImpl: Bad service name: proton.android.pass/proton.android.pass.autofill.ProtonPassAutofillService
05-25 09:15:35.780  1321  1321 I SystemServiceManager: Starting com.android.server.credentials.CredentialManagerService
05-25 09:15:35.782  1321  1321 I SystemServiceManager: Starting com.android.server.clipboard.ClipboardService
05-25 09:15:35.785  1321  1321 I SystemServiceManager: Starting com.android.server.appbinding.AppBindingService$Lifecycle
05-25 09:15:35.786  1321  1321 I SystemServiceManager: Starting com.android.server.tracing.TracingServiceProxy
05-25 09:15:35.789   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.authsecret@1.0::IAuthSecret/default in either framework or device VINTF manifest.
05-25 09:15:35.789  1321  1321 I LockSettingsService: Device doesn't implement AuthSecret HAL
05-25 09:15:35.794  1321  1321 I SystemServiceManager: Starting phase 480
05-25 09:15:35.798   822   826 I ServiceManagerCppClient: Waiting for service 'statscompanion' on '/dev/binder' successful after waiting 201ms
05-25 09:15:35.809  1321  1321 W PocketService: Un-handled boot phase:480
05-25 09:15:35.809  1321  1321 I SystemServiceManager: Starting phase 500
05-25 09:15:35.810  1321  1321 E StatsPullAtomCallbackImpl: Failed to start PowerStatsService statsd pullers
05-25 09:15:35.813  1321  1321 E BatteryStatsService: Could not register PowerStatsInternal
05-25 09:15:35.817   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.power.stats@1.0::IPowerStats/default in either framework or device VINTF manifest.
05-25 09:15:35.817  1321  1366 E BatteryStatsService: Unable to load Power.Stats.HAL. Setting rail availability to false
05-25 09:15:35.817  1321  1366 E BluetoothAdapter: Bluetooth service is null
05-25 09:15:35.851  1321  1373 W KeyguardServiceDelegate: onScreenTurningOn(): no keyguard service!
05-25 09:15:35.870  1321  1321 W SystemServiceManager: Service com.android.server.power.PowerManagerService took 51 ms in onBootPhase
05-25 09:15:35.880  1321  1356 E AppWidgetManager: Notify service of inheritance info
05-25 09:15:35.880  1321  1356 E AppWidgetManager: 	at com.android.server.appwidget.AppWidgetServiceImpl.ensureGroupStateLoadedLocked(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:22)
05-25 09:15:35.880  1321  1356 E AppWidgetManager: 	at com.android.server.appwidget.AppWidgetServiceImpl.getInstalledProvidersForProfile(go/retraceme 977a98ad21c90921ad02a9875dfbea15e51eaf39e928ddf36dbff916ca861046:76)
05-25 09:15:35.928  1321  1321 I WifiScanningService: Starting wifiscanner
05-25 09:15:35.930  1321  1321 I EthernetServiceImpl: Starting Ethernet service
05-25 09:15:35.930  1321  1623 I WifiContext: Got resolveInfos for com.android.server.wifi.intent.action.SERVICE_WIFI_RESOURCES_APK: [ResolveInfo{d3b8555 com.android.wifi.resources/android.app.Activity m=0x108000 userHandle=UserHandle{0}}]
05-25 09:15:35.944  1321  1619 I WifiService: WifiService starting up with Wi-Fi disabled
05-25 09:15:35.951  1321  1619 I WifiHalHidlImpl: Initializing the WiFi HAL
05-25 09:15:35.951  1321  1619 I WifiHalHidlImpl: initServiceManagerIfNecessaryLocked
05-25 09:15:35.953  1321  1619 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.2::IServiceManager/default
05-25 09:15:35.955  1321  1619 I WifiHalHidlImpl: initWifiIfNecessaryLocked
05-25 09:15:35.956   573   573 I hwservicemanager: Since android.hardware.wifi@1.0::IWifi/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-25 09:15:35.958   573   573 I hwservicemanager: Since android.hardware.wifi@1.0::IWifi/default is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-25 09:15:35.958  1321  1619 I HidlServiceManagement: getService: Trying again for android.hardware.wifi@1.0::IWifi/default...
05-25 09:15:35.999   573   573 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IKeySwapper/default in either framework or device VINTF manifest.
05-25 09:15:36.015  1321  1356 I android_os_HwBinder: HwBinder: Starting thread pool for getting: android.hidl.manager@1.0::IServiceManager/default
05-25 09:15:36.018  1321  1321 I SystemServiceManager: Starting com.android.server.policy.PermissionPolicyService
05-25 09:15:36.024  1321  1356 I HealthServiceWrapperHidl: health: HealthServiceWrapper listening to instance default
05-25 09:15:36.029  1321  1321 E UserManagerService: Auto-lock preference updated but private space user not found
05-25 09:15:36.031  1321  1321 I AS.AudioService: registerAudioPolicy for android.media.audiopolicy.AudioPolicy$1@e454328 u/pid:1000/1321 with config:reg:32:ap:0
05-25 09:15:36.038  1321  1356 I StatsPullAtomService: register thermal listener successfully
05-25 09:15:36.053  1321  1321 I SystemServiceManager: Starting com.android.server.crashrecovery.CrashRecoveryModule$Lifecycle
05-25 09:15:36.056  1694  1694 I android.hardware.wifi@1.0-service-lazy: Wifi Hal is booting up...
05-25 09:15:36.063  1694  1694 I HidlServiceManagement: Registered android.hardware.wifi@1.5::IWifi/default
05-25 09:15:36.064  1694  1694 I HidlServiceManagement: Removing namespace from process name android.hardware.wifi@1.0-service-lazy to wifi@1.0-service-lazy.
05-25 09:15:36.066  1321  1379 W DefaultPermGrantPolicy: No such package:com.google.android.apps.camera.services
05-25 09:15:36.067  1321  1379 W DefaultPermGrantPolicy: No such package:com.verizon.mips.services
05-25 09:15:36.068  1321  1379 W DefaultPermGrantPolicy: No such package:com.google.android.adservices
05-25 09:15:36.069  1321  1379 W DefaultPermGrantPolicy: No such package:com.google.android.apps.actionsservice
05-25 09:15:36.071   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.wifi@1.6::IWifi/default in either framework or device VINTF manifest.
05-25 09:15:36.073  1321  1321 I BrightnessSynchronizer: Initial brightness readings: 76(int), 0.2952756(float)
05-25 09:15:36.075  1321  1321 I SystemServiceManager: Starting com.android.server.app.GameManagerService$Lifecycle
05-25 09:15:36.088  1321  1321 I SystemServiceManager: Starting phase 520
05-25 09:15:36.103   573   573 I hwservicemanager: getTransport: Cannot find entry vendor.oplus.hardware.commondcs@1.0::ICommonDcsHalService/commondcsservice in either framework or device VINTF manifest.
05-25 09:15:36.104  1038  1038 E android.hardware.biometrics.fingerprint@2.1-service: service NULL
05-25 09:15:36.114  1321  1321 W PocketService: Un-handled boot phase:520
05-25 09:15:36.115  1321  1321 I SystemServiceManager: Starting com.android.safetycenter.SafetyCenterService
05-25 09:15:36.129  1321  1321 I SystemServiceManager: Starting com.android.server.appsearch.AppSearchModule$Lifecycle
05-25 09:15:36.143  1321  1321 I AppSearchModule: AppsIndexer service is disabled.
05-25 09:15:36.143  1321  1321 I AppSearchModule: AppOpenEventIndexer service is disabled.
05-25 09:15:36.143  1321  1321 I SystemServiceManager: Starting com.android.server.media.MediaCommunicationService
05-25 09:15:36.145  1321  1321 I SystemServiceManager: Starting com.android.server.compat.overrides.AppCompatOverridesService$Lifecycle
05-25 09:15:36.147  1321  1321 I SystemServiceManager: Starting com.android.server.power.SleepModeService
05-25 09:15:36.152  1321  1321 I SystemServiceManager: Starting com.android.server.healthconnect.HealthConnectManagerService
05-25 09:15:36.162  1321  1321 I SystemServiceManager: Starting com.android.server.devicelock.DeviceLockService
05-25 09:15:36.166  1321  1321 I DeviceLockService: Registering device_lock
05-25 09:15:36.167  1321  1321 I SystemServiceManager: Starting com.android.server.SensitiveContentProtectionManagerService
05-25 09:15:36.231  1321  1321 E ActivityManager: Unable to find com.android.overlay.permissioncontroller/u0
05-25 09:15:36.235  1321  1321 E ActivityManager: Unable to find com.google.android.printservice.recommendation/u0
05-25 09:15:36.268  1321  1321 I SystemServer: Making services ready
05-25 09:15:36.268  1321  1321 I SystemServiceManager: Starting phase 550
05-25 09:15:36.276  1321  1321 I ThermalManagerService$ThermalHalWrapper: Thermal HAL 2.0 service connected.
05-25 09:15:36.276   844   844 I <EMAIL>: thermal_zone_num are changed0
05-25 09:15:36.277   844   844 W <EMAIL>: tz_data_v1[2].tz_idx:0
05-25 09:15:36.277   844   844 W <EMAIL>: tz_data_v1[3].tz_idx:2
05-25 09:15:36.277   844   844 W <EMAIL>: tz_data_v1[5].tz_idx:3
05-25 09:15:36.277   844   844 W <EMAIL>: tz_data_v1[0].tz_idx:5
05-25 09:15:36.277   844   844 W <EMAIL>: tz_data_v1[1].tz_idx:5
05-25 09:15:36.277   844   844 W <EMAIL>: tz_data_v1[9].tz_idx:5
05-25 09:15:36.278   844   844 W <EMAIL>: init_tz_path_v1:find out tz path
05-25 09:15:36.278   844   844 W <EMAIL>: get_tz_map: tz0, name=mtktscpu, label=CPU, muti_tz_num=1
05-25 09:15:36.278   844   844 W <EMAIL>: tz_idx0:5, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-25 09:15:36.278   844   844 W <EMAIL>: get_tz_map: tz1, name=mtktscpu, label=GPU, muti_tz_num=1
05-25 09:15:36.278   844   844 W <EMAIL>: tz_idx0:5, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-25 09:15:36.278   844   844 W <EMAIL>: get_tz_map: tz2, name=mtktsbattery, label=BATTERY, muti_tz_num=1
05-25 09:15:36.278   844   844 W <EMAIL>: tz_idx0:0, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-25 09:15:36.278   844   844 W <EMAIL>: get_tz_map: tz3, name=mtktsAP, label=SKIN, muti_tz_num=1
05-25 09:15:36.278   844   844 W <EMAIL>: tz_idx0:2, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-25 09:15:36.278   844   844 W <EMAIL>: get_tz_map: tz4, name=notsupport, label=USB_PORT, muti_tz_num=1
05-25 09:15:36.278   844   844 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-25 09:15:36.278   844   844 W <EMAIL>: get_tz_map: tz5, name=mtktsbtsmdpa, label=POWER_AMPLIFIER, muti_tz_num=1
05-25 09:15:36.278   844   844 W <EMAIL>: tz_idx0:3, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-25 09:15:36.278   844   844 W <EMAIL>: get_tz_map: tz6, name=notsupport, label=BCL_VOLTAGE, muti_tz_num=1
05-25 09:15:36.278   844   844 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-25 09:15:36.278   844   844 W <EMAIL>: get_tz_map: tz7, name=notsupport, label=BCL_CURRENT, muti_tz_num=1
05-25 09:15:36.278   844   844 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-25 09:15:36.278   844   844 W <EMAIL>: get_tz_map: tz8, name=notsupport, label=BCL_PERCENTAGE, muti_tz_num=1
05-25 09:15:36.278   844   844 W <EMAIL>: tz_idx0:-1, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-25 09:15:36.278   844   844 W <EMAIL>: get_tz_map: tz9, name=mtktscpu, label=NPU, muti_tz_num=1
05-25 09:15:36.278   844   844 W <EMAIL>: tz_idx0:5, tz_idx1:-1,tz_idx2:-1,tz_idx3:-1,tz_idx4:-1,
05-25 09:15:36.278   844   844 I <EMAIL>: fill_temperatures filterType0 name: CPU type: CPU throttlingStatus: NONE value: 57.524 ret_temps size 0
05-25 09:15:36.278   844   844 I <EMAIL>: fill_temperatures filterType0 name: GPU type: GPU throttlingStatus: NONE value: 57.524 ret_temps size 1
05-25 09:15:36.279   844   844 I <EMAIL>: fill_temperatures filterType0 name: BATTERY type: BATTERY throttlingStatus: NONE value: 37 ret_temps size 2
05-25 09:15:36.279   844   844 I <EMAIL>: fill_temperatures filterType0 name: SKIN type: SKIN throttlingStatus: NONE value: 45.23 ret_temps size 3
05-25 09:15:36.279   844   844 I <EMAIL>: fill_temperatures filterType0 name: POWER_AMPLIFIER type: POWER_AMPLIFIER throttlingStatus: NONE value: 43.902 ret_temps size 4
05-25 09:15:36.279   844   844 I <EMAIL>: fill_temperatures filterType0 name: NPU type: NPU throttlingStatus: NONE value: 57.524 ret_temps size 5
05-25 09:15:36.280   844   844 I <EMAIL>: fill_thresholds filterType1 name: SKIN type: SKIN hotThrottlingThresholds: 50 vrThrottlingThreshold: 50 ret_thresholds size 0
05-25 09:15:36.284  1321  1321 W ActivityManager: Too early to start/bind service in system_server: Phase=550 ComponentInfo{com.android.server.telecom/com.android.server.telecom.components.TelecomService}
05-25 09:15:36.285  1321  1321 W ActivityManager: Too early to start/bind service in system_server: Phase=550 ComponentInfo{com.android.server.telecom/com.android.server.telecom.components.TelecomService}
05-25 09:15:36.292   573   573 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IGloveMode/default in either framework or device VINTF manifest.
05-25 09:15:36.293   573   573 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IStylusMode/default in either framework or device VINTF manifest.
05-25 09:15:36.296   573   573 I hwservicemanager: getTransport: Cannot find entry vendor.lineage.touch@1.0::IHighTouchPollingRate/default in either framework or device VINTF manifest.
05-25 09:15:36.326  1321  1610 W StorageManagerService: Failed to get storage lifetime
05-25 09:15:36.337  1012  1212 I Codec2Client: Available Codec2 services: "default" "software"
05-25 09:15:36.399  1321  1321 W SystemServiceManager: Service com.android.server.NetworkStatsServiceInitializer took 79 ms in onBootPhase
05-25 09:15:36.399  1321  1321 I ConnectivityServiceInitializerB: Starting vcn_management
05-25 09:15:36.405  1321  1633 I VcnManagementService: new snapshot: TelephonySubscriptionSnapshot{ mActiveDataSubId=-1, mSubIdToInfoMap={}, mSubIdToCarrierConfigMap={}, mPrivilegedPackages={} }
05-25 09:15:36.420  1321  1643 I AS.AudioDeviceBroker: setBluetoothScoOn: false, mBluetoothScoOn: false, btScoRequesterUId: -1, from: resetBluetoothSco
05-25 09:15:36.421   847   847 I android.hardware.usb@1.3-service-mediatekv2: port0
05-25 09:15:36.421  1321  1358 I ActivityManager: Start proc 1712:com.android.systemui/u0a226 for service {com.android.systemui/com.android.systemui.wallpapers.ImageWallpaper}
05-25 09:15:36.421   847   847 I android.hardware.usb@1.3-service-mediatekv2: HAL version V1_2
05-25 09:15:36.421   847   847 I android.hardware.usb@1.3-service-mediatekv2: 0:port0 connected:1 canChangeMode:1 canChagedata:0 canChangePower:0 supportedModes:3
05-25 09:15:36.421   847   847 I android.hardware.usb@1.3-service-mediatekv2: ContaminantDetectionStatus:2 ContaminantProtectionStatus:0
05-25 09:15:36.423  1321  1639 W BroadcastLoopers: Found previously unknown looper Thread[AudioDeviceBroker,5,main]
05-25 09:15:36.426  1321  1321 I AppLockManagerService: onBootCompleted
05-25 09:15:36.427   880  1566 I AudioFlinger: systemReady
05-25 09:15:36.442  1321  1321 I LMOFreeform/LMOFreeformUIService: add SystemService: com.libremobileos.freeform.server.LMOFreeformUIService@6bcea8b
05-25 09:15:36.443  1321  1321 W PocketService: Un-handled boot phase:550
05-25 09:15:36.445  1321  1321 I AppBindingService: Updating constants with: null
05-25 09:15:36.627  1321  1321 W SystemServiceManager: Service com.android.server.policy.PermissionPolicyService took 181 ms in onBootPhase
05-25 09:15:36.635  1012  1212 I Codec2InfoBuilder: adding type 'audio/x-adpcm-dvi-ima'
05-25 09:15:36.636  1012  1212 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-dvi-ima
05-25 09:15:36.640  1012  1212 I Codec2InfoBuilder: adding type 'audio/x-adpcm-ms'
05-25 09:15:36.641  1012  1212 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-ms
05-25 09:15:36.649  1012  1212 I Codec2InfoBuilder: adding type 'audio/alac'
05-25 09:15:36.649  1012  1212 W AudioCapabilities: Unsupported mediaType audio/alac
05-25 09:15:36.653  1012  1212 I Codec2InfoBuilder: adding type 'audio/ape'
05-25 09:15:36.654  1012  1212 W AudioCapabilities: Unsupported mediaType audio/ape
05-25 09:15:36.673  1321  1596 W PinnerService: Could not find pinlist.meta for "/product/app/webview/webview.apk": pinning as blob
05-25 09:15:36.748   821   821 I netd    : networkSetPermissionForUser(1, [1002, 10160, 10191, 10204, 10221, 10222, 10271, 10272, 10273, 10282, 10355]) <0.04ms>
05-25 09:15:36.748   821   821 I netd    : networkSetPermissionForUser(2, [1000, 1001, 1073, 2000, 10152, 10171, 10183, 10199, 10201, 10205, 10226, 10234, 10251]) <0.01ms>
05-25 09:15:36.789  1321  1321 I SystemServiceManager: Starting phase 600
05-25 09:15:36.802  1712  1732 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-25 09:15:36.804  1321  1321 I ServiceWatcher: [network] chose new implementation 10369/app.grapheneos.networklocation/.NetworkLocationService@0
05-25 09:15:36.810  1321  1321 I ServiceWatcher: [fused] chose new implementation 1000/com.android.location.fused/.FusedLocationService@0
05-25 09:15:36.839  1004  1130 W MNLD    : hal_gps_init: hal_gps_init
05-25 09:15:36.840  1321  1321 I GnssLocationProviderJni: Unable to initialize IGnssGeofencing interface.
05-25 09:15:36.842  1321  1321 I GnssManager: gnss hal initialized
05-25 09:15:36.844  1321  1321 I ServiceWatcher: [GeocoderProxy] chose new implementation null
05-25 09:15:36.845  1321  1321 I ServiceWatcher: [HardwareActivityRecognitionProxy] chose new implementation null
05-25 09:15:36.845  1321  1321 I ServiceWatcher: [GeofenceProxy] chose new implementation null
05-25 09:15:36.884  1321  1321 I ServiceWatcher: [TrustManagerService] chose new implementation null
05-25 09:15:36.901  1321  1321 W PocketService: Un-handled boot phase:600
05-25 09:15:36.902  1321  1321 W AlarmManager: Unrecognized alarm listener com.android.server.stats.StatsCompanionService$PullingAlarmListener@f587744
05-25 09:15:36.902  1321  1321 W AlarmManager: Unrecognized alarm listener com.android.server.stats.StatsCompanionService$PeriodicAlarmListener@f99b02d
05-25 09:15:36.916  1321  1321 I StatsCompanionService: Told statsd that StatsCompanionService is alive.
05-25 09:15:36.937  1321  1358 I ActivityManager: Start proc 1814:app.grapheneos.networklocation/u0a369 for service {app.grapheneos.networklocation/app.grapheneos.networklocation.NetworkLocationService}
05-25 09:15:36.941  1321  1802 E StatsCompanionService: Could not get installer for package: com.google.android.trichromelibrary
05-25 09:15:36.941  1321  1802 E StatsCompanionService: android.content.pm.PackageManager$NameNotFoundException: com.google.android.trichromelibrary
05-25 09:15:36.941  1321  1802 E StatsCompanionService: 	at android.app.ApplicationPackageManager.getInstallSourceInfo(ApplicationPackageManager.java:2772)
05-25 09:15:36.941  1321  1802 E StatsCompanionService: 	at com.android.server.stats.StatsCompanionService.getInstallerPackageName(StatsCompanionService.java:153)
05-25 09:15:36.941  1321  1802 E StatsCompanionService: 	at com.android.server.stats.StatsCompanionService.$r8$lambda$MBPStrBhgnmbybdtzkoTAe-YOYw(StatsCompanionService.java:229)
05-25 09:15:36.941  1321  1802 E StatsCompanionService: 	at com.android.server.stats.StatsCompanionService$$ExternalSyntheticLambda1.run(R8$$SyntheticClass:0)
05-25 09:15:36.941  1321  1802 E StatsCompanionService: 	at android.os.Handler.handleCallback(Handler.java:991)
05-25 09:15:36.941  1321  1802 E StatsCompanionService: 	at android.os.Handler.dispatchMessage(Handler.java:102)
05-25 09:15:36.941  1321  1802 E StatsCompanionService: 	at android.os.Looper.loopOnce(Looper.java:232)
05-25 09:15:36.941  1321  1802 E StatsCompanionService: 	at android.os.Looper.loop(Looper.java:317)
05-25 09:15:36.941  1321  1802 E StatsCompanionService: 	at android.os.HandlerThread.run(HandlerThread.java:85)
05-25 09:15:36.942  1321  1358 I ActivityManager: Start proc 1818:com.android.networkstack.process/1073 for service {com.android.networkstack/com.android.server.NetworkStackService}
05-25 09:15:36.987  1321  1321 I MR2ServiceImpl: switchUser | user: 0
05-25 09:15:36.988  1321  1321 I MmsServiceBroker: Delay connecting to MmsService until an API is called
05-25 09:15:37.001  1321  1321 I SystemServiceManager: Calling onStartUser 0
05-25 09:15:37.027  1712  1712 E SharedConnectivityManager: To support shared connectivity service on this device, the service's package name and intent action strings must not be empty
05-25 09:15:37.030  1012  1212 I Codec2InfoBuilder: adding type 'audio/mp4a-latm'
05-25 09:15:37.035  1321  1600 I BluetoothSystemServer: AirplaneModeListener: Init completed. isOn=false, isOnOverrode=false
05-25 09:15:37.035  1321  1600 I BluetoothSystemServer: SatelliteModeListener: Initialized successfully with state: false
05-25 09:15:37.040  1012  1212 I Codec2InfoBuilder: adding type 'audio/mp4a-latm'
05-25 09:15:37.048  1012  1212 I Codec2InfoBuilder: adding type 'audio/3gpp'
05-25 09:15:37.057  1012  1212 I Codec2InfoBuilder: adding type 'audio/3gpp'
05-25 09:15:37.062  1012  1212 I Codec2InfoBuilder: adding type 'audio/amr-wb'
05-25 09:15:37.069  1012  1212 I Codec2InfoBuilder: adding type 'audio/amr-wb'
05-25 09:15:37.086  1321  1321 W VoiceInteractionManager: no available voice recognition services found for user 0
05-25 09:15:37.087  1321  1321 I AppLockManagerService: onUserStarting: userId = 0
05-25 09:15:37.112  1814  1814 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-25 09:15:37.121  1012  1212 I Codec2InfoBuilder: adding type 'audio/flac'
05-25 09:15:37.127  1012  1212 I Codec2InfoBuilder: adding type 'audio/flac'
05-25 09:15:37.133  1012  1212 I Codec2InfoBuilder: adding type 'audio/g711-alaw'
05-25 09:15:37.139  1012  1212 I Codec2InfoBuilder: adding type 'audio/g711-mlaw'
05-25 09:15:37.153  1890  1890 I WebViewZygoteInit: Starting WebViewZygoteInit
05-25 09:15:37.162  1321  1321 I SystemServiceManager: Starting com.android.server.scheduling.RebootReadinessManagerService$Lifecycle
05-25 09:15:37.162  1321  1321 I SystemServiceManager: Not starting an already started service com.android.server.scheduling.RebootReadinessManagerService$Lifecycle
05-25 09:15:37.168  1321  1345 I ServiceWatcher: [network] connected to {app.grapheneos.networklocation/app.grapheneos.networklocation.NetworkLocationService}
05-25 09:15:37.172  1012  1212 I Codec2InfoBuilder: adding type 'audio/mpeg'
05-25 09:15:37.175  1712  1750 E AppWidgetManager: Notify service of inheritance info
05-25 09:15:37.175  1712  1750 E AppWidgetManager: 	at com.android.internal.appwidget.IAppWidgetService$Stub$Proxy.getInstalledProvidersForProfile(IAppWidgetService.java:1071)
05-25 09:15:37.178  1872  1872 W ContextImpl: Failed to ensure /data/user/0/com.android.se/cache: mkdir failed: ENOENT (No such file or directory)
05-25 09:15:37.178  1872  1872 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-25 09:15:37.192  1012  1212 I Codec2InfoBuilder: adding type 'audio/opus'
05-25 09:15:37.195  1890  1890 I WebViewZygoteInit: Beginning application preload for com.android.webview
05-25 09:15:37.198  1012  1212 I Codec2InfoBuilder: adding type 'audio/opus'
05-25 09:15:37.203  1012  1212 I Codec2InfoBuilder: adding type 'audio/raw'
05-25 09:15:37.205  1872  1872 I SecureElementService: main onCreate
05-25 09:15:37.206  1872  1872 I SecureElementService: Check if terminal eSE1 is available.
05-25 09:15:37.207   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.secure_element@1.2::ISecureElement/eSE1 in either framework or device VINTF manifest.
05-25 09:15:37.207  1012  1212 I Codec2InfoBuilder: adding type 'audio/vorbis'
05-25 09:15:37.211  1321  1345 I ServiceWatcher: [fused] connected to {com.android.location.fused/com.android.location.fused.FusedLocationService}
05-25 09:15:37.214  1321  1321 I ExplicitHealthCheckController: Service not ready to get health check supported packages. Binding...
05-25 09:15:37.216  1321  1321 I ExplicitHealthCheckController: Explicit health check service is bound
05-25 09:15:37.217   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.secure_element@1.1::ISecureElement/eSE1 in either framework or device VINTF manifest.
05-25 09:15:37.222   573   573 I hwservicemanager: getTransport: Cannot find entry android.hardware.secure_element@1.0::ISecureElement/eSE1 in either framework or device VINTF manifest.
05-25 09:15:37.223  1872  1872 I SecureElementService: No HAL implementation for eSE1
05-25 09:15:37.223  1872  1872 I SecureElementService: Check if terminal SIM1 is available.
05-25 09:15:37.225  1321  1610 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-dvi-ima
05-25 09:15:37.225  1321  1610 W AudioCapabilities: Unsupported mediaType audio/x-adpcm-ms
05-25 09:15:37.225  1321  1610 W AudioCapabilities: Unsupported mediaType audio/alac
05-25 09:15:37.225  1321  1610 W AudioCapabilities: Unsupported mediaType audio/ape
05-25 09:15:37.226   573   573 I hwservicemanager: Since android.hardware.secure_element@1.2::ISecureElement/SIM1 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-25 09:15:37.227   573   573 I hwservicemanager: Since android.hardware.secure_element@1.2::ISecureElement/SIM1 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-25 09:15:37.227  1872  1872 I HidlServiceManagement: getService: Trying again for android.hardware.secure_element@1.2::ISecureElement/SIM1...
05-25 09:15:37.231  1321  1345 I ServiceWatcher: [GeocoderProxy] chose new implementation null
05-25 09:15:37.232  1321  1345 I ServiceWatcher: [HardwareActivityRecognitionProxy] chose new implementation null
05-25 09:15:37.235  1321  1345 I ServiceWatcher: [GeofenceProxy] chose new implementation null
05-25 09:15:37.235  1321  1727 I Codec2Client: Available Codec2 services: "default" "software"
05-25 09:15:37.239  1321  1345 I ServiceWatcher: [TrustManagerService] chose new implementation null
05-25 09:15:37.241  1321  1638 W BroadcastLoopers: Found previously unknown looper Thread[AudioService Broadcast,5,main]
05-25 09:15:37.242  1890  1890 I WebViewZygoteInit: Application preload done
05-25 09:15:37.254  1321  1610 W AudioCapabilities: Unsupported mime audio/x-adpcm-dvi-ima
05-25 09:15:37.255  1321  1610 W AudioCapabilities: Unsupported mime audio/x-adpcm-dvi-ima
05-25 09:15:37.255  1321  1610 W AudioCapabilities: Unsupported mime audio/x-adpcm-ms
05-25 09:15:37.255   573  1934 I hwservicemanager: Tried to start android.hardware.secure_element@1.2::ISecureElement/SIM1 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-25 09:15:37.256  1321  1610 W AudioCapabilities: Unsupported mime audio/x-adpcm-ms
05-25 09:15:37.256  1321  1610 W AudioCapabilities: Unsupported mime audio/alac
05-25 09:15:37.256  1321  1610 W AudioCapabilities: Unsupported mime audio/alac
05-25 09:15:37.256  1321  1610 W AudioCapabilities: Unsupported mime audio/ape
05-25 09:15:37.256  1321  1610 W AudioCapabilities: Unsupported mime audio/ape
05-25 09:15:37.279   573  1936 I hwservicemanager: Tried to start android.hardware.secure_element@1.2::ISecureElement/SIM1 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-25 09:15:37.287  1321  1321 I Telecom : SystemStateHelper: Registering broadcast receiver: android.content.IntentFilter@cefd70d: TS.init@AAA
05-25 09:15:37.287  1321  1321 I Telecom : SystemStateHelper: Registering broadcast receiver: android.content.IntentFilter@6b2b6c2: TS.init@AAA
05-25 09:15:37.294  1321  1321 I Telecom : CallAudioRouteController: calculateSupportedRouteMaskInit: is wired headset plugged in - false: TS.init@AAA
05-25 09:15:37.294  1321  1321 I Telecom : AudioRoute$Factory: createRetry; type=TYPE_SPEAKER, address=null, retryCount=2: TS.init@AAA
05-25 09:15:37.294  1321  1321 I Telecom : AudioRoute$Factory: type: 1: TS.init@AAA
05-25 09:15:37.294  1321  1321 I Telecom : AudioRoute$Factory: type: 2: TS.init@AAA
05-25 09:15:37.294  1321  1321 I Telecom : AudioRoute$Factory: createRetry; type=TYPE_EARPIECE, address=null, retryCount=2: TS.init@AAA
05-25 09:15:37.294  1321  1321 I Telecom : AudioRoute$Factory: type: 1: TS.init@AAA
05-25 09:15:37.296  1712  1756 I CameraManagerGlobal: Connecting to camera service
05-25 09:15:37.316  1005  1941 I AttributionAndPermissionUtils: checkPermission checkPermission (forDataDelivery 0 startDataDelivery 0): Permission soft denied for client attribution [uid 10226, pid 1712, packageName "<unknown>"]
05-25 09:15:37.319  1321  1961 I Telecom : CallAudioModeStateMachine: Audio focus entering UNFOCUSED state
05-25 09:15:37.319  1321  1961 I Telecom : CallAudioModeStateMachine: Message received: null.: TS.init->CAMSM.pM_1@AAA
05-25 09:15:37.328  1321  1321 I Telecom : MissedCallNotifierImpl: reloadFromDatabase: Boot not yet complete -- call log db may not be available. Deferring loading until boot complete for user 0: TS.init@AAA
05-25 09:15:37.337  1321  1321 W Looper  : Slow dispatch took 101ms main h=android.app.ActivityThread$H c=android.app.LoadedApk$ServiceDispatcher$RunConnection@f68463 m=0
05-25 09:15:37.337  1321  1321 I ConnectivityModuleConnector: Networking module service connected
05-25 09:15:37.337  1321  1321 I NetworkStackClient: Network stack service connected
05-25 09:15:37.340  1321  1321 W TrustManagerService: EXTRA_USER_HANDLE missing or invalid, value=0
05-25 09:15:37.341  1321  1321 I ContentSuggestionsManagerService: Updating for user 0: disabled=false
05-25 09:15:37.341  1897  1897 E CarrierIdProvider: read carrier list from ota pb failure: java.io.FileNotFoundException: /data/misc/carrierid/carrier_list.pb: open failed: ENOENT (No such file or directory)
05-25 09:15:37.341  1321  1321 E ContentSuggestionsPerUserService: Bad service name: com.google.android.as/com.google.android.apps.miphone.aiai.app.AiAiContentSuggestionsService
05-25 09:15:37.341  1321  1321 I AutofillManagerService: Updating for user 0: disabled=false
05-25 09:15:37.341  1321  1321 E AutofillManagerServiceImpl: Bad service name: proton.android.pass/proton.android.pass.autofill.ProtonPassAutofillService
05-25 09:15:37.347  1321  1321 W ActivityManager: Unable to start service Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener } U=0: not found
05-25 09:15:37.347  1321  1321 W NotificationManagerService.NotificationListeners: Unable to bind notification listener service: Intent { act=android.service.notification.NotificationListenerService cmp=com.android.launcher3/.notification.NotificationListener (has extras) } in user 0
05-25 09:15:37.357  1321  1321 I ConnectivityModuleConnector: Networking module service connected
05-25 09:15:37.359  1321  1345 W PermissionService: getPermissionFlags: Unknown user -1
05-25 09:15:37.359  1321  1345 W PermissionService: getPermissionFlags: Unknown user -1
05-25 09:15:37.361  1321  1358 I ActivityManager: Start proc 1971:com.android.permissioncontroller/u0a266 for broadcast {com.android.permissioncontroller/com.android.permissioncontroller.privacysources.SafetyCenterReceiver}
05-25 09:15:37.442   883  2001 W gpuservice: AIBinder_linkToDeath is being called with a non-null cookie and no onUnlink callback set. This might not be intended. AIBinder_DeathRecipient_setOnUnlinked should be called first.
05-25 09:15:37.466  1897  1897 E RILJ    : Feature android.hardware.telephony.ims is declared, but service IMS is missing [PHONE0]
05-25 09:15:37.467  1897  1897 E RILJ    : Feature android.hardware.telephony.messaging is declared, but service MESSAGING is missing [PHONE0]
05-25 09:15:37.467  1897  1897 E RILJ    : Feature android.hardware.telephony.data is declared, but service DATA is missing [PHONE0]
05-25 09:15:37.468  1897  1897 E RILJ    : Feature android.hardware.telephony.calling is declared, but service VOICE is missing [PHONE0]
05-25 09:15:37.477  1907  1907 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-25 09:15:37.500  1897  1897 E RILJ    : Feature android.hardware.telephony.ims is declared, but service IMS is missing [PHONE1]
05-25 09:15:37.500  1897  1897 E RILJ    : Feature android.hardware.telephony.messaging is declared, but service MESSAGING is missing [PHONE1]
05-25 09:15:37.500  1897  1897 E RILJ    : Feature android.hardware.telephony.data is declared, but service DATA is missing [PHONE1]
05-25 09:15:37.502  1897  1897 E RILJ    : Feature android.hardware.telephony.calling is declared, but service VOICE is missing [PHONE1]
05-25 09:15:37.526  1321  1348 W KeyguardServiceDelegate: onScreenTurningOn(): no keyguard service!
05-25 09:15:37.545  1408  1413 E RILC-OplusAppRadio: oplusAppRadioService: oplusAppRadioService[0]->mRadioIndicationOplus == NULL
05-25 09:15:37.545  1408  1413 E RILC-OplusRadioAIDL: oplusRadioServiceAIDL: oplusRadioServiceAIDL[0]->mRadioIndicationOplus == NULL
05-25 09:15:37.601  1897  1897 E SatelliteController: SatelliteController was not yet initialized.
05-25 09:15:37.616  1321  1358 I ActivityManager: Start proc 2051:com.android.smspush/u0a237 for service {com.android.smspush/com.android.smspush.WapPushManager}
05-25 09:15:37.661  1897  1897 E EmergencyNumberTracker: [0]Cache ota emergency database IOException: java.io.FileNotFoundException: /data/misc/emergencynumberdb/emergency_number_db: open failed: ENOENT (No such file or directory)
05-25 09:15:37.668  2051  2051 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-25 09:15:37.681  1321  1994 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-25 09:15:37.683  1321  1998 I DefaultPermGrantPolicy: Granting permissions to enabled data services for user:0
05-25 09:15:37.689  1321  1994 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-25 09:15:37.708  2038  2068 E CellBroadcastConfigService: fails to setCellBroadcastRanges
05-25 09:15:37.716  2038  2068 E CellBroadcastConfigService: fails to setCellBroadcastRanges
05-25 09:15:37.728  1897  1897 E SatelliteController: SatelliteController was not yet initialized.
05-25 09:15:37.763  1712  1712 I SystemUIService: Found SurfaceFlinger's GPU Priority: 13143
05-25 09:15:37.763  1712  1712 I SystemUIService: Setting SysUI's GPU Context priority to: 12545
05-25 09:15:37.764  1897  1897 E EmergencyNumberTracker: [1]Cache ota emergency database IOException: java.io.FileNotFoundException: /data/misc/emergencynumberdb/emergency_number_db: open failed: ENOENT (No such file or directory)
05-25 09:15:37.781  1321  1898 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-25 09:15:37.782  1321  1994 I DefaultPermGrantPolicy: Granting permissions to enabled data services for user:0
05-25 09:15:37.788  1321  1339 I DefaultPermGrantPolicy: Revoking permissions from disabled data services for user:0
05-25 09:15:37.792  1321  1951 W Telecom : BluetoothDeviceManager: getBluetoothHeadset: Acquire BluetoothHeadset service failed due to: java.util.concurrent.TimeoutException
05-25 09:15:37.792  1321  1951 I Telecom : BluetoothRouteManager: getBluetoothAudioConnectedDevice: no service available.
05-25 09:15:37.793  1321  1955 I Telecom : CallAudioRouteController: Message received: BT_AUDIO_DISCONNECTED=1301, arg1=0
05-25 09:15:37.829  1321  1956 W WifiService: Attempt to retrieve WifiConfiguration with invalid scanResult List
05-25 09:15:37.835  1321  1898 W WifiService: Attempt to retrieve WifiConfiguration with invalid scanResult List
05-25 09:15:37.835  1321  1337 E WifiService: Attempt to retrieve passpoint with invalid scanResult List
05-25 09:15:37.836  1321  1898 W WifiService: Attempt to retrieve OsuProviders with invalid scanResult List
05-25 09:15:37.862  1897  2082 I ImsResolver: Initializing cache.
05-25 09:15:37.873  1897  1897 E SatelliteModemInterface: Unable to bind to the satellite service because the package is undefined.
05-25 09:15:37.883  1408  1413 E RILC-OplusAppRadio: oplusAppRadioService: oplusAppRadioService[0]->mRadioIndicationOplus == NULL
05-25 09:15:37.883  1408  1413 E RILC-OplusRadioAIDL: oplusRadioServiceAIDL: oplusRadioServiceAIDL[0]->mRadioIndicationOplus == NULL
05-25 09:15:37.903  1712  2087 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-25 09:15:37.924  1321  1358 I ActivityManager: Start proc 2103:com.android.launcher3/u0a230 for service {com.android.launcher3/com.android.quickstep.TouchInteractionService}
05-25 09:15:37.929  1897  1897 I TelephonyRcsService: updateFeatureControllers: oldSlots=0, newNumSlots=2
05-25 09:15:37.976  1321  1321 I AS.AudioService: onSubscriptionsChanged()
05-25 09:15:37.980  1321  1633 I VcnManagementService: new snapshot: TelephonySubscriptionSnapshot{ mActiveDataSubId=2, mSubIdToInfoMap={}, mSubIdToCarrierConfigMap={}, mPrivilegedPackages={} }
05-25 09:15:37.998  1321  1356 E StatsPullAtomService: subInfo of subId 2 is invalid, ignored.
05-25 09:15:37.998  1321  1321 I AS.AudioService: onSubscriptionsChanged()
05-25 09:15:38.012  1897  1897 E RILJ    : getImei not supported on service MODEM < 2.1. [PHONE0]
05-25 09:15:38.014  1321  1356 E StatsPullAtomService: subInfo of subId 1 is invalid, ignored.
05-25 09:15:38.016  1897  1897 E RILJ    : setNullCipherAndIntegrityEnabled not supported on service NETWORK < 2.1. [PHONE0]
05-25 09:15:38.017  1897  1897 E RILJ    : setCellularIdentifierTransparencyEnabled not supported on service NETWORK < 2.2. [PHONE0]
05-25 09:15:38.018  1897  1897 E RILJ    : setSecurityAlgorithmsUpdatedEnabled not supported on service NETWORK < 2.2. [PHONE0]
05-25 09:15:38.026  1897  1897 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-25 09:15:38.033  2103  2103 W ContextImpl: Failed to update user.inode_cache: stat failed: ENOENT (No such file or directory)
05-25 09:15:38.042  1897  1897 E NRM-C-0 : service not connected. Domain = PS
05-25 09:15:38.042  1897  1897 E NRM-C-0 : service not connected. Domain = CS
05-25 09:15:38.042  1897  1897 E NRM-I-0 : service not connected. Domain = PS
05-25 09:15:38.048  1897  1897 E NRM-C-0 : service not connected. Domain = PS
05-25 09:15:38.048  1897  1897 E NRM-C-0 : service not connected. Domain = CS
05-25 09:15:38.048  1897  1897 E NRM-I-0 : service not connected. Domain = PS
05-25 09:15:38.060  2103  2103 I QuickstepProtoLogGroup: Initializing ProtoLog.
05-25 09:15:38.095  1897  1897 E RILJ    : getImei not supported on service MODEM < 2.1. [PHONE1]
05-25 09:15:38.097  1897  1897 E RILJ    : setNullCipherAndIntegrityEnabled not supported on service NETWORK < 2.1. [PHONE1]
05-25 09:15:38.098  1897  1897 E RILJ    : setCellularIdentifierTransparencyEnabled not supported on service NETWORK < 2.2. [PHONE1]
05-25 09:15:38.099  1897  1897 E RILJ    : setSecurityAlgorithmsUpdatedEnabled not supported on service NETWORK < 2.2. [PHONE1]
05-25 09:15:38.104  1897  1897 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-25 09:15:38.109  1712  2087 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-25 09:15:38.110  1897  1897 E NRM-C-1 : service not connected. Domain = PS
05-25 09:15:38.110  1897  1897 E NRM-C-1 : service not connected. Domain = CS
05-25 09:15:38.110  1897  1897 E NRM-I-1 : service not connected. Domain = PS
05-25 09:15:38.116  1897  1897 E NRM-C-1 : service not connected. Domain = PS
05-25 09:15:38.117  1897  1897 E NRM-C-1 : service not connected. Domain = CS
05-25 09:15:38.117  1897  1897 E NRM-I-1 : service not connected. Domain = PS
05-25 09:15:38.169  2103  2140 E FileLog : java.io.FileNotFoundException: /data/user/0/com.android.launcher3/files/log-1: open failed: ENOENT (No such file or directory)
05-25 09:15:38.169  2103  2140 E FileLog : 	at java.io.FileOutputStream.<init>(FileOutputStream.java:259)
05-25 09:15:38.169  2103  2140 E FileLog : 	at java.io.FileWriter.<init>(FileWriter.java:113)
05-25 09:15:38.169  2103  2140 E FileLog : Caused by: android.system.ErrnoException: open failed: ENOENT (No such file or directory)
05-25 09:15:38.213  1712  1712 E SharedConnectivityManager: To support shared connectivity service on this device, the service's package name and intent action strings must not be empty
05-25 09:15:38.227  1872  1872 W HidlServiceManagement: Waited one second for android.hardware.secure_element@1.2::ISecureElement/SIM1
05-25 09:15:38.227   573   573 I hwservicemanager: Since android.hardware.secure_element@1.2::ISecureElement/SIM1 is not registered, trying to start it as a lazy HAL (if it's not configured to be a lazy HAL, it may be stuck starting or still starting).
05-25 09:15:38.228  1872  1872 I HidlServiceManagement: getService: Trying again for android.hardware.secure_element@1.2::ISecureElement/SIM1...
05-25 09:15:38.234   573  2151 I hwservicemanager: Tried to start android.hardware.secure_element@1.2::ISecureElement/SIM1 as a lazy service, but was unable to. Usually this happens when a service is not installed, but if the service is intended to be used as a lazy service, then it may be configured incorrectly.
05-25 09:15:38.237  1897  1918 E PhoneInterfaceManager: getServiceStateForSlot retuning null for invalid slotIndex=-1
05-25 09:15:38.309  1321  1380 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.adservices/appid
05-25 09:15:38.312  1321  1380 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.permission/appid
05-25 09:15:38.322  1321  1380 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.btservices/appid
05-25 09:15:38.334  1321  1380 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.extservices/appid
05-25 09:15:38.338  1321  1380 W PackageSettings: Couldn't write -1 to /config/sdcardfs/com.android.nfcservices/appid
05-25 09:15:38.398  1897  1897 I Telephony: TelecomAccountRegistry$AccountEntry: registerPstnPhoneAccount: Registering account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with Telecom. subId=-1
05-25 09:15:38.401  1321  1956 I Telecom : TelecomServiceImpl$1: registerPhoneAccount: account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@AAo
05-25 09:15:38.402  1321  1956 I Telecom : PhoneAccountRegistrar: New phone account registered: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@AAo
05-25 09:15:38.406  1321  1956 I Telecom : CallsManager: Sending phone-account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} registered intent as user: TSI.rPA(cap)@AAo
05-25 09:15:38.410  1321  1956 I Telecom : PhoneAccountRegistrar: Notifying telephony of voice service override change for 1 SIMs, hasService = false: TSI.rPA(cap)@AAo
05-25 09:15:38.410  1321  1956 I Telecom : PhoneAccountRegistrar: maybeNotifyTelephonyForVoiceServiceState: simTm is null.: TSI.rPA(cap)@AAo
05-25 09:15:38.411  1897  1897 I Telephony: TelecomAccountRegistry$AccountEntry: Registered phoneAccount: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with handle: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}
05-25 09:15:38.412  1897  1897 I Telephony: TelecomAccountRegistry: Unregistering phone account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 2, UserHandle{0}.
05-25 09:15:38.416  1321  1898 I Telecom : CallsManager: Sending phone-account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 2, UserHandle{0} unregistered intent as user: TSI.uPA(cap)@AAw
05-25 09:15:38.418  1897  1897 I Telephony: TelecomAccountRegistry: Unregistering phone account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 1, UserHandle{0}.
05-25 09:15:38.420  1321  1898 I Telecom : CallsManager: Sending phone-account ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, 1, UserHandle{0} unregistered intent as user: TSI.uPA(cap)@AA0
05-25 09:15:38.426  1321  1321 I AS.AudioService: onSubscriptionsChanged()
05-25 09:15:38.430  1321  1321 I AS.AudioService: onSubscriptionsChanged()
05-25 09:15:38.433  1321  1321 I AS.AudioService: onSubscriptionsChanged()
05-25 09:15:38.434  1321  1321 I AS.AudioService: onSubscriptionsChanged()
05-25 09:15:38.493  1321  1337 I MR2ServiceImpl: registerManager | callerUid: 10226, callerPid: 1712, callerPackage: com.android.systemui, targetPackageName: null, targetUserId: UserHandle{0}, hasMediaRoutingControl: false
05-25 09:15:38.493  1897  1897 I Telephony: TelecomAccountRegistry$AccountEntry: registerPstnPhoneAccount: Registering account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with Telecom. subId=-1
05-25 09:15:38.495  1321  1956 I Telecom : TelecomServiceImpl$1: registerPhoneAccount: account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@AA4
05-25 09:15:38.496  1321  1668 I MR2ServiceImpl: addProviderRoutes | provider: com.android.server.media/.SystemMediaRoute2Provider, routes: [ROUTE_ID_BUILTIN_SPEAKER | Phone]
05-25 09:15:38.498  1321  1956 I Telecom : PhoneAccountRegistrar: Modify account: [ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}]: TSI.rPA(cap)@AA4
05-25 09:15:38.501  1321  1956 I Telecom : CallsManager: handlePhoneAccountChanged: phoneAccount=[[X] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@AA4
05-25 09:15:38.507  1321  1956 I Telecom : PhoneAccountRegistrar: Notifying telephony of voice service override change for 1 SIMs, hasService = false: TSI.rPA(cap)@AA4
05-25 09:15:38.507  1321  1956 I Telecom : PhoneAccountRegistrar: maybeNotifyTelephonyForVoiceServiceState: simTm is null.: TSI.rPA(cap)@AA4
05-25 09:15:38.508  1897  1897 I Telephony: TelecomAccountRegistry$AccountEntry: Registered phoneAccount: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with handle: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}
05-25 09:15:38.510  1321  1668 I AS.AudioService: removePreferredDevicesForStrategy strat:5
05-25 09:15:38.529  1897  1897 E SST     : [0] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-25 09:15:38.530  1897  1897 E SST     : [0] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-25 09:15:38.530  1897  1897 E SST     : [0] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-25 09:15:38.532  1897  1897 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-25 09:15:38.534  1897  1897 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-25 09:15:38.586  1897  1897 E SST     : [1] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-25 09:15:38.586  1897  1897 E SST     : [1] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-25 09:15:38.586  1897  1897 E SST     : [1] handlePollStateResult: RIL returned an error where it must succeed: java.lang.IllegalStateException: Service not connected.
05-25 09:15:38.588  1897  1897 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-25 09:15:38.590  1897  1897 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-25 09:15:38.671  1712  2163 I Codec2Client: Available Codec2 services: "default" "software"
05-25 09:15:38.713  1321  1898 W Binder  : java.lang.SecurityException: Need REGISTER_STATS_PULL_ATOM permission.: Neither user 10226 nor current process has android.permission.REGISTER_STATS_PULL_ATOM.
05-25 09:15:38.713  1321  1898 W Binder  : 	at android.app.ContextImpl.enforceCallingOrSelfPermission(ContextImpl.java:2630)
05-25 09:15:38.713  1321  1898 W Binder  : 	at com.android.server.stats.StatsManagerService.enforceRegisterStatsPullAtomPermission(StatsManagerService.java:678)
05-25 09:15:38.713  1321  1898 W Binder  : 	at com.android.server.stats.StatsManagerService.registerPullAtomCallback(StatsManagerService.java:219)
05-25 09:15:38.713  1321  1898 W Binder  : 	at android.os.IStatsManagerService$Stub.onTransact(IStatsManagerService.java:434)
05-25 09:15:38.716  1321  1337 I StatusBarManagerService: registerStatusBar bar=com.android.internal.statusbar.IStatusBar$Stub$Proxy@9be8ddd
05-25 09:15:38.722  1897  1897 E NRM-C-0 : service not connected. Domain = PS
05-25 09:15:38.722  1897  1897 E NRM-C-0 : service not connected. Domain = CS
05-25 09:15:38.722  1897  1897 E NRM-I-0 : service not connected. Domain = PS
05-25 09:15:38.725  1712  1712 I KeyguardSecurityView: Switching mode from Uninitialized to Default
05-25 09:15:38.739  1712  1712 E OverviewProxyService: Failed to get overview proxy for disable flags.
05-25 09:15:38.755  1712  1712 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-25 09:15:38.757   573   573 I hwservicemanager: Notifying android.hardware.wifi@1.5::IWifi/default they have clients: 1
05-25 09:15:38.764  1712  1712 E OverviewProxyService: Failed to get overview proxy for disable flags.
05-25 09:15:38.783  1712  1712 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-25 09:15:38.799  1712  1712 E ViewRootImpl: SensitiveContentProtectionService shouldn't be null
05-25 09:15:38.802  1897  1897 I Telephony: TelecomAccountRegistry$AccountEntry: registerPstnPhoneAccount: Registering account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with Telecom. subId=-1
05-25 09:15:38.803  1321  1956 I Telecom : TelecomServiceImpl$1: registerPhoneAccount: account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABA
05-25 09:15:38.804  1321  1956 I Telecom : PhoneAccountRegistrar: Modify account: [ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}]: TSI.rPA(cap)@ABA
05-25 09:15:38.804  1321  1956 I Telecom : CallsManager: handlePhoneAccountChanged: phoneAccount=[[X] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABA
05-25 09:15:38.806  1321  1956 I Telecom : PhoneAccountRegistrar: Notifying telephony of voice service override change for 1 SIMs, hasService = false: TSI.rPA(cap)@ABA
05-25 09:15:38.806  1321  1956 I Telecom : PhoneAccountRegistrar: maybeNotifyTelephonyForVoiceServiceState: simTm is null.: TSI.rPA(cap)@ABA
05-25 09:15:38.806  1897  1897 I Telephony: TelecomAccountRegistry$AccountEntry: Registered phoneAccount: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with handle: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}
05-25 09:15:38.826  1321  1339 W UserManagerService: Requested status bar icon for non-badged user 0
05-25 09:15:38.862  1897  1897 I Telephony: TelecomAccountRegistry$AccountEntry: registerPstnPhoneAccount: Registering account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with Telecom. subId=-1
05-25 09:15:38.863  1321  1339 I Telecom : TelecomServiceImpl$1: registerPhoneAccount: account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABM
05-25 09:15:38.863  1321  1339 I Telecom : PhoneAccountRegistrar: Modify account: [ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}]: TSI.rPA(cap)@ABM
05-25 09:15:38.864  1321  1339 I Telecom : CallsManager: handlePhoneAccountChanged: phoneAccount=[[X] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABM
05-25 09:15:38.866  1712  1712 I SystemUIService: Topological CoreStartables completed in 2 iterations
05-25 09:15:38.867  1321  1339 I Telecom : PhoneAccountRegistrar: Notifying telephony of voice service override change for 1 SIMs, hasService = false: TSI.rPA(cap)@ABM
05-25 09:15:38.867  1321  1339 I Telecom : PhoneAccountRegistrar: maybeNotifyTelephonyForVoiceServiceState: simTm is null.: TSI.rPA(cap)@ABM
05-25 09:15:38.867  1897  1897 I Telephony: TelecomAccountRegistry$AccountEntry: Registered phoneAccount: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with handle: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}
05-25 09:15:38.867  1712  1712 E OverviewProxyService: Failed to get overview proxy for disable flags.
05-25 09:15:38.877  1321  1339 W UserManagerService: Requested status bar icon for non-badged user 0
05-25 09:15:38.878  1712  1712 W SimLog  : invalid subId in handleServiceStateChange()
05-25 09:15:38.915  1210  1210 E bootanimation: === MALI DEBUG ===eglp_check_display_valid_and_initialized_and_retain retun EGL_NOT_INITIALIZED
05-25 09:15:38.928  1897  1897 I Telephony: TelecomAccountRegistry$AccountEntry: registerPstnPhoneAccount: Registering account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with Telecom. subId=-1
05-25 09:15:38.929  1321  1956 I Telecom : TelecomServiceImpl$1: registerPhoneAccount: account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABU
05-25 09:15:38.930  1321  1956 I Telecom : PhoneAccountRegistrar: Modify account: [ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}]: TSI.rPA(cap)@ABU
05-25 09:15:38.932  1321  1956 I Telecom : CallsManager: handlePhoneAccountChanged: phoneAccount=[[X] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABU
05-25 09:15:38.934  1321  1956 I Telecom : PhoneAccountRegistrar: Notifying telephony of voice service override change for 1 SIMs, hasService = false: TSI.rPA(cap)@ABU
05-25 09:15:38.934  1321  1956 I Telecom : PhoneAccountRegistrar: maybeNotifyTelephonyForVoiceServiceState: simTm is null.: TSI.rPA(cap)@ABU
05-25 09:15:38.935  1897  1897 I Telephony: TelecomAccountRegistry$AccountEntry: Registered phoneAccount: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with handle: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}
05-25 09:15:38.999  1897  1897 I Telephony: TelecomAccountRegistry$AccountEntry: registerPstnPhoneAccount: Registering account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with Telecom. subId=-1
05-25 09:15:39.001  1321  2113 I Telecom : TelecomServiceImpl$1: registerPhoneAccount: account=[[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABc
05-25 09:15:39.002  1321  2113 I Telecom : PhoneAccountRegistrar: Modify account: [ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}]: TSI.rPA(cap)@ABc
05-25 09:15:39.003  1321  2113 I Telecom : CallsManager: handlePhoneAccountChanged: phoneAccount=[[X] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]]: TSI.rPA(cap)@ABc
05-25 09:15:39.007  1321  2113 I Telecom : PhoneAccountRegistrar: Notifying telephony of voice service override change for 1 SIMs, hasService = false: TSI.rPA(cap)@ABc
05-25 09:15:39.007  1321  2113 I Telecom : PhoneAccountRegistrar: maybeNotifyTelephonyForVoiceServiceState: simTm is null.: TSI.rPA(cap)@ABc
05-25 09:15:39.008  1897  1897 I Telephony: TelecomAccountRegistry$AccountEntry: Registered phoneAccount: [[ ] PhoneAccount: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0} Capabilities: SuppVideo CallProvider MultiUser PlaceEmerg SimSub  Audio Routes: BESW Schemes: tel voicemail  Extras: Bundle[{android.telecom.extra.SUPPORTS_VIDEO_CALLING_FALLBACK=true}] GroupId: *** SC Restrictions: [ ]] with handle: ComponentInfo{com.android.phone/com.android.services.telephony.TelephonyConnectionService}, E, UserHandle{0}
05-25 09:15:39.012  1897  1897 E NRM-C-1 : service not connected. Domain = PS
05-25 09:15:39.012  1897  1897 E NRM-C-1 : service not connected. Domain = CS
05-25 09:15:39.012  1897  1897 E NRM-I-1 : service not connected. Domain = PS
05-25 09:15:39.020  1897  2085 E SatelliteModemInterface: requestIsSatelliteSupported: Satellite service is unavailable.
05-25 09:15:39.020  1897  2085 E SatelliteServiceUtils: isSatelliteSupported SatelliteException: android.telephony.satellite.SatelliteManager$SatelliteException
05-25 09:15:39.028  1321  1956 I Telecom : TelecomServiceImpl$1: isInEmergencyCall: false: TSI.iIEC@ABk
05-25 09:15:39.029  1897  2085 E SatelliteModemInterface: requestIsSatelliteSupported: Satellite service is unavailable.
05-25 09:15:39.029  1897  2085 E SatelliteServiceUtils: isSatelliteSupported SatelliteException: android.telephony.satellite.SatelliteManager$SatelliteException
05-25 09:15:39.031  1321  2113 I Telecom : TelecomServiceImpl$1: isInEmergencyCall: false: TSI.iIEC@ABo
05-25 09:15:39.054  1897  1897 E NRM-I-0 : service not connected. Domain = PS
05-25 09:15:39.057  1897  1897 E SatelliteController: registerForSatelliteModemStateChanged: mSatelliteSessionController is not initialized yet
05-25 09:15:39.078  1897  1897 E NRM-I-0 : service not connected. Domain = PS
05-25 09:15:39.089  1897  1897 E RILJ    : getUsageSetting not supported on service NETWORK < 2.0. [PHONE0]
05-25 09:15:39.089  1897  1897 E RILJ    : getUsageSetting not supported on service NETWORK < 2.0. [PHONE1]
05-25 09:15:39.105  1321  1348 I SystemServiceManager: Starting phase 1000
05-25 09:15:39.105  1321  1348 E PowerStatsService: Failed to start PowerStatsService loggers
05-25 09:15:39.110   892   892 I PowerHalLoader: Successfully connected to Power HAL AIDL service.
05-25 09:15:39.122  1321  1348 I TransparencyService: Boot completed. Getting boot integrity data.
05-25 09:15:39.122  1321  1348 I TransparencyService: Boot completed. Collecting biometric system properties.
05-25 09:15:39.126  1321  1348 I TransparencyService: Scheduling measurements to be taken.
05-25 09:15:39.127  1321  1348 I TransparencyService: Scheduling binary content-digest computation job
05-25 09:15:39.139  1321  1610 I StorageSessionController: Started resetting external storage service...
05-25 09:15:39.139  1321  1610 I StorageSessionController: Finished resetting external storage service
05-25 09:15:39.140  1321  1610 I StorageManagerService: Resetting vold...
05-25 09:15:39.140  1321  1610 I StorageManagerService: Reset vold
