<manifest version="4.0" type="device" target-level="5">
    <hal format="hidl">
        <name>android.hardware.audio</name>
        <transport>hwbinder</transport>
        <version>7.0</version>
        <interface>
            <name>IDevicesFactory</name>
            <instance>default</instance>
        </interface>
        <fqname>@7.0::IDevicesFactory/default</fqname>
    </hal>
    <hal format="hidl">
        <name>android.hardware.audio.effect</name>
        <transport>hwbinder</transport>
        <version>7.0</version>
        <interface>
            <name>IEffectsFactory</name>
            <instance>default</instance>
        </interface>
        <fqname>@7.0::IEffectsFactory/default</fqname>
    </hal>
    <hal format="hidl">
        <name>android.hardware.bluetooth</name>
        <transport>hwbinder</transport>
        <version>1.1</version>
        <interface>
            <name>IBluetoothHci</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.1::IBluetoothHci/default</fqname>
    </hal>
    <hal format="hidl">
        <name>android.hardware.camera.provider</name>
        <transport>hwbinder</transport>
        <version>2.4</version>
        <version>2.5</version>
        <version>2.6</version>
        <interface>
            <name>ICameraProvider</name>
            <instance>internal/0</instance>
        </interface>
        <fqname>@2.4::ICameraProvider/internal/0</fqname>
        <fqname>@2.5::ICameraProvider/internal/0</fqname>
        <fqname>@2.6::ICameraProvider/internal/0</fqname>
    </hal>
    <hal format="hidl">
        <name>android.hardware.gatekeeper</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IGatekeeper</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.0::IGatekeeper/default</fqname>
    </hal>
    <hal format="hidl">
        <name>android.hardware.graphics.allocator</name>
        <transport>hwbinder</transport>
        <version>4.0</version>
        <interface>
            <name>IAllocator</name>
            <instance>default</instance>
        </interface>
        <fqname>@4.0::IAllocator/default</fqname>
    </hal>
    <hal format="hidl">
        <name>android.hardware.graphics.composer</name>
        <transport>hwbinder</transport>
        <version>2.1</version>
        <interface>
            <name>IComposer</name>
            <instance>default</instance>
        </interface>
        <fqname>@2.1::IComposer/default</fqname>
    </hal>
    <hal format="hidl">
        <name>android.hardware.graphics.mapper</name>
        <transport arch="32+64">passthrough</transport>
        <version>4.0</version>
        <interface>
            <name>IMapper</name>
            <instance>default</instance>
        </interface>
        <fqname>@4.0::IMapper/default</fqname>
    </hal>
    <hal format="hidl">
        <name>android.hardware.keymaster</name>
        <transport>hwbinder</transport>
        <version>4.1</version>
        <interface>
            <name>IKeymasterDevice</name>
            <instance>default</instance>
        </interface>
        <fqname>@4.1::IKeymasterDevice/default</fqname>
    </hal>
    <hal format="hidl">
        <name>android.hardware.media.omx</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IOmx</name>
            <instance>default</instance>
        </interface>
        <interface>
            <name>IOmxStore</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.0::IOmx/default</fqname>
        <fqname>@1.0::IOmxStore/default</fqname>
    </hal>
    <hal format="hidl">
        <name>android.hardware.radio</name>
        <transport>hwbinder</transport>
        <version>1.2</version>
        <interface>
            <name>IRadio</name>
            <instance>em1</instance>
            <instance>em2</instance>
            <instance>imsAospSlot1</instance>
            <instance>imsAospSlot2</instance>
            <instance>se1</instance>
            <instance>se2</instance>
            <instance>slot1</instance>
            <instance>slot2</instance>
        </interface>
        <interface>
            <name>ISap</name>
            <instance>slot1</instance>
            <instance>slot2</instance>
        </interface>
        <fqname>@1.2::IRadio/em1</fqname>
        <fqname>@1.2::IRadio/em2</fqname>
        <fqname>@1.2::IRadio/imsAospSlot1</fqname>
        <fqname>@1.2::IRadio/imsAospSlot2</fqname>
        <fqname>@1.2::IRadio/se1</fqname>
        <fqname>@1.2::IRadio/se2</fqname>
        <fqname>@1.2::IRadio/slot1</fqname>
        <fqname>@1.2::IRadio/slot2</fqname>
        <fqname>@1.2::ISap/slot1</fqname>
        <fqname>@1.2::ISap/slot2</fqname>
        <fqname>@1.6::IRadio/em1</fqname>
        <fqname>@1.6::IRadio/em2</fqname>
        <fqname>@1.6::IRadio/imsAospSlot1</fqname>
        <fqname>@1.6::IRadio/imsAospSlot2</fqname>
        <fqname>@1.6::IRadio/se1</fqname>
        <fqname>@1.6::IRadio/se2</fqname>
        <fqname>@1.6::IRadio/slot1</fqname>
        <fqname>@1.6::IRadio/slot2</fqname>
    </hal>
    <hal format="hidl">
        <name>android.hardware.radio.config</name>
        <transport>hwbinder</transport>
        <version>1.3</version>
        <interface>
            <name>IRadioConfig</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.3::IRadioConfig/default</fqname>
    </hal>
    <hal format="hidl">
        <name>android.hardware.soundtrigger</name>
        <transport>hwbinder</transport>
        <fqname>@2.0::ISoundTriggerHw/default</fqname>
        <fqname>@2.3::ISoundTriggerHw/default</fqname>
    </hal>
    <hal format="hidl">
        <name>android.hardware.tetheroffload.config</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IOffloadConfig</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.0::IOffloadConfig/default</fqname>
    </hal>
    <hal format="hidl">
        <name>android.hardware.tetheroffload.control</name>
        <transport>hwbinder</transport>
        <version>1.1</version>
        <interface>
            <name>IOffloadControl</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.1::IOffloadControl/default</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.apmonitor</name>
        <transport>hwbinder</transport>
        <version>2.0</version>
        <interface>
            <name>IApmService</name>
            <instance>apm_hidl_service</instance>
        </interface>
        <fqname>@2.0::IApmService/apm_hidl_service</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.apuware.apusys</name>
        <transport>hwbinder</transport>
        <version>2.1</version>
        <interface>
            <name>INeuronApusys</name>
            <instance>default</instance>
        </interface>
        <fqname>@2.1::INeuronApusys/default</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.apuware.hmp</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IApuwareHmp</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.0::IApuwareHmp/default</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.apuware.utils</name>
        <transport>hwbinder</transport>
        <version>2.0</version>
        <interface>
            <name>IApuwareUtils</name>
            <instance>default</instance>
        </interface>
        <fqname>@2.0::IApuwareUtils/default</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.apuware.xrp</name>
        <transport>hwbinder</transport>
        <version>2.0</version>
        <interface>
            <name>INeuronXrp</name>
            <instance>default</instance>
        </interface>
        <fqname>@2.0::INeuronXrp/default</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.atci</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IAtcid</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.0::IAtcid/default</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.camera.bgservice</name>
        <transport>hwbinder</transport>
        <version>1.1</version>
        <interface>
            <name>IBGService</name>
            <instance>internal/0</instance>
        </interface>
        <fqname>@1.1::IBGService/internal/0</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.camera.isphal</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IISPModule</name>
            <instance>internal/0</instance>
        </interface>
        <fqname>@1.0::IISPModule/internal/0</fqname>
        <fqname>@1.1::IISPModule/internal/0</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.camera.atms</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IATMs</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.0::IATMs/default</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.camera.postproc</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IPostDevice</name>
            <instance>internal/0</instance>
        </interface>
        <fqname>@1.0::IPostDevice/internal/0</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.composer_ext</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IComposerExt</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.0::IComposerExt/default</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.dmc</name>
        <transport>hwbinder</transport>
        <version>1.2</version>
        <interface>
            <name>IDmcService</name>
            <instance>dmc_hidl_service</instance>
        </interface>
        <fqname>@1.2::IDmcService/dmc_hidl_service</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.engineermode</name>
        <transport>hwbinder</transport>
        <version>1.3</version>
        <interface>
            <name>IEmd</name>
            <instance>EmHidlServer</instance>
        </interface>
        <fqname>@1.3::IEmd/EmHidlServer</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.keymaster_attestation</name>
        <transport>hwbinder</transport>
        <version>1.1</version>
        <interface>
            <name>IKeymasterDevice</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.1::IKeymasterDevice/default</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.log</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>ILog</name>
            <instance>ATMWiFiHidlServer</instance>
            <instance>ConnsysFWHidlServer</instance>
            <instance>LoggerHidlServer</instance>
            <instance>MobileLogHidlServer</instance>
            <instance>ModemLogHidlServer</instance>
        </interface>
        <fqname>@1.0::ILog/ATMWiFiHidlServer</fqname>
        <fqname>@1.0::ILog/ConnsysFWHidlServer</fqname>
        <fqname>@1.0::ILog/LoggerHidlServer</fqname>
        <fqname>@1.0::ILog/MobileLogHidlServer</fqname>
        <fqname>@1.0::ILog/ModemLogHidlServer</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.mdmonitor</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IMDMonitorService</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.0::IMDMonitorService/default</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.mmagent</name>
        <transport>hwbinder</transport>
        <version>1.1</version>
        <interface>
            <name>IMMAgent</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.1::IMMAgent/default</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.mms</name>
        <transport>hwbinder</transport>
        <version>1.6</version>
        <interface>
            <name>IMms</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.6::IMms/default</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.mtkpower</name>
        <transport>hwbinder</transport>
        <version>1.2</version>
        <interface>
            <name>IMtkPerf</name>
            <instance>default</instance>
        </interface>
        <interface>
            <name>IMtkPower</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.2::IMtkPerf/default</fqname>
        <fqname>@1.2::IMtkPower/default</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.netdagent</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>INetdagent</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.0::INetdagent/default</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.nvram</name>
        <transport>hwbinder</transport>
        <version>1.1</version>
        <interface>
            <name>INvram</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.1::INvram/default</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.pq</name>
        <transport>hwbinder</transport>
        <version>2.15</version>
        <interface>
            <name>IPictureQuality</name>
            <instance>default</instance>
        </interface>
        <fqname>@2.15::IPictureQuality/default</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.mediatek.hardware.videotelephony</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IVideoTelephony</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.0::IVideoTelephony/default</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.trustonic.tee</name>
        <transport>hwbinder</transport>
        <version>1.1</version>
        <interface>
            <name>ITee</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.1::ITee/default</fqname>
    </hal>
    <hal format="hidl">
        <name>vendor.trustonic.tee.tui</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>ITui</name>
            <instance>default</instance>
        </interface>
        <fqname>@1.0::ITui/default</fqname>
    </hal>
    <hal format="hidl">
        <name>android.hardware.secure_element</name>
        <transport>hwbinder</transport>
        <fqname>@1.2::ISecureElement/SIM1</fqname>
        <fqname>@1.2::ISecureElement/SIM2</fqname>
    </hal>
   <hal override="true" format="hidl">
        <name>android.hardware.radio</name>
        <transport>hwbinder</transport>
        <version>1.2</version>
        <interface>
            <name>IRadio</name>
            <instance>slot1</instance>
            <instance>slot2</instance>
            <instance>imsAospSlot1</instance>
            <instance>imsAospSlot2</instance>
            <instance>se1</instance>
            <instance>se2</instance>
            <instance>em1</instance>
            <instance>em2</instance>
        </interface>
        <interface>
            <name>ISap</name>
            <instance>slot1</instance>
            <instance>slot2</instance>
        </interface>
        <fqname>@1.6::IRadio/slot1</fqname>
        <fqname>@1.6::IRadio/slot2</fqname>
        <fqname>@1.6::IRadio/imsAospSlot1</fqname>
        <fqname>@1.6::IRadio/imsAospSlot2</fqname>
        <fqname>@1.6::IRadio/se1</fqname>
        <fqname>@1.6::IRadio/se2</fqname>
        <fqname>@1.6::IRadio/em1</fqname>
        <fqname>@1.6::IRadio/em2</fqname>
    </hal>
   <hal override="true" format="hidl">
        <name>vendor.mediatek.hardware.mtkradioex</name>
        <transport>hwbinder</transport>
        <version>3.0</version>
        <interface>
            <name>IMtkRadioEx</name>
            <instance>mtkSlot1</instance>
            <instance>mtkSlot2</instance>
            <instance>imsSlot1</instance>
            <instance>imsSlot2</instance>
            <instance>mtkSe1</instance>
            <instance>mtkSe2</instance>
            <instance>mtkEm1</instance>
            <instance>mtkEm2</instance>
            <instance>mtkAssist1</instance>
            <instance>mtkAssist2</instance>
            <instance>mtkRcs1</instance>
            <instance>mtkRcs2</instance>
            <instance>mtkCap1</instance>
            <instance>mtkCap2</instance>
            <instance>mtkSmartRatSwitch1</instance>
            <instance>mtkSmartRatSwitch2</instance>
            <instance>mtkRsu1</instance>
            <instance>mtkRsu2</instance>
        </interface>
    </hal>
   <hal override="true" format="hidl">
        <name>android.hardware.secure_element</name>
        <transport>hwbinder</transport>
        <fqname>@1.2::ISecureElement/SIM1</fqname>
        <fqname>@1.2::ISecureElement/SIM2</fqname>
    </hal>
    <!-- #<EMAIL>.2054403, 2019/05/25, add for Euclid -->
    <hal override="true" format="hidl">
        <name>vendor.oplus.hardware.appradio</name>
        <transport>hwbinder</transport>
        <version>1.0</version>
        <interface>
            <name>IOplusAppRadio</name>
            <instance>oplus_app_slot1</instance>
            <instance>oplus_app_slot2</instance>
        </interface>
    </hal>

    <!-- #<EMAIL>.2054403, 2019/05/25, add for Euclid -->
    <hal format="aidl">
        <name>vendor.oplus.hardware.radio</name>
        <version>1</version>
        <fqname>IRadioStable/OplusRadio0</fqname>
        <fqname>IRadioStable/OplusRadio1</fqname>
    </hal>
    <hal format="hidl">
        <name>android.hardware.nfc</name>
        <transport>hwbinder</transport>
        <version>1.2</version>
        <interface>
            <name>INfc</name>
            <instance>default</instance>
        </interface>
    </hal>
</manifest>
