<?xml version="1.0" encoding="utf-8"?>
<permissions>
    <privapp-permissions package="com.mediatek.ims">
        <permission name="android.permission.READ_PRECISE_PHONE_STATE"/>
        <permission name="android.permission.READ_NETWORK_USAGE_HISTORY"/>
        <permission name="android.permission.PACKET_KEEPALIVE_OFFLOAD"/>
        <permission name="android.permission.CHANGE_WIFI_STATE"/>
        <permission name="android.permission.NETWORK_SIGNAL_STRENGTH_WAKEUP"/>
        <permission name="android.permission.MODIFY_PHONE_STATE"/>
        <permission name="android.permission.UPDATE_DEVICE_STATS"/>
        <permission name="android.permission.LOCATION_BYPASS"/>
        <permission name="android.permission.READ_PRIVILEGED_PHONE_STATE"/>
    </privapp-permissions>

    <privapp-permissions package="com.mediatek.engineermode">
        <permission name="android.permission.WRITE_SECURE_SETTINGS"/>
        <permission name="android.permission.SET_DEBUG_APP"/>
        <permission name="android.permission.WRITE_MEDIA_STORAGE"/>
        <permission name="android.permission.READ_PRIVILEGED_PHONE_STATE"/>
        <permission name="android.permission.MASTER_CLEAR"/>
        <permission name="android.permission.REAL_GET_TASKS"/>
        <permission name="android.permission.READ_LOGS"/>
        <permission name="android.permission.READ_PRECISE_PHONE_STATE"/>
        <permission name="android.permission.BLUETOOTH_PRIVILEGED"/>
        <permission name="android.permission.LOCATION_HARDWARE"/>

        <!-- Add reboot permission for TelephonyManager.switchMultiSimConfig -->
        <permission name="android.permission.MODIFY_PHONE_STATE"/>
        <permission name="android.permission.MANAGE_USB"/>
        <!-- Add for network slice test -->
        <permission name="android.permission.CONNECTIVITY_INTERNAL"/>
    </privapp-permissions>

</permissions>
