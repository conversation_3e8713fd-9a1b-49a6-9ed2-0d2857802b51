<compatibility-matrix version="4.0" type="device">
    <hal format="hidl" optional="false">
        <name>android.frameworks.schedulerservice</name>
        <version>1.0</version>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.frameworks.sensorservice</name>
        <version>1.0</version>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.hidl.allocator</name>
        <version>1.0</version>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.hidl.manager</name>
        <version>1.0</version>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.hidl.memory</name>
        <version>1.0</version>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.hidl.token</name>
        <version>1.0</version>
    </hal>
    <hal format="hidl" optional="false">
        <name>android.system.wifi.keystore</name>
        <version>1.0</version>
    </hal>
    <hal format="hidl" optional="true">
        <name>vendor.oplus.hardware.osense.client</name>
        <version>1.0</version>
        <interface>
            <name>IOsenseHalReporter</name>
            <instance>OsenseHalReporter</instance>
        </interface>
    </hal>
</compatibility-matrix>
