# AAL
ro.oplus.bl.disable.dre.enable                                  u:object_r:vendor_mtk_default_prop:s0
ro.oplus.backlight.threhold                                     u:object_r:vendor_mtk_default_prop:s0

# Audio
persist.vendor.audio.tuning.def_path                            u:object_r:vendor_audio_tuning_prop:s0

# Fingerprint
persist.vendor.fingerprint.                                     u:object_r:vendor_fingerprint_prop:s0
persist.vendor.fp.                                              u:object_r:vendor_fingerprint_prop:s0
vendor.flash.locked                                             u:object_r:vendor_fingerprint_prop:s0
vendor.fingerprint.                                             u:object_r:vendor_fingerprint_prop:s0

# Camera
vendor.oplus.camera.                                            u:object_r:vendor_oplus_prop:s0
persist.vendor.camera.privapp.list                              u:object_r:vendor_oplus_prop:s0
vendor.debug.cameralog.enable                                   u:object_r:vendor_oplus_prop:s0
vendor.debug.tpi.                                               u:object_r:vendor_oplus_prop:s0

# MNLD
ro.oplus.mtk_gps_L1L5                                           u:object_r:vendor_mtk_mnld_prop:s0

# PQ
ro.vendor.mtk_hdr_video_support                                 u:object_r:vendor_mtk_pq_ro_prop:s0

# PowerHAL
vendor.powerhal.rendering                                       u:object_r:vendor_power_prop:s0
