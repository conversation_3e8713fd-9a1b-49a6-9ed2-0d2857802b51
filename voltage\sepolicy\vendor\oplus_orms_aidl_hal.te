type oplus_orms_aidl_service, domain;
type oplus_orms_aidl_service_exec, file_type, exec_type, vendor_file_type;
type oplus_orms_aidl_hal_service, service_manager_type ;
add_service(oplus_orms_aidl_service, oplus_orms_aidl_hal_service)
allow oplus_orms_aidl_service oplus_orms_aidl_service_exec:file rx_file_perms;
binder_call(oplus_orms_aidl_service, servicemanager)
allow oplus_orms_aidl_service sysfs_fpsgo:file rw_file_perms;
allow oplus_orms_aidl_service sysfs_fpsgo:dir r_dir_perms;
allow oplus_orms_aidl_service sysfs:file { read write open };
allow oplus_orms_aidl_service proc_mcdi:dir r_dir_perms;
allow oplus_orms_aidl_service proc_mcdi:file rw_file_perms;
allow oplus_orms_aidl_service proc_perfmgr:dir r_dir_perms;
allow oplus_orms_aidl_service proc_perfmgr:file rw_file_perms;
allow oplus_orms_aidl_service sysfs_devices_system_cpu:dir r_dir_perms;
allow oplus_orms_aidl_service sysfs_devices_system_cpu:file rw_file_perms;
allow oplus_orms_aidl_service sysfs_fbt_cpu:dir r_dir_perms;
allow oplus_orms_aidl_service sysfs_fbt_cpu:file rw_file_perms;
allow oplus_orms_aidl_service sysfs_xgf:dir r_dir_perms;
allow oplus_orms_aidl_service sysfs_xgf:file rw_file_perms;
allow oplus_orms_aidl_service proc_version:file { read getattr open };
allow oplus_orms_aidl_service sysfs_thermal:file rw_file_perms;
