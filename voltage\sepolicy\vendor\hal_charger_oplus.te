type hal_charger_oplus, domain;
type hal_charger_oplus_exec, exec_type, vendor_file_type, file_type;

add_hwservice(hal_charger_oplus, hal_charger_oplus_hwservice)

init_daemon_domain(hal_charger_oplus)

hwbinder_use(hal_charger_oplus)
allow hal_charger_oplus fwk_sensor_hwservice:hwservice_manager find;

allow hal_charger_oplus hal_charger_oplus:netlink_kobject_uevent_socket { read create bind getopt setopt };

allow hal_charger_oplus oplus_charger_device:chr_file rw_file_perms;
allow hal_charger_oplus tee_device:chr_file rw_file_perms;
allow hal_charger_oplus vendor_proc_batt_param:dir r_dir_perms;
allow hal_charger_oplus vendor_proc_batt_param:file r_file_perms;
allow hal_charger_oplus vendor_proc_batt_param:lnk_file r_file_perms;
allow hal_charger_oplus vendor_proc_charger:file rw_file_perms;
allow hal_charger_oplus vendor_proc_decimal:file rw_file_perms;
allow hal_charger_oplus vendor_proc_devinfo_fastchg:file rw_file_perms;
allow hal_charger_oplus vendor_proc_fw_update:file rw_file_perms;
allow hal_charger_oplus vendor_proc_tbatt_pwroff:file rw_file_perms;
allow hal_charger_oplus vendor_proc_wireless:file rw_file_perms;

allow hal_charger_oplus sysfs_thermal:file r_file_perms;
allow hal_charger_oplus sysfs_thermal:dir search;
allow hal_charger_oplus sysfs_therm:dir search;
allow hal_charger_oplus sysfs_therm:file r_file_perms;
allow hal_charger_oplus vendor_sysfs_usb_supply:dir search;
allow hal_charger_oplus vendor_sysfs_usb_supply:file rw_file_perms;
allow hal_charger_oplus vendor_sysfs_usb_supply:lnk_file rw_file_perms;
allow hal_charger_oplus vendor_sysfs_battery_supply:dir search;
allow hal_charger_oplus vendor_sysfs_battery_supply:file rw_file_perms;
allow hal_charger_oplus vendor_sysfs_battery_supply:lnk_file rw_file_perms;

allow hal_charger_oplus vendor_firmware_file:dir search;
allow hal_charger_oplus vendor_firmware_file:file r_file_perms;

r_dir_file(hal_charger_oplus, vendor_proc_oplus_version)
r_dir_file(hal_charger_oplus, sysfs_batteryinfo)

get_prop(hal_charger_oplus, hwservicemanager_prop)

allow hal_charger_oplus vendor_sysfs_ac_supply:dir r_dir_perms;
allow hal_charger_oplus vendor_sysfs_ac_supply:file rw_file_perms;
allow hal_charger_oplus oplus_block_device:dir search;
allow hal_charger_oplus oplus_block_device:file r_file_perms;
allow hal_charger_oplus oplus_block_device:blk_file r_file_perms;
allow hal_charger_oplus block_device:dir search;
allow hal_charger_oplus block_device:file rw_file_perms;
