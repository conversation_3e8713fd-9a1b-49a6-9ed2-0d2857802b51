on init
    setprop sys.usb.configfs 1
    setprop sys.usb.ffs.aio_compat 1

on fs
    wait /dev/block/platform/bootdevice/
    symlink /dev/block/platform/bootdevice/ /dev/block/bootdevice

    # OTG
    write /sys/class/power_supply/usb/otg_switch 1
    # distinguish USB shoulde connect or not, i.e. CDP vs SDP
    write /sys/class/udc/musb-hdrc/device/cmode 2
    # set charging free due to it wait for USB activation
    start adbd
