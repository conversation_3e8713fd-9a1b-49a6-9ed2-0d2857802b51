allow vendor_init vendor_proc_decimal:file w_file_perms;
allow vendor_init nfc_data_vendor_file:dir { r_dir_perms create_dir_perms };

allow vendor_init proc_sched_stune:file w_file_perms;
allow vendor_init proc_vm_dirty:file w_file_perms;

allow vendor_init vendor_sysfs_usb_supply:dir search;
allow vendor_init vendor_sysfs_usb_supply:file w_file_perms;

allow vendor_init vendor_sysfs_otg_switch:file w_file_perms;

allow vendor_init vendor_proc_display:file w_file_perms;

allow vendor_init vts_status_prop:file { read getattr open };
allow vendor_init system_prop:file { read getattr open };
allow vendor_init proc_swappiness:file rw_file_perms;

# Allow vendor_init to set LMK properties
allow vendor_init lmk_prop:property_service set;

# Allow vendor_init to set fingerprint properties
allow vendor_init system_fingerprint_prop:property_service set;

# Allow vendor_init to set exported system properties
allow vendor_init exported_system_prop:property_service set;

# Allow vendor_init to set MediaTek PQ properties
allow vendor_init vendor_mtk_pq_prop:property_service set;
allow vendor_init vendor_mtk_pq_ro_prop:property_service set;

# Allow vendor_init to set MediaTek C2 video decoder properties
allow vendor_init vendor_mtk_c2_log_prop:property_service set;
