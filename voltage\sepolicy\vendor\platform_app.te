r_dir_file(platform_app, vendor_sysfs_graphics)

allow platform_app hal_performance_oplus_hwservice:hwservice_manager find;
allow platform_app hal_osense_oplus_hwservice:hwservice_manager find;

binder_call(platform_app, mtk_hal_camera)
binder_call(platform_app, hal_performance_oplus)
binder_call(platform_app, mtk_hal_camera)
binder_call(platform_app, hal_performance_oplus)

r_dir_file(platform_app, persist_camera_file)
r_dir_file(platform_app, persist_data_file)
allow platform_app shell_data_file:file r_file_perms;
allow platform_app shell_data_file:dir r_dir_perms;

allow platform_app system_data_file:file r_file_perms;
allow platform_app apusys_device:chr_file { ioctl read write open };
get_prop(platform_app, vendor_oplus_prop)

# Allow platform_app to set system properties
allow platform_app system_prop:property_service set;
