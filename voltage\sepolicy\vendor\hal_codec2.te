
# Allow hal_codec2 to use vendor_mtk_c2_log_prop
get_prop(hal_codec2, vendor_mtk_c2_log_prop);
set_prop(hal_codec2, vendor_mtk_c2_log_prop);

# Allow hal_codec2 to access vendor properties
get_prop(hal_codec2, vendor_default_prop);

# Allow hal_codec2 to access video devices
allow hal_codec2 video_device_type:chr_file rw_file_perms;

# Allow hal_codec2 to access MediaTek video devices
allow hal_codec2 mtk_mdp_device:chr_file rw_file_perms;

# Allow hal_codec2 to access vendor files
allow hal_codec2 vendor_file:file { read getattr open execute };
allow hal_codec2 vendor_data_file:file { read write open };
