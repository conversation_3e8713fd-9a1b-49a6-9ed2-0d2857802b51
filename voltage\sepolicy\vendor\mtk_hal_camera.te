# HWService permissions
add_hwservice(mtk_hal_camera, hal_camera_oplus_hwservice)
allow mtk_hal_camera oplus_hal_ormsHal_hwservice:hwservice_manager find;
allow mtk_hal_camera hal_performance_oplus_hwservice:hwservice_manager find;
allow mtk_hal_camera hal_osense_oplus_hwservice:hwservice_manager find;

# Property access
set_prop(mtk_hal_camera, vendor_oplus_prop)
get_prop(mtk_hal_camera, system_oplus_camera_prop)

# Allow mtk_hal_camera to read default_prop and vendor_dp_prop
allow mtk_hal_camera default_prop:file r_file_perms;
get_prop(mtk_hal_camera, default_prop)

allow mtk_hal_camera vendor_dp_prop:file r_file_perms;
get_prop(mtk_hal_camera, vendor_dp_prop)

# File access permissions
r_dir_file(mtk_hal_camera, proc_boost_pool)
r_dir_file(mtk_hal_camera, proc_sched_assist)
r_dir_file(mtk_hal_camera, proc_version)
r_dir_file(mtk_hal_camera, vendor_proc_oplus_version)

# Persistent storage access
allow mtk_hal_camera persist_camera_file:dir rw_dir_perms;
allow mtk_hal_camera persist_camera_file:file create_file_perms;
allow mtk_hal_camera vendor_camera_update_data_file:dir rw_dir_perms;
allow mtk_hal_camera vendor_camera_update_data_file:file create_file_perms;
allow mtk_hal_camera persist_data_file:dir r_dir_perms;
allow mtk_hal_camera proc_sched_assist:file rw_file_perms;
allow mtk_hal_camera proc_boost_pool:file rw_file_perms;

# Binder permissions
binder_call(mtk_hal_camera, mtk_hal_mmagent)
binder_call(mtk_hal_camera, opluscamera_app)
allow mtk_hal_camera opluscamera_app:fd use;
hal_client_domain(mtk_hal_camera, hal_mtk_mmagent)