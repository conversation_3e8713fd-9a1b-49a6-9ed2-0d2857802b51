typeattribute mtk_hal_sensors socket_between_core_and_vendor_violators;

r_dir_file(mtk_hal_sensors, vendor_proc_oplus_sensor_feature)
r_dir_file(mtk_hal_sensors, vendor_sysfs_oplus_virtual_sensor)

allow mtk_hal_sensors vendor_sysfs_oplus_virtual_sensor:file w_file_perms;

allow mtk_hal_sensors vendor_proc_oplus_sensor_param:file r_file_perms;
allow mtk_hal_sensors virtual_sensor_device:chr_file r_file_perms;

allow mtk_hal_sensors als_ps_device:chr_file r_file_perms;
allow mtk_hal_sensors vendor_proc_oplus_sensor_cali:dir search;
allow mtk_hal_sensors vendor_proc_oplus_sensor_cali:file rw_file_perms;

allow mtk_hal_sensors oplus_block_device:blk_file r_file_perms;
allow mtk_hal_sensors block_device:dir search;
allow mtk_hal_sensors persist_data_file:dir search;

r_dir_file(mtk_hal_sensors, sysfs_leds)
r_dir_file(mtk_hal_sensors, vendor_sysfs_graphics)
allow mtk_hal_sensors oplus_sensor_file:dir r_dir_perms;
allow mtk_hal_sensors oplus_sensor_file:file r_file_perms;
r_dir_file(mtk_hal_sensors, persist_engineer_file)

