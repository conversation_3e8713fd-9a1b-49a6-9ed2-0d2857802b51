﻿<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright (C) 2014 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<MediaCodecs>
    <Settings>
        <!-- disable TV and telephony domains by default. These must be opted in by OEMs -->
        <Domain name="telephony" enabled="false" />
        <Domain name="tv" enabled="false" />
    </Settings>
    <Decoders>
        <MediaCodec name="c2.android.mp3.decoder" type="audio/mpeg">
            <Alias name="OMX.google.mp3.decoder" />
            <Limit name="channel-count" max="2" />
            <Limit name="sample-rate" ranges="8000,11025,12000,16000,22050,24000,32000,44100,48000" />
            <Limit name="bitrate" range="8000-320000" />
        </MediaCodec>
        <MediaCodec name="c2.android.amrnb.decoder" type="audio/3gpp">
            <Alias name="OMX.google.amrnb.decoder" />
            <Limit name="channel-count" max="1" />
            <Limit name="sample-rate" ranges="8000" />
            <Limit name="bitrate" range="4750-12200" />
        </MediaCodec>
        <MediaCodec name="c2.android.amrwb.decoder" type="audio/amr-wb">
            <Alias name="OMX.google.amrwb.decoder" />
            <Limit name="channel-count" max="1" />
            <Limit name="sample-rate" ranges="16000" />
            <Limit name="bitrate" range="6600-23850" />
        </MediaCodec>
        <MediaCodec name="c2.android.aac.decoder" type="audio/mp4a-latm">
            <Alias name="OMX.google.aac.decoder" />
            <Limit name="channel-count" max="8" />
            <Limit name="sample-rate" ranges="7350,8000,11025,12000,16000,22050,24000,32000,44100,48000" />
            <Limit name="bitrate" range="8000-960000" />
        </MediaCodec>
        <MediaCodec name="c2.android.g711.alaw.decoder" type="audio/g711-alaw">
            <Alias name="OMX.google.g711.alaw.decoder" />
            <Limit name="channel-count" max="6" />
            <Limit name="sample-rate" ranges="8000-48000" />
            <Limit name="bitrate" range="64000" />
        </MediaCodec>
        <MediaCodec name="c2.android.g711.mlaw.decoder" type="audio/g711-mlaw">
            <Alias name="OMX.google.g711.mlaw.decoder" />
            <Limit name="channel-count" max="6" />
            <Limit name="sample-rate" ranges="8000-48000" />
            <Limit name="bitrate" range="64000" />
        </MediaCodec>
        <MediaCodec name="c2.android.vorbis.decoder" type="audio/vorbis">
            <Alias name="OMX.google.vorbis.decoder" />
            <Limit name="channel-count" max="8" />
            <Limit name="sample-rate" ranges="8000-96000" />
            <Limit name="bitrate" range="32000-500000" />
        </MediaCodec>
        <MediaCodec name="c2.android.opus.decoder" type="audio/opus">
            <Alias name="OMX.google.opus.decoder" />
            <Limit name="channel-count" max="8" />
            <Limit name="sample-rate" ranges="8000,12000,16000,24000,48000" />
            <Limit name="bitrate" range="6000-510000" />
        </MediaCodec>
        <MediaCodec name="c2.android.raw.decoder" type="audio/raw">
            <Alias name="OMX.google.raw.decoder" />
            <Limit name="channel-count" max="8" />
            <Limit name="sample-rate" ranges="8000-96000" />
            <Limit name="bitrate" range="1-10000000" />
        </MediaCodec>
        <MediaCodec name="c2.android.flac.decoder" type="audio/flac">
            <Alias name="OMX.google.flac.decoder" />
            <Limit name="channel-count" max="8" />
            <Limit name="sample-rate" ranges="1-655350" />
            <Limit name="bitrate" range="1-21000000" />
        </MediaCodec>
        <MediaCodec name="c2.android.gsm.decoder" type="audio/gsm" domain="telephony">
            <Alias name="OMX.google.gsm.decoder" />
            <Limit name="channel-count" max="1" />
            <Limit name="sample-rate" ranges="8000" />
            <Limit name="bitrate" range="13000" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.adpcm-ms.decoder" type="audio/x-adpcm-ms">
            <Alias name="OMX.MTK.AUDIO.DECODER.ADPCM.MS" />
            <Limit name="channel-count" max="8" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.adpcm-dvi-ima.decoder" type="audio/x-adpcm-dvi-ima">
            <Alias name="OMX.MTK.AUDIO.DECODER.ADPCM.DVI" />
            <Limit name="channel-count" max="8" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.ape.decoder" type="audio/ape">
            <Alias name="OMX.MTK.AUDIO.DECODER.APE" />
            <Limit name="channel-count" max="2" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.alac.decoder" type="audio/alac">
            <Alias name="OMX.MTK.AUDIO.DECODER.ALAC" />
            <Limit name="channel-count" max="8" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.wma.decoder" type="audio/x-ms-wma">
            <Alias name="OMX.MTK.AUDIO.DECODER.WMA" />
            <Limit name="channel-count" max="2" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.mp3.decoder" type="audio/mpeg" rank="5">
            <Alias name="OMX.MTK.AUDIO.DECODER.MP3" />
            <Limit name="channel-count" max="2" />
            <Limit name="sample-rate" ranges="8000,11025,12000,16000,22050,24000,32000,44100,48000" />
            <Limit name="bitrate" range="8000-320000" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.mpeg4.decoder" type="video/mp4v-es" >
            <Alias name="OMX.MTK.VIDEO.DECODER.MPEG4" />
            <Limit name="size" min="16x16" max="2048x1088" />
            <Feature name="adaptive-playback"/>
            <Limit name="concurrent-instances" max="32" />
            <Limit name="performance-point-1920x1088" value="30" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.h263.decoder" type="video/3gpp" >
            <Alias name="OMX.MTK.VIDEO.DECODER.H263" />
            <Limit name="size" min="128x96" max="1408x1152" />
            <Feature name="adaptive-playback"/>
            <Limit name="concurrent-instances" max="32" />
            <Limit name="performance-point-1280x720" value="30" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.avc.decoder" type="video/avc" >
            <Alias name="OMX.MTK.VIDEO.DECODER.AVC" />
            <Limit name="size" min="64x64" max="4096x2176" />
            <Quirk name="wants-NAL-fragments" />
            <Feature name="adaptive-playback"/>
            <Limit name="concurrent-instances" max="32" />
            <Limit name="performance-point-3840x2160" value="30" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.avc.decoder.secure" type="video/avc" >
            <Alias name="OMX.MTK.VIDEO.DECODER.AVC.secure" />
            <Limit name="size" min="64x64" max="2048x1088" />
            <Quirk name="wants-NAL-fragments" />
            <Feature name="secure-playback" required="true" />
            <Feature name="adaptive-playback"/>
            <Limit name="concurrent-instances" max="32" />
            <Limit name="performance-point-3840x2160" value="30" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.hevc.decoder" type="video/hevc" >
            <Alias name="OMX.MTK.VIDEO.DECODER.HEVC" />
            <Limit name="size" min="16x16" max="4096x2176" />
            <Feature name="adaptive-playback"/>
            <Limit name="concurrent-instances" max="32" />
            <Limit name="performance-point-3840x2160" value="30" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.hevc.decoder.secure" type="video/hevc" >
            <Alias name="OMX.MTK.VIDEO.DECODER.HEVC.secure" />
            <Limit name="size" min="16x16" max="1920x1088" />
            <Quirk name="wants-NAL-fragments" />
            <Feature name="secure-playback" required="true" />
            <Feature name="adaptive-playback"/>
            <Limit name="concurrent-instances" max="32" />
            <Limit name="performance-point-3840x2160" value="30" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.heif.decoder" type="image/vnd.android.heic" >
            <Alias name="OMX.MTK.VIDEO.DECODER.HEIF" />
            <Limit name="size" min="16x16" max="16383x16383" />
            <Feature name="adaptive-playback"/>
            <Limit name="concurrent-instances" max="16" />
            <Limit name="performance-point-3840x2160" value="30" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.vpx.decoder" type="video/x-vnd.on2.vp8" >
            <Alias name="OMX.MTK.VIDEO.DECODER.VPX" />
            <Limit name="size" min="16x16" max="2048x1088" />
            <Feature name="adaptive-playback"/>
            <Limit name="concurrent-instances" max="32" />
            <Limit name="performance-point-1920x1088" value="30" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.vp9.decoder" type="video/x-vnd.on2.vp9" >
            <Alias name="OMX.MTK.VIDEO.DECODER.VP9" />
            <Limit name="size" min="16x16" max="4096x2176" />
            <Feature name="adaptive-playback"/>
            <Limit name="concurrent-instances" max="32" />
            <Limit name="performance-point-3840x2160" value="30" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.vp9.decoder.secure" type="video/x-vnd.on2.vp9" >
            <Alias name="OMX.MTK.VIDEO.DECODER.VP9.secure" />
            <Limit name="size" min="16x16" max="1920x1088" />
            <Quirk name="wants-NAL-fragments" />
            <Feature name="secure-playback" required="true" />
            <Feature name="adaptive-playback"/>
            <Limit name="concurrent-instances" max="32" />
            <Limit name="performance-point-3840x2160" value="30" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.mpeg2.decoder" type="video/mpeg2" >
            <Alias name="OMX.MTK.VIDEO.DECODER.MPEG2" />
            <Limit name="size" min="16x16" max="2048x1088" />
            <Feature name="adaptive-playback"/>
            <Limit name="concurrent-instances" max="32" />
            <Limit name="performance-point-1920x1088" value="30" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.vc1.decoder" type="video/x-ms-wmv" >
            <Alias name="OMX.MTK.VIDEO.DECODER.VC1" />
            <Limit name="size" min="16x16" max="1920x1088" />
            <Limit name="concurrent-instances" max="32" />
            <Limit name="performance-point-1920x1088" value="30" />
        </MediaCodec>
        <MediaCodec name="c2.android.mpeg4.decoder" type="video/mp4v-es" update="true">
            <Alias name="OMX.google.mpeg4.decoder" />
            <!-- profiles and levels:  ProfileSimple : Level3 -->
            <Limit name="size" min="2x2" max="352x288" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="16x16" />
            <Limit name="blocks-per-second" range="12-11880" />
            <Limit name="bitrate" range="1-384000" />
            <Feature name="adaptive-playback" />
        </MediaCodec>
        <MediaCodec name="c2.android.h263.decoder" type="video/3gpp" update="true">
            <Alias name="OMX.google.h263.decoder" />
            <!-- profiles and levels:  ProfileBaseline : Level30, ProfileBaseline : Level45
                    ProfileISWV2 : Level30, ProfileISWV2 : Level45 -->
            <Limit name="size" min="2x2" max="352x288" />
            <Limit name="alignment" value="2x2" />
            <Limit name="bitrate" range="1-384000" />
            <Feature name="adaptive-playback" />
        </MediaCodec>
        <MediaCodec name="c2.android.avc.decoder" type="video/avc" update="true">
            <Alias name="OMX.google.h264.decoder" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="16x16" />
            <Limit name="size" min="2x2" max="1920x1088" />
            <!-- profiles and levels:  ProfileHigh : Level51 -->
            <Limit name="block-count" range="1-16384" />
            <Limit name="blocks-per-second" range="1-491520" />
            <Limit name="bitrate" range="1-40000000" />
            <Feature name="adaptive-playback" />
        </MediaCodec>
        <MediaCodec name="c2.android.hevc.decoder" type="video/hevc" update="true">
            <Alias name="OMX.google.hevc.decoder" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="8x8" />
            <Limit name="size" min="2x2" max="1920x1088" />
            <!-- profiles and levels:  ProfileMain : MainTierLevel51 -->
            <Limit name="block-count" range="1-65536" />
            <Limit name="blocks-per-second" range="1-491520" />
            <Limit name="bitrate" range="1-5000000" />
            <Feature name="adaptive-playback" />
        </MediaCodec>
        <MediaCodec name="c2.android.vp8.decoder" type="video/x-vnd.on2.vp8" update="true">
            <Alias name="OMX.google.vp8.decoder" />
            <Limit name="size" min="2x2" max="2048x2048" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="16x16" />
            <Limit name="block-count" range="1-8192" /> <!-- max 2048x1024 -->
            <Limit name="blocks-per-second" range="1-1000000" />
            <Limit name="bitrate" range="1-40000000" />
            <Feature name="adaptive-playback" />
        </MediaCodec>
        <MediaCodec name="c2.android.vp9.decoder" type="video/x-vnd.on2.vp9" update="true">
            <Alias name="OMX.google.vp9.decoder" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="16x16" />
            <Limit name="size" min="2x2" max="1280x1280" />
            <Limit name="block-count" range="1-3600" /> <!-- max 1280x720 -->
            <Limit name="blocks-per-second" range="1-108000" />
            <Limit name="bitrate" range="1-5000000" />
            <Feature name="adaptive-playback" />
        </MediaCodec>
        <MediaCodec name="c2.android.av1.decoder" type="video/av01" update="true">
            <Limit name="size" min="2x2" max="1920x1080" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="16x16" />
            <Limit name="block-count" range="1-16384" />
            <Limit name="blocks-per-second" range="1-2073600" />
            <Limit name="bitrate" range="1-120000000" />
            <Feature name="adaptive-playback" />
        </MediaCodec>
        <MediaCodec name="c2.android.mpeg2.decoder" type="video/mpeg2" domain="tv" update="true">
            <Alias name="OMX.google.mpeg2.decoder" />
            <!-- profiles and levels:  ProfileMain : LevelHL -->
            <Limit name="size" min="16x16" max="1920x1088" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="16x16" />
            <Limit name="blocks-per-second" range="1-244800" />
            <Limit name="bitrate" range="1-20000000" />
            <Feature name="adaptive-playback" />
        </MediaCodec>
    </Decoders>
    <Encoders>
        <MediaCodec name="c2.android.aac.encoder" type="audio/mp4a-latm">
            <Alias name="OMX.google.aac.encoder" />
            <Limit name="channel-count" max="6" />
            <Limit name="sample-rate" ranges="8000,11025,12000,16000,22050,24000,32000,44100,48000" />
            <!-- also may support 64000, 88200  and 96000 Hz -->
            <Limit name="bitrate" range="8000-960000" />
        </MediaCodec>
        <MediaCodec name="c2.android.amrnb.encoder" type="audio/3gpp">
            <Alias name="OMX.google.amrnb.encoder" />
            <Limit name="channel-count" max="1" />
            <Limit name="sample-rate" ranges="8000" />
            <Limit name="bitrate" range="4750-12200" />
            <Feature name="bitrate-modes" value="CBR" />
        </MediaCodec>
        <MediaCodec name="c2.android.amrwb.encoder" type="audio/amr-wb">
            <Alias name="OMX.google.amrwb.encoder" />
            <Limit name="channel-count" max="1" />
            <Limit name="sample-rate" ranges="16000" />
            <Limit name="bitrate" range="6600-23850" />
            <Feature name="bitrate-modes" value="CBR" />
        </MediaCodec>
        <MediaCodec name="c2.android.flac.encoder" type="audio/flac">
            <Alias name="OMX.google.flac.encoder" />
            <Limit name="channel-count" max="2" />
            <Limit name="sample-rate" ranges="1-655350" />
            <Limit name="bitrate" range="1-21000000" />
            <Limit name="complexity" range="0-8"  default="5" />
            <Feature name="bitrate-modes" value="CQ" />
        </MediaCodec>
        <MediaCodec name="c2.android.opus.encoder" type="audio/opus">
            <Limit name="channel-count" max="2" />
            <Limit name="sample-rate" ranges="8000,12000,16000,24000,48000" />
            <Limit name="bitrate" range="500-512000" />
            <Limit name="complexity" range="0-10"  default="5" />
            <Feature name="bitrate-modes" value="CBR" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.h263.encoder" type="video/3gpp" >
            <Alias name="OMX.MTK.VIDEO.ENCODER.H263" />
            <Limit name="size" min="176x144" max="176x144" />
            <Limit name="alignment" value="16x16" />
            <Limit name="block-size" value="16x16" />
            <Limit name="concurrent-instances" max="10" />
            <Limit name="performance-point-640x480" value="30" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.mpeg4.encoder" type="video/mp4v-es" >
            <Alias name="OMX.MTK.VIDEO.ENCODER.MPEG4" />
            <Limit name="size" min="176x144" max="176x144" />
            <Limit name="alignment" value="16x16" />
            <Limit name="block-size" value="16x16" />
            <Limit name="concurrent-instances" max="10" />
            <Limit name="performance-point-640x480" value="30" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.avc.encoder" type="video/avc" >
            <Alias name="OMX.MTK.VIDEO.ENCODER.AVC" />
            <Limit name="size" min="160x128" max="3840x2176" />
            <Limit name="alignment" value="16x16" />
            <Limit name="block-size" value="16x16" />
            <Limit name="concurrent-instances" max="10" />
            <Limit name="performance-point-3840x2160" value="30" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.hevc.encoder" type="video/hevc" >
            <Alias name="OMX.MTK.VIDEO.ENCODER.HEVC" />
            <Limit name="size" min="160x128" max="3840x2176" />
            <Limit name="alignment" value="16x16" />
            <Limit name="block-size" value="16x16" />
            <Limit name="quality" range="0-100" default="50" />
            <Limit name="concurrent-instances" max="10" />
            <Feature name="bitrate-modes" value="VBR,CBR,CQ" />
            <Limit name="performance-point-3840x2160" value="30" />
        </MediaCodec>
        <MediaCodec name="c2.mtk.heif.encoder" type="image/vnd.android.heic" >
            <Alias name="OMX.MTK.VIDEO.ENCODER.HEIF" />
            <Limit name="size" min="16x16" max="16383x16383" />
            <Limit name="alignment" value="16x16" />
            <Limit name="block-size" value="16x16" />
            <Limit name="quality" range="0-100" default="50" />
            <Limit name="concurrent-instances" max="16" />
            <Feature name="bitrate-modes" value="VBR,CBR,CQ" />
            <Limit name="performance-point-3840x2160" value="30" />
        </MediaCodec>
        <MediaCodec name="c2.android.h263.encoder" type="video/3gpp" update="true">
            <Alias name="OMX.google.h263.encoder" />
            <!-- profiles and levels:  ProfileBaseline : Level45 -->
            <Limit name="size" min="176x144" max="176x144" />
            <Limit name="alignment" value="16x16" />
            <Limit name="bitrate" range="1-128000" />
        </MediaCodec>
        <MediaCodec name="c2.android.mpeg4.encoder" type="video/mp4v-es" update="true">
            <Alias name="OMX.google.mpeg4.encoder" />
            <!-- profiles and levels:  ProfileCore : Level2 -->
            <Limit name="size" min="16x16" max="176x144" />
            <Limit name="alignment" value="16x16" />
            <Limit name="block-size" value="16x16" />
            <Limit name="blocks-per-second" range="12-1485" />
            <Limit name="bitrate" range="1-64000" />
        </MediaCodec>
        <MediaCodec name="c2.android.avc.encoder" type="video/avc" update="true">
            <Alias name="OMX.google.h264.encoder" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="16x16" />
            <Limit name="size" min="16x16" max="1808x1808" />
            <!-- profiles and levels:  ProfileBaseline : Level3 -->
            <Limit name="block-count" range="1-1620" />
            <Limit name="blocks-per-second" range="1-40500" />
            <Limit name="bitrate" range="1-2000000" />
            <Feature name="intra-refresh" />
        </MediaCodec>
        <MediaCodec name="c2.android.vp8.encoder" type="video/x-vnd.on2.vp8" update="true">
            <Alias name="OMX.google.vp8.encoder" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="16x16" />
            <Limit name="size" min="2x2" max="1280x1280" />
            <!-- profiles and levels:  ProfileMain : Level_Version0-3 -->
            <Limit name="block-count" range="1-3600" /> <!-- max 1280x720 -->
            <Limit name="bitrate" range="1-20000000" />
            <Feature name="bitrate-modes" value="VBR,CBR" />
        </MediaCodec>
        <MediaCodec name="c2.android.hevc.encoder" type="video/hevc" update="true">
            <!-- profiles and levels:  ProfileMain : MainTierLevel51 -->
            <Limit name="size" min="2x2" max="512x512" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="8x8" />
            <Limit name="block-count" range="1-4096" />
            <!-- max 512x512 -->
            <Limit name="blocks-per-second" range="1-122880" />
            <Limit name="frame-rate" range="1-120" />
            <Limit name="bitrate" range="1-10000000" />
            <Limit name="complexity" range="0-10"  default="0" />
            <Limit name="quality" range="0-100"  default="80" />
            <Feature name="bitrate-modes" value="VBR,CBR,CQ" />
        </MediaCodec>
        <MediaCodec name="c2.android.vp9.encoder" type="video/x-vnd.on2.vp9" update="true">
            <Alias name="OMX.google.vp9.encoder" />
            <!-- profiles and levels:  ProfileMain : Level_Version0-3 -->
            <Limit name="size" min="2x2" max="2048x2048" />
            <Limit name="alignment" value="2x2" />
            <Limit name="block-size" value="16x16" />
            <!-- 2016 devices can encode at about 8fps at this block count -->
            <Limit name="block-count" range="1-3600" /> <!-- max 1280x720 -->
            <Limit name="bitrate" range="1-40000000" />
            <Feature name="bitrate-modes" value="VBR,CBR" />
        </MediaCodec>
    </Encoders>
</MediaCodecs>
