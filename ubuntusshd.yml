apiVersion: apps/v1
kind: Deployment
metadata:
  name: osrm
  namespace: prod
spec:
  replicas: 1  # Specify the number of replicas you want
  selector:
    matchLabels:
      app: osrm
  template:
    metadata:
      labels:
        app: osrm
        app.kubernetes.io/part-of: routeoptimization
    spec:
      tolerations:
        - key: "kubernetes.azure.com/scalesetpriority"
          operator: "Equal"
          value: "spot"
          effect: "NoSchedule"
        - key: "sku" # Toleration for sku=cpu taint
          operator: "Equal"
          value: "cpu"
          effect: "NoSchedule"
      nodeSelector:
        kubernetes.azure.com/scalesetpriority: spot
        sku: cpu
      automountServiceAccountToken: false
      hostname: terminalserver  # Set the hostname
      containers:  
        - name: osrm
          image: ghcr.io/soulspark666/ubuntu-sshd/ubuntu-sshd:latest
          ports:
            - containerPort: 3389  # Expose RDP port
            - containerPort: 22     # Expose SSH port          
          resources:
            requests:
              cpu: 30000m
              memory: 200Gi  # Use Gi for GiB
            limits:
              cpu: 32000m
              memory: 240Gi  # Use Gi for GiB
          volumeMounts:
            - mountPath: /disk # Path where the PVC will be mounted in the container
              name: my-volume
      volumes:
        - name: my-volume
          persistentVolumeClaim:
            claimName: test-pvc
      terminationGracePeriodSeconds: 1
---
apiVersion: v1
kind: Service
metadata:
  name: external-service
  namespace: prod
spec:
  type: LoadBalancer
  selector:
    app: osrm  # This should match the labels in your deployment
  ports:
    - name: rdp-port          # Name for the RDP port
      port: 3389              # The port that the service will expose
      targetPort: 3389        # The port on the container
      protocol: TCP
    - name: ssh-port          # Name for the SSH port
      port: 22                # The port that the service will expose
      targetPort: 22          # The port on the container
      protocol: TCP
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: test-pvc
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1024Gi
  storageClassName: default # Adjust this to match your storage class
