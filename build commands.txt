PATH=../bin:$PATH

git remote set-<NAME_EMAIL>:soulspark666/realme_kernel.git

git config --global user.email "<EMAIL>"
git config --global user.name "Soul Spark"

ssh-keygen -t ed25519 -C "<EMAIL>"

cat ~/.ssh/id_ed25519.pub


mkdir -p .repo/local_manifest && cp ../ossi_manifest/ossi.xml "$_"

cp /disk/roms/genesisOS/.repo/repo/repo /disk/roms/bin/repo


repo sync -c --no-clone-bundle --no-tags --optimized-fetch --prune --auto-gc --no-repo-verify --force-sync -j$(nproc --all)


axion cupida gms pico
ax -b -j32


nano device/oplus/mt6893-common/configs/props/vendor.prop

subject='/C=IN/ST=UP/L=Lko/O=SN0W Inc./OU=Android/CN=SoulSpark/emailAddress=<EMAIL>'
for x in releasekey platform shared media networkstack verity otakey testkey sdk_sandbox bluetooth nfc; do \
    ./development/tools/make_key vendor/genesis/signing/keys/$x "$subject"; \
done

for x in releasekey platform shared media networkstack verity otakey testkey sdk_sandbox bluetooth nfc; do
    ./development/tools/make_key vendor/yaap/signing/keys/$x "$subject";
done


croot && git clone https://github.com/VoltageOS/vendor_voltage-priv_keys vendor/lineage-priv/keys

-include vendor/lineage-priv/keys/keys.mk


curl -T out/target/product/cupida/voltage-4.4-cupida-20250525-0239-UNOFFICIAL.zip -s -L -D - xfr.station307.com | grep human


cat out/error.log | curl -F 'file=@out/error.log' https://bin.cyberknight777.dev

export TARGET_BUILD_VARIANT="userdebug"
export TARGET_RELEASE="ap4a"
export TARGET_BOARD_PLATFORM="mt6893"
export TARGET_DISABLES_GMS = true

export BUILD_USERNAME="soulspark"

# GMS space please.
export WITH_GMS=true

TARGET_PRODUCT
TARGET_BUILD_VARIANT
TARGET_PRODUCT
TARGET_RELEASE