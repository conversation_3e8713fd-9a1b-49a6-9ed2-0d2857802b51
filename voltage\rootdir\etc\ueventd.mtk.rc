#Runsheng.<PERSON><PERSON>@ANDROID.STORAGE.3612, 2017/12/29, Add for new reserve partition
#ya<PERSON><PERSON>@PSW.KERNEL.Stability. change the owner from system to root for Android P kernel can >
/dev/block/bootdevice/by-name/oplusreserve1               0660   root     system
/dev/block/sdf6   0660   root     system

#<EMAIL> 2020/08/07 add for opporeserve3 access
/dev/block/by-name/oplusreserve3               0660   root     system
/dev/block/sdc6           0660   root     system

#<EMAIL>.3612, 2018/10/17, Add for new reserve partition
/dev/block/platform/bootdevice/by-name/reserve1               0660   system     system
/dev/block/sdc4   0660   system     system
/dev/block/by-name/oplusreserve1               0660   system     system
#<EMAIL>.3612, 2018/10/17, Add for new reserve partition
/dev/block/mmcblk0p4 0660 system system
/dev/block/mmcblk0p7 0660 root system

#Jinke.zhou modify for engineermode to access by-name/reserve_exp1
/dev/block/platform/bootdevice/by-name/reserve_exp1              0660   system     radio

#Canjie.<PERSON> add for critical log
/dev/block/platform/bootdevice/by-name/reserve2               0660   system     system

#change partition permission
# eMMC only
/dev/block/mmcblk0                               0660    root    system
/dev/block/mmcblk0boot0                          0660    root    system
/dev/block/mmcblk0boot1                          0660    root    system
/dev/misc-sd                                     0660    root    system

# UFS only
/dev/block/sda                                   0660    root    system
/dev/block/sdb                                   0660    root    system
/dev/block/sdc                                   0660    root    system

# eMMC/UFS common
/dev/block/by-name/misc2     0660    root    system
/dev/block/by-name/boot      0640    root    system
/dev/block/by-name/recovery  0640    root    system
/dev/block/by-name/secro     0640    root    system
/dev/block/by-name/seccfg    0660    root    system
/dev/block/by-name/proinfo   0660    root    system
/dev/block/by-name/nvram     0660    root    system
/dev/block/by-name/para      0660    root    system
/dev/block/by-name/logo      0660    root    system
/dev/block/by-name/frp       0660    root    system
/dev/block/by-name/md1img    0640    root    system
/dev/block/by-name/md1img_a  0640    root    system
/dev/block/by-name/md1img_b  0640    root    system
/dev/block/by-name/md1dsp    0660    root    system
/dev/block/by-name/boot_para 0640    root    system

# OTP
/dev/otp                  0660   system     system
/dev/block/platform/bootdevice/by-name/otp       0660    root    system

# Connectivity
/dev/stpwmt               0660   system     system
/dev/wmtdetect            0660   system     system
/dev/fw_log_wmt           0660   system     system
/dev/conninfra_dev        0660   system     system
/dev/conn_scp             0660   system     system
/dev/conn_pwr_dev         0660   system     system

# BT
/dev/stpbt                0660   bluetooth  bluetooth
/dev/fw_log_bt            0660   bluetooth  bluetooth

# GPS
/dev/gpsdl0               0660   gps        gps
/dev/gpsdl1               0660   gps        gps
/dev/stpgps               0660   gps        gps
/dev/gps                  0660   gps        system
/dev/gps_emi              0660   gps        gps
/dev/gps2scp              0660   gps        gps
/dev/gps_pwr              0660   gps        gps

# ANT
/dev/stpant               0660   system     system

# WIFI
/dev/wmtWifi              0660   wifi     wifi
/dev/fw_log_wifi          0660   wifi     wifi

# FMRadio
/dev/fm                   0660   media     media

# ConnFem
/dev/connfem              0660   system     system

# NFC
/dev/st21nfc              0660   nfc        radio
/dev/st54spi              0660   secure_element        secure_element

# MTK BTIF driver
/dev/btif                 0600   system     system

# Trusty driver
/dev/trusty-ipc-dev0      0660   system     system
/dev/nebula-ipc-dev0      0660   system     system

# RPMB (for Trusty)
/dev/block/mmcblk0rpmb    0660   root       system

# TrustonicTEE driver
/dev/mobicore             0600   system     system
/dev/mobicore-user        0666   system     system
/dev/t-base-tui           0666   system     system

#v4l2 device
/dev/video*      0660 camera system
/dev/media*      0660 camera system
/dev/v4l-subdev* 0660 camera system

#v4l2 codec
/dev/vcu    0660 camera system

#v4l2 camera
/dev/mtk_hcp     0660 camera system

# DRM node
/dev/dri/card0            0660   system     system

# MDDP node
/dev/mddp                 0660   system     system

# add for MIDAS
/dev/midas_dev            0660   system     system
/dev/binder_stats         0660   system     system
/*Xuhang.Li for rgbleds*/
/sys/class/leds/red		brightness	0664 system system
/sys/class/leds/red		trigger         0664 system system
/sys/class/leds/red		delay_on        0664 system system
/sys/class/leds/red		delay_off       0664 system system
/sys/class/leds/red		tr1          	0664 system system
/sys/class/leds/red		toff		0664 system system
/sys/class/leds/red		ton		0664 system system
/sys/class/leds/red		pwm_dim_freq	0664 system system
/sys/class/leds/red		pwm_dim_duty	0664 system system
/sys/class/leds/red		support		0664 system system
/sys/class/leds/red		tr1		0664 system system
/sys/class/leds/red		tr2		0664 system system
/sys/class/leds/red		tf1		0664 system system
/sys/class/leds/red		tf2		0664 system system

/sys/class/leds/blue		brightness	0664 system system
/sys/class/leds/blue		trigger         0664 system system
/sys/class/leds/blue		delay_on        0664 system system
/sys/class/leds/blue		delay_off       0664 system system
/sys/class/leds/blue		tr1          	0664 system system
/sys/class/leds/blue		toff          	0664 system system
/sys/class/leds/blue		ton          	0664 system system
/sys/class/leds/blue		pwm_dim_freq    0664 system system
/sys/class/leds/blue		pwm_dim_duty    0664 system system
/sys/class/leds/blue		support		0664 system system
/sys/class/leds/blue		tr1		0664 system system
/sys/class/leds/blue		tr2		0664 system system
/sys/class/leds/blue		tf1		0664 system system
/sys/class/leds/blue		tf2		0664 system system

/sys/class/leds/green		brightness	0664 system system
/sys/class/leds/green		trigger         0664 system system
/sys/class/leds/green		delay_on        0664 system system
/sys/class/leds/green		delay_off       0664 system system
/sys/class/leds/green		tr1          	0664 system system
/sys/class/leds/green		toff          	0664 system system
/sys/class/leds/green		ton          	0664 system system
/sys/class/leds/green		pwm_dim_freq    0664 system system
/sys/class/leds/green		pwm_dim_duty    0664 system system
/sys/class/leds/green		support		0664 system system
/sys/class/leds/green		tr1		0664 system system
/sys/class/leds/green		tr2		0664 system system
/sys/class/leds/green		tf1		0664 system system
/sys/class/leds/green		tf2		0664 system system

# add the read write right of ttyUSB*
/dev/bus/usb/*     	  0660   root       usb
/dev/ttyUSB0              0660   radio      radio
/dev/ttyUSB1              0660   radio      radio
/dev/ttyUSB2              0660   radio      radio
/dev/ttyUSB3              0660   radio      radio
/dev/ttyUSB4              0660   radio      radio

# Anyone can read the logs, but if they're not in the "logs"
# group, then they'll only see log entries for their UID.
/dev/log/ksystem          0600   root       log

/dev/ccci*                0660   radio      radio
/dev/ttyC*                0660   radio      radio
/sys/kernel/ccci          modem_info   0644   radio       radio
/sys/kernel/ccci          md1_postfix  0644   radio       radio
/sys/kernel/ccci          md2_postfix  0644   radio       radio
/dev/eemcs*               0660   radio      radio
/dev/emd*                 0660   radio      radio
/dev/ccci_pcm_rx          0660   audio      audio
/dev/ccci_pcm_tx          0660   audio      audio
/dev/ccci_aud             0660   audio      audio
/dev/ccci2_aud            0660   audio      audio
/dev/ccci3_aud            0660   audio      audio
/dev/ccci_raw_audio            0660   audio      audio
/dev/ccci3_raw_audio            0660   audio      audio
/dev/eemcs_aud            0660   audio      audio
/dev/irtx                 0660   system     system
/dev/lirc*                0660   root       system
/dev/ir-learning          0660   system     system
# SGX device node
/dev/pvrsrvkm             0666   root       root
/dev/pvr_sync             0666   root       root


/sys/devices/platform/leds-mt65xx/leds/green   delay_on      0664  system   system
/sys/devices/platform/leds-mt65xx/leds/green   delay_off      0664  system   system
/sys/devices/platform/leds-mt65xx/leds/red   delay_on      0664  system   system
/sys/devices/platform/leds-mt65xx/leds/red   delay_off      0664  system   system
/sys/devices/platform/leds-mt65xx/leds/blue   delay_on      0664  system   system
/sys/devices/platform/leds-mt65xx/leds/blue   delay_off      0664  system   system

#GPIO
/dev/mtgpio               0600   radio      root

# Camera
/dev/camera_eeprom*       0660   system     camera

#FOR VIA MODEM
/dev/ttySDIO*             0660   radio      sdcard_rw
/dev/ttyRB*               0660   radio      radio

# Mali node
/dev/mali                 0666   system     graphics

# Change ION driver permission
/dev/ion                  0666   system     graphics

# Change DMA_HEAP deice permission
#     refs: /system/core/rootdir/uevent.rc
#           /dev/dma_heap/system      0444   system     system
/dev/dma_heap/mtk_mm                        0444   system     system
/dev/dma_heap/mtk_mm-uncached               0444   system     system
/dev/dma_heap/mtk_prot_region-uncached      0444   system     system
/dev/dma_heap/mtk_svp_region-uncached       0444   system     system

#touch
/dev/touch                0660   root       system
/dev/hotknot              0660   root       system

#hang_detect
/dev/RT_Monitor           0660   system     system
/dev/kick_powerkey        0660   system     system

#MTK In-House TEE
/dev/ttyACM0              0660   radio      radio

subsystem adf
    devname uevent_devname

