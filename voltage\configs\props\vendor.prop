# AAL
ro.vendor.pq.mtk_aal_support=1
ro.vendor.mtk_aal_support=1

# ADB
persist.adb.nonblocking_ffs=0

# AEE
ro.vendor.have_aeev_feature=1
ro.vendor.aee.convert64=0

# Audio
aaudio.mmap_policy=2
aaudio.mmap_exclusive_policy=2
ro.vendor.mtk_audio_alac_support=1
ro.vendor.mtk_hifiaudio_support=1
ro.vendor.mtk_audio_tuning_tool_ver=V2.2
ro.vendor.mtk_audio_ape_support=1

# Bluetooth
persist.vendor.bluetooth.leaudio_mode=ums
ro.vendor.bluetooth.bt_multidevice_perform=600
ro.bluetooth.a2dp_offload.supported=true
persist.bluetooth.a2dp_offload.cap=sbc-aac
persist.bluetooth.a2dp_offload.disabled=true
ro.vendor.bluetooth.a2dp_aac_vbr.is_disabled=true
bluetooth.profile.sap.server.enabled=false
bluetooth.profile.bas.client.enabled=true
bluetooth.profile.asha.central.enabled=true
bluetooth.profile.a2dp.source.enabled=true
bluetooth.profile.avrcp.target.enabled=true
bluetooth.profile.gatt.enabled=true
bluetooth.profile.hfp.ag.enabled=true
bluetooth.profile.hid.device.enabled=true
bluetooth.profile.hid.host.enabled=true
bluetooth.profile.map.server.enabled=true
bluetooth.profile.opp.enabled=true
bluetooth.profile.pan.nap.enabled=true
bluetooth.profile.pan.panu.enabled=true
bluetooth.profile.pbap.server.enabled=true
vendor.bluetooth.ldac.abr=true
ro.vendor.bt.platform=6893

# Brightness
persist.sys.brightness.low.gamma=true

# Camera
camera.disable_zsl_mode=1
vendor.camera.mdp.dre.enable=1
vendor.camera.mdp.cz.enable=1
vendor.mtk.camera.app.fd.video=1
ro.vendor.camera.directfdyuv.support=1
persist.vendor.camera3.pipeline.bufnum.min.high_ram.fdyuv=6
persist.vendor.camera3.pipeline.bufnum.min.low_ram.fdyuv=6
ro.vendor.camera.aishutter.support=1
ro.vendor.camera3.zsl.default=260
ro.vendor.camera3.zsl.project=-130
ro.vendor.mtk_camera_app_version=3
ro.vendor.camera.isp.support.colorspace=0
persist.vendor.camera3.pipeline.bufnum.min.high_ram.imgo=7
persist.vendor.camera3.pipeline.bufnum.min.low_ram.imgo=6
persist.vendor.camera3.pipeline.bufnum.base.imgo=4
persist.vendor.camera3.pipeline.bufnum.min.high_ram.rrzo=7
persist.vendor.camera3.pipeline.bufnum.min.low_ram.rrzo=6
persist.vendor.camera3.pipeline.bufnum.base.rrzo=4
persist.vendor.camera3.pipeline.bufnum.min.high_ram.lcso=7
persist.vendor.camera3.pipeline.bufnum.min.low_ram.lcso=6
persist.vendor.camera3.pipeline.bufnum.base.lcso=4
persist.vendor.camera3.pipeline.bufnum.min.high_ram.rsso=7
persist.vendor.camera3.pipeline.bufnum.min.low_ram.rsso=6
persist.vendor.camera3.pipeline.bufnum.base.rsso=5
ro.mtk_cam_stereo_camera_support=1
ro.vendor.mtk_cam_security_support=1
ro.vendor.mtk_zsdhdr_support=1
ro.mtk_cam_dualzoom_support=1
ro.vendor.mtk_slow_motion_support=1

# Dalvik
dalvik.vm.dex2oat64.enabled=true

# Display
ro.surface_flinger.supports_background_blur=1
ro.sf.blurs_are_expensive=1
ro.opengles.version=196610
debug.hwui.use_hint_manager=true
debug.hwui.target_cpu_time_percent=20
debug.renderengine.backend=skiaglthreaded
ro.hardware.hwcomposer=mtk_common
ro.vendor.composer_version=2.3
debug.mediatek.disp_decompress=1
ro.vendor.pq.mtk_dc_support=1
ro.vendor.pq.mtk_ds_support=1
ro.vendor.pq.mtk_hfg_support=1
ro.vendor.pq.mtk_caltm_support=1
ro.vendor.pq.mtk_clearzoom_support=1
ro.vendor.pq.mtk_mdp_ccorr_support=1
ro.vendor.pq.mtk_pq_interface_support=0
ro.vendor.pq.mtk_disp_c3d_support=0
ro.vendor.pq.mtk_disp_tdshp_support=0
ro.vendor.pq.mtk_disp_color_support=1
ro.vendor.pq.mtk_disp_ccorr_support=1
ro.vendor.pq.mtk_disp_gamma_support=1
ro.vendor.pq.mtk_disp_game_pq_support=1
ro.vendor.pq.mtk_backlight_smooth_support=0
ro.vendor.pq.mtk_ultra_dimming_support=0
ro.vendor.pq.mtk_blulight_def_support=0
ro.surface_flinger.has_wide_color_display=true
ro.surface_flinger.has_HDR_display=true
ro.surface_flinger.primary_display_orientation=ORIENTATION_0
ro.surface_flinger.force_hwc_copy_for_virtual_displays=true
ro.hardware.vulkan=mali
ro.gfx.driver.0=com.mediatek.mt6893.gamedriver
ro.vendor.smvr.p2batch.vga=32
ro.vendor.smvr.p2batch.hd=8
ro.vendor.smvr.p2batch.fhd=4
ro.hardware.egl=meow
ro.surface_flinger.max_frame_buffer_acquired_buffers=3
vendor.mtk.hwc.HWC_PLAT_SWITCH_EXTEND_SF_TARGET_TS_FOR_VIDEO=1
persist.sys.sf.color_saturation=1.0
persist.sys.sf.native_mode=2

# DRAM
ro.vendor.mtk_config_max_dram_size=0x800000000

# DRM
drm.service.enabled=true
ro.vendor.mtk_widevine_drm_l1_support=1
ro.vendor.mtk_sec_video_path_support=1
ro.netflix.bsp_rev=MTK6893-32816-1
ro.vendor.mtk_mvpu_security_support=0

# FM Radio
persist.vendor.connsys.fm_chipid=mt6635
ro.vendor.fm.platform=connac2x

# FUSE passthrough
persist.sys.fuse.passthrough.enable=true

# FRP
ro.frp.pst=/dev/block/by-name/frp

# GPS
ro.vendor.mtk_agps_app=1
ro.vendor.mtk_gps_support=1
ro.vendor.gps.chrdev=gps_drv
ro.vendor.mtk_log_hide_gps=0

# IMS
ro.vendor.md_auto_setup_ims=1
persist.vendor.ims_support=1
persist.vendor.mtk_dynamic_ims_switch=1
persist.vendor.mtk_wfc_support=1
persist.vendor.volte_support=1
persist.vendor.mtk.volte.enable=1
persist.vendor.vilte_support=0
persist.vendor.viwifi_support=1
persist.vendor.mtk_ct_volte_support=3

# Init
ro.vendor.rc=/vendor/etc/init/hw/
ro.vendor.init.sensor.rc=init.sensor_2_0.rc

# Kernel
ro.logd.kernel=false

# LMK
ro.lmk.psi_complete_stall_ms=150
ro.lmk.swap_free_low_percentage=20
ro.lmk.thrashing_limit=25
ro.lmk.thrashing_limit_decay=50
ro.lmk.limit_killing_array_kb=819200,819200,655360,655360
ro.lmk.swap_util_max=90
ro.lmk.psi_complete_stall_ms_dup=70
ro.lmk.nandswap_version=2
ro.lmk.psi_scrit_complete_stall_ms=150
ro.lmk.swap_is_low_kill_enable=1

# LMKD/PSI
ro.lmk.kill_heaviest_task=true
ro.lmk.kill_timeout_ms=15
# Media Properties
ro.vendor.jpeg_decode_sw_opt=2
ro.vendor.mtk_video_hevc_enc_support=1
debug.mediatek.appgamepq_compress=1

# HDR and PQ Properties
ro.vendor.mtk_pq_support=2
ro.vendor.mtk_pq_color_mode=3
ro.vendor.pq.mtk_pq_video_whitelist_support=0
ro.vendor.pq.mtk_video_transition=0
ro.vendor.pq.mtk_scltm_support=1
ro.vendor.mml.mtk_mml_support=1
ro.vendor.pq.mtk_ai_scence_pq_support=0
ro.vendor.pq.mtk_hdr10_plus_recording_support=1
ro.vendor.pq.mtk_ai_sdr_to_hdr_support=1
ro.vendor.pq.mtk_ultra_resolution_support=0
ro.vendor.pq.mtk_dre30_support=1
ro.vendor.mtk_hdr_video_support=1
ro.vendor.mtk_hdr10_support=1
ro.vendor.mtk_hdr10_plus_support=1
ro.vendor.pq.mtk_hdr10_plus_process=1
ro.vendor.mtk_video_hal_support=1

# MediaTek Codec Properties
vendor.mtk.vdec.waitkeyframeforplay=9
vendor.mtk.vdec.enable.downsize=1
vendor.mtk.vdec.enable.hdr10plus=1
vendor.mtk.vdec.enable.hdr10=1

# Miscellaneous Properties
media.c2.dmabuf.padding=3072

# Mediatek
ro.vendor.mediatek.platform=MT6893
ro.vendor.mediatek.version.branch=alps-mp-s0.mp1.tc16sp-pr8
ro.vendor.mediatek.version.release=alps-mp-s0.mp1.tc16sp-pr8-V1.5
ro.vendor.mtk_prefer_64bit_proc=0
ro.vendor.mtk_gamehdr_support=1

# Misc
ro.oem_unlock_supported=1
ro.vendor.mtk_aod_support=0
ro.vendor.mtk_ovl_bringup=0
dalvik.vm.mtk-stack-trace-file=/data/anr/mtk_traces.txt
dalvik.vm.heapsize=512m
persist.vendor.factory.GB2312=yes

# Neural Networks
ro.vendor.mtk_nn_support=1
ro.vendor.mtk_nn.option=A,B,E,F,Z
ro.vendor.mtk_nn_quant_preferred=1
ro.vendor.mtk_nn_baseline_support=1
debug.mtk_tflite.target_nnapi=29

# Power HAL
vendor.powerhal.disp.idle_support=false

# Radio
persist.vendor.radio.fd.counter=150
persist.vendor.radio.fd.off.counter=50
persist.vendor.radio.fd.r8.counter=150
persist.vendor.radio.fd.off.r8.counter=50
persist.vendor.radio.flashless.fsm=0
persist.vendor.radio.flashless.fsm_cst=0
persist.vendor.radio.flashless.fsm_rw=0
persist.vendor.radio.mtk_ps2_rat=N/L/W/G
persist.vendor.radio.mtk_ps3_rat=G
persist.vendor.radio.mtk_dsbp_support=2
persist.radio.multisim.config=dsds
persist.vendor.radio.msimmode=dsds
ro.vendor.radio.max.multisim=dsds
persist.vendor.radio.smart.data.switch=1
ro.vendor.mtk_fd_support=1
ro.vendor.mtk_single_bin_modem_support=1
ro.vendor.mtk_wapi_support=1
ro.vendor.mtk_embms_support=1
ro.vendor.mtk_sim_hot_swap_common_slot=1
ro.vendor.mtk_sim_card_onoff=3
persist.vendor.mims_support=2
persist.vendor.log.tel_log_ctrl=1
ro.vendor.mtk_gwsd_capability=2
ro.vendor.num_md_protocol=2
ro.vendor.mtk_mcf_support=1
ro.vendor.mtk_protocol1_rat_config=N/C/Lf/Lt/W/G
ro.vendor.mtk_c2k_lte_mode=2
ro.vendor.mtk_c2k_support=1
telephony.lteOnCdmaDevice=1
ro.telephony.default_network=33,33,33,33
ro.vendor.mtk_ps1_rat=N/C/Lf/Lt/W/G
ro.vendor.mtk_lte_support=1
persist.vendor.mtk_sim_switch_policy=2
ro.vendor.mtk_world_phone_policy=0
ro.vendor.mtk_rild_read_imsi=1
ro.vendor.sim_me_lock_mode=3
telephony.active_modems.max_count=2
ro.vendor.mtk_wappush_support=1
ro.vendor.mtk_modem_monitor_support=1
ro.vendor.mtk_external_sim_support=1
ro.vendor.mtk_external_sim_only_slots=0
ro.vendor.mtk_mobile_management=1
ro.vendor.mtk_ril_mode=c6m_1rild
ro.vendor.md_prop_ver=1
ro.vendor.mtk_md_world_mode_support=1
ro.vendor.mtk_data_config=1
ro.vendor.mtk_eccci_c2k=1
persist.vendor.md_c2k_cap_dep_check=0

# Storage
ro.vendor.mtk_f2fs_enable=1
ro.vendor.mtk_emmc_support=1
ro.vendor.mtk_ufs_support=1
ro.crypto.volume.filenames_mode=aes-256-cts

# Sensors
ro.vendor.mtk.sensor.support=yes

# Surface Flinger
ro.surface_flinger.protected_contents=true
debug.sf.disable_client_composition_cache=1
debug.sf.enable_gl_backpressure=1
debug.sf.predict_hwc_composition_strategy=0
debug.sf.use_phase_offsets_as_durations=1
debug.sf.late.sf.duration=27600000
debug.sf.late.app.duration=20000000
debug.sf.early.sf.duration=27600000
debug.sf.early.app.duration=20000000
debug.sf.earlyGl.sf.duration=27600000
debug.sf.earlyGl.app.duration=20000000
debug.sf.hwc.min.duration=17000000
debug.sf.high_fps.late.sf.duration=8300000
debug.sf.high_fps.late.app.duration=10000000
debug.sf.high_fps.early.sf.duration=8300000
debug.sf.high_fps.early.app.duration=10000000
debug.sf.high_fps.earlyGl.sf.duration=8300000
debug.sf.high_fps.earlyGl.app.duration=10000000
debug.sf.high_fps.hwc.min.duration=7500000
debug.sf.enable_transaction_tracing=false
debug.sf.auto_latch_unsignaled=0
debug.sf.enable_hwc_vds=0
ro.surface_flinger.use_content_detection_for_refresh_rate=true

# TEE
ro.vendor.mtk_svp_on_mtee_support=1
ro.vendor.mtk_tee_gp_support=1
ro.vendor.mtk_trustonic_tee_support=1
ro.mtk_key_manager_support=1
ro.hardware.kmsetkey=trustonic
ro.hardware.gatekeeper=trustonic

# WFD
ro.vendor.mtk_wfd_support=1
ro.vendor.wfd.dummy.enable=0
ro.vendor.wfd.iframesize.level=0

# Wifi
ro.vendor.wifi.sap.interface=ap0
ro.vendor.wlan.gen=gen4m
ro.vendor.wlan.chrdev=wmt_chrdev_wifi
persist.vendor.connsys.coredump.mode=0
persist.vendor.connsys.chipid=-1
persist.vendor.connsys.patch.version=-1
persist.vendor.connsys.dynamic.dump=0
vendor.connsys.driver.ready=no
ro.telephony.iwlan_operation_mode=AP-assisted
ro.vendor.ap_info_monitor=0
ro.vendor.wifi.sap.concurrent.iface=ap1
