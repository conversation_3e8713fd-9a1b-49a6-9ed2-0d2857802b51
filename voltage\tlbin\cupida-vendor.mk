# Automatically generated file. DO NOT MODIFY
#
# This file is generated by device/oplus/cupida/setup-makefiles.sh

PRODUCT_SOONG_NAMESPACES += \
    vendor/oplus/cupida

PRODUCT_COPY_FILES += \
    vendor/oplus/cupida/proprietary/odm/etc/audio/smartpa_param/tfa98xx.cnt:$(TARGET_COPY_OUT_ODM)/etc/audio/smartpa_param/tfa98xx.cnt \
    vendor/oplus/cupida/proprietary/odm/etc/audio/smartpa_param/tfa98xx_music.bin:$(TARGET_COPY_OUT_ODM)/etc/audio/smartpa_param/tfa98xx_music.bin \
    vendor/oplus/cupida/proprietary/odm/etc/audio/smartpa_param/tfa98xx_ringtone.bin:$(TARGET_COPY_OUT_ODM)/etc/audio/smartpa_param/tfa98xx_ringtone.bin \
    vendor/oplus/cupida/proprietary/odm/etc/audio/smartpa_param/tfa98xx_voice.bin:$(TARGET_COPY_OUT_ODM)/etc/audio/smartpa_param/tfa98xx_voice.bin \
    vendor/oplus/cupida/proprietary/odm/etc/audio/smartpa_param/tfa98xx_voip.bin:$(TARGET_COPY_OUT_ODM)/etc/audio/smartpa_param/tfa98xx_voip.bin \
    vendor/oplus/cupida/proprietary/odm/etc/camera/CameraHWConfiguration.config:$(TARGET_COPY_OUT_ODM)/etc/camera/CameraHWConfiguration.config \
    vendor/oplus/cupida/proprietary/odm/etc/init/init.camera_update.rc:$(TARGET_COPY_OUT_ODM)/etc/init/init.camera_update.rc \
    vendor/oplus/cupida/proprietary/odm/etc/init/<EMAIL>:$(TARGET_COPY_OUT_ODM)/etc/init/<EMAIL> \
    vendor/oplus/cupida/proprietary/odm/firmware/aw8697_haptic.bin:$(TARGET_COPY_OUT_ODM)/firmware/aw8697_haptic.bin \
    vendor/oplus/cupida/proprietary/odm/firmware/aw8697_haptic_170.bin:$(TARGET_COPY_OUT_ODM)/firmware/aw8697_haptic_170.bin \
    vendor/oplus/cupida/proprietary/odm/firmware/aw8697_haptic_170_soft.bin:$(TARGET_COPY_OUT_ODM)/firmware/aw8697_haptic_170_soft.bin \
    vendor/oplus/cupida/proprietary/odm/firmware/aw8697_haptic_235.bin:$(TARGET_COPY_OUT_ODM)/firmware/aw8697_haptic_235.bin \
    vendor/oplus/cupida/proprietary/odm/firmware/aw8697_haptic_235_19161.bin:$(TARGET_COPY_OUT_ODM)/firmware/aw8697_haptic_235_19161.bin \
    vendor/oplus/cupida/proprietary/odm/firmware/fastchg/20615/charging_thermal_config_default.txt:$(TARGET_COPY_OUT_ODM)/firmware/fastchg/20615/charging_thermal_config_default.txt \
    vendor/oplus/cupida/proprietary/odm/firmware/fastchg/charging_thermal_config_default.txt:$(TARGET_COPY_OUT_ODM)/firmware/fastchg/charging_thermal_config_default.txt \
    vendor/oplus/cupida/proprietary/odm/firmware/fastchg/smart_chg_config_table.txt:$(TARGET_COPY_OUT_ODM)/firmware/fastchg/smart_chg_config_table.txt \
    vendor/oplus/cupida/proprietary/odm/firmware/tfa98xx.cnt:$(TARGET_COPY_OUT_ODM)/firmware/tfa98xx.cnt \
    vendor/oplus/cupida/proprietary/odm/firmware/tfa98xx/tfa98xx_vibrator_remain_12_RTP_56_226Hz.bin:$(TARGET_COPY_OUT_ODM)/firmware/tfa98xx/tfa98xx_vibrator_remain_12_RTP_56_226Hz.bin \
    vendor/oplus/cupida/proprietary/odm/firmware/tfa98xx/tfa98xx_vibrator_remain_12_RTP_56_230Hz.bin:$(TARGET_COPY_OUT_ODM)/firmware/tfa98xx/tfa98xx_vibrator_remain_12_RTP_56_230Hz.bin \
    vendor/oplus/cupida/proprietary/odm/firmware/tfa98xx/tfa98xx_vibrator_remain_12_RTP_56_234Hz.bin:$(TARGET_COPY_OUT_ODM)/firmware/tfa98xx/tfa98xx_vibrator_remain_12_RTP_56_234Hz.bin \
    vendor/oplus/cupida/proprietary/odm/firmware/tfa98xx/tfa98xx_vibrator_remain_12_RTP_56_237Hz.bin:$(TARGET_COPY_OUT_ODM)/firmware/tfa98xx/tfa98xx_vibrator_remain_12_RTP_56_237Hz.bin \
    vendor/oplus/cupida/proprietary/odm/firmware/tp/20615/FW_FT3518_SAMSUNG.img:$(TARGET_COPY_OUT_ODM)/firmware/tp/20615/FW_FT3518_SAMSUNG.img \
    vendor/oplus/cupida/proprietary/odm/firmware/tp/20615/FW_FT3518_SAMSUNG_FAE.img:$(TARGET_COPY_OUT_ODM)/firmware/tp/20615/FW_FT3518_SAMSUNG_FAE.img \
    vendor/oplus/cupida/proprietary/odm/firmware/tp/20615/LIMIT_FT3518_SAMSUNG.img:$(TARGET_COPY_OUT_ODM)/firmware/tp/20615/LIMIT_FT3518_SAMSUNG.img \
    vendor/oplus/cupida/proprietary/odm/vendor/app/mcRegistry/030c0000000000000000000000000000.drbin:$(TARGET_COPY_OUT_ODM)/vendor/app/mcRegistry/030c0000000000000000000000000000.drbin \
    vendor/oplus/cupida/proprietary/odm/vendor/app/mcRegistry/030c0000000000000000000000000000.tlbin:$(TARGET_COPY_OUT_ODM)/vendor/app/mcRegistry/030c0000000000000000000000000000.tlbin \
    vendor/oplus/cupida/proprietary/odm/vendor/app/mcRegistry/04320000000000000000000000000000.tlbin:$(TARGET_COPY_OUT_ODM)/vendor/app/mcRegistry/04320000000000000000000000000000.tlbin \
    vendor/oplus/cupida/proprietary/odm/vendor/app/mcRegistry/05060000000000000000000000000000.tabin:$(TARGET_COPY_OUT_ODM)/vendor/app/mcRegistry/05060000000000000000000000000000.tabin \
    vendor/oplus/cupida/proprietary/odm/vendor/app/mcRegistry/05070000000000000000000000000000.drbin:$(TARGET_COPY_OUT_ODM)/vendor/app/mcRegistry/05070000000000000000000000000000.drbin \
    vendor/oplus/cupida/proprietary/odm/vendor/app/mcRegistry/070f0000000000000000000000000a0a.tlbin:$(TARGET_COPY_OUT_ODM)/vendor/app/mcRegistry/070f0000000000000000000000000a0a.tlbin \
    vendor/oplus/cupida/proprietary/odm/vendor/app/mcRegistry/08010203000000000000000000000000.tabin:$(TARGET_COPY_OUT_ODM)/vendor/app/mcRegistry/08010203000000000000000000000000.tabin \
    vendor/oplus/cupida/proprietary/odm/vendor/app/mcRegistry/09060000000000000000000000000000.tabin:$(TARGET_COPY_OUT_ODM)/vendor/app/mcRegistry/09060000000000000000000000000000.tabin \
    vendor/oplus/cupida/proprietary/odm/vendor/app/mcRegistry/09070000000000000000000000000000.drbin:$(TARGET_COPY_OUT_ODM)/vendor/app/mcRegistry/09070000000000000000000000000000.drbin \
    vendor/oplus/cupida/proprietary/odm/vendor/app/mcRegistry/09080000000000000000000000000000.tlbin:$(TARGET_COPY_OUT_ODM)/vendor/app/mcRegistry/09080000000000000000000000000000.tlbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/020f0000000000000000000000000000.drbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/020f0000000000000000000000000000.drbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/020f0000000000000000000000000000.tlbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/020f0000000000000000000000000000.tlbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/05120000000000000000000000000000.drbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/05120000000000000000000000000000.drbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/05120000000000000000000000000000.tlbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/05120000000000000000000000000000.tlbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/05120000000000000000000000000001.drbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/05120000000000000000000000000001.drbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/05120000000000000000000000000001.tlbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/05120000000000000000000000000001.tlbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/06090000000000000000000000000000.drbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/06090000000000000000000000000000.drbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/06090000000000000000000000000000.tlbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/06090000000000000000000000000000.tlbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/0706000000000000000000000000004d.tlbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/0706000000000000000000000000004d.tlbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/07061000000000000000000000000000.tlbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/07061000000000000000000000000000.tlbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/07150000000000000000000000000000.drbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/07150000000000000000000000000000.drbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/07150000000000000000000000000000.tlbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/07150000000000000000000000000000.tlbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/07170000000000000000000000000000.drbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/07170000000000000000000000000000.drbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/07170000000000000000000000000000.tlbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/07170000000000000000000000000000.tlbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/08030000000000000000000000000000.tabin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/08030000000000000000000000000000.tabin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/08040000000000000000000000003419.tabin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/08040000000000000000000000003419.tabin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/08050000000000000000000000003419.drbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/08050000000000000000000000003419.drbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/08050000000000000000000000003419.tlbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/08050000000000000000000000003419.tlbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/08110000000000000000000000000000.tabin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/08110000000000000000000000000000.tabin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/40188311faf343488db888ad39496f9a.drbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/40188311faf343488db888ad39496f9a.drbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/40188311faf343488db888ad39496f9a.tlbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/40188311faf343488db888ad39496f9a.tlbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/5020170115e016302017012521300000.drbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/5020170115e016302017012521300000.drbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/5020170115e016302017012521300000.tlbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/5020170115e016302017012521300000.tlbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/9073f03a9618383bb1856eb3f990babd.drbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/9073f03a9618383bb1856eb3f990babd.drbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/9073f03a9618383bb1856eb3f990babd.tlbin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/9073f03a9618383bb1856eb3f990babd.tlbin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/98fb95bcb4bf42d26473eae48690d7ea.tabin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/98fb95bcb4bf42d26473eae48690d7ea.tabin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/abcd270ea5c44c58bcd3384a2fa2539e.tabin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/abcd270ea5c44c58bcd3384a2fa2539e.tabin \
    vendor/oplus/cupida/proprietary/vendor/app/mcRegistry/e97c270ea5c44c58bcd3384a2fa2539e.tabin:$(TARGET_COPY_OUT_VENDOR)/app/mcRegistry/e97c270ea5c44c58bcd3384a2fa2539e.tabin \
    vendor/oplus/cupida/proprietary/vendor/etc/init/camerahalserver.rc:$(TARGET_COPY_OUT_VENDOR)/etc/init/camerahalserver.rc \
    vendor/oplus/cupida/proprietary/vendor/etc/init/tee.rc:$(TARGET_COPY_OUT_VENDOR)/etc/init/tee.rc \
    vendor/oplus/cupida/proprietary/vendor/etc/init/trustonic.rc:$(TARGET_COPY_OUT_VENDOR)/etc/init/trustonic.rc \
    vendor/oplus/cupida/proprietary/vendor/etc/init/<EMAIL>:$(TARGET_COPY_OUT_VENDOR)/etc/init/<EMAIL> \
    vendor/oplus/cupida/proprietary/vendor/etc/smartpa_param/tfa98xx_device.bin:$(TARGET_COPY_OUT_VENDOR)/etc/smartpa_param/tfa98xx_device.bin \
    vendor/oplus/cupida/proprietary/vendor/etc/smartpa_param/tfa98xx_music.bin:$(TARGET_COPY_OUT_VENDOR)/etc/smartpa_param/tfa98xx_music.bin \
    vendor/oplus/cupida/proprietary/vendor/etc/smartpa_param/tfa98xx_ringtone.bin:$(TARGET_COPY_OUT_VENDOR)/etc/smartpa_param/tfa98xx_ringtone.bin \
    vendor/oplus/cupida/proprietary/vendor/etc/smartpa_param/tfa98xx_voice.bin:$(TARGET_COPY_OUT_VENDOR)/etc/smartpa_param/tfa98xx_voice.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/BT_FW.cfg:$(TARGET_COPY_OUT_VENDOR)/firmware/BT_FW.cfg \
    vendor/oplus/cupida/proprietary/vendor/firmware/WIFI_RAM_CODE_soc3_0_1a_1.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/WIFI_RAM_CODE_soc3_0_1a_1.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/conninfra.cfg:$(TARGET_COPY_OUT_VENDOR)/firmware/conninfra.cfg \
    vendor/oplus/cupida/proprietary/vendor/firmware/fm_cust.cfg:$(TARGET_COPY_OUT_VENDOR)/firmware/fm_cust.cfg \
    vendor/oplus/cupida/proprietary/vendor/firmware/gt9886_cfg_6893v01.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/gt9886_cfg_6893v01.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/gt9886_firmware_6893v01.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/gt9886_firmware_6893v01.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/gt9896s_cfg_6893v01.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/gt9896s_cfg_6893v01.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/gt9896s_cfg_6893v02.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/gt9896s_cfg_6893v02.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/gt9896s_cfg_6893v03.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/gt9896s_cfg_6893v03.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/gt9896s_cfg_6893v04.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/gt9896s_cfg_6893v04.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/gt9896s_cfg_6893v05.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/gt9896s_cfg_6893v05.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/gt9896s_cfg_6895v01.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/gt9896s_cfg_6895v01.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/gt9896s_cfg_6983v01.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/gt9896s_cfg_6983v01.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/gt9896s_firmware_6893v01.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/gt9896s_firmware_6893v01.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/gt9896s_firmware_6893v02.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/gt9896s_firmware_6893v02.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/gt9896s_firmware_6893v03.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/gt9896s_firmware_6893v03.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/gt9896s_firmware_6893v04.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/gt9896s_firmware_6893v04.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/gt9896s_firmware_6893v05.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/gt9896s_firmware_6893v05.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/gt9896s_firmware_6895v01.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/gt9896s_firmware_6895v01.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/gt9896s_firmware_6983v01.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/gt9896s_firmware_6983v01.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/lib3a.ccu:$(TARGET_COPY_OUT_VENDOR)/firmware/lib3a.ccu \
    vendor/oplus/cupida/proprietary/vendor/firmware/mt6635_fm_v1_coeff.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/mt6635_fm_v1_coeff.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/mt6635_fm_v1_patch.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/mt6635_fm_v1_patch.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/novatek_ts_fw.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/novatek_ts_fw.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/novatek_ts_fw_144.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/novatek_ts_fw_144.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/novatek_ts_fw_jdi.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/novatek_ts_fw_jdi.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/novatek_ts_fw_tm.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/novatek_ts_fw_tm.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/remoteproc_scp:$(TARGET_COPY_OUT_VENDOR)/firmware/remoteproc_scp \
    vendor/oplus/cupida/proprietary/vendor/firmware/soc3_0_patch_wmmcu_1a_1_hdr.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/soc3_0_patch_wmmcu_1a_1_hdr.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/soc3_0_ram_bt_1_1_hdr.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/soc3_0_ram_bt_1_1_hdr.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/soc3_0_ram_bt_1a_1_hdr.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/soc3_0_ram_bt_1a_1_hdr.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/soc3_0_ram_mcu_1_1_hdr.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/soc3_0_ram_mcu_1_1_hdr.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/soc3_0_ram_mcu_1a_1_hdr.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/soc3_0_ram_mcu_1a_1_hdr.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/soc3_0_ram_mcu_e1_hdr.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/soc3_0_ram_mcu_e1_hdr.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/soc3_0_ram_wifi_1a_1_hdr.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/soc3_0_ram_wifi_1a_1_hdr.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/soc3_0_ram_wmmcu_1a_1_hdr.bin:$(TARGET_COPY_OUT_VENDOR)/firmware/soc3_0_ram_wmmcu_1a_1_hdr.bin \
    vendor/oplus/cupida/proprietary/vendor/firmware/valhall-1691526.wa:$(TARGET_COPY_OUT_VENDOR)/firmware/valhall-1691526.wa

PRODUCT_PACKAGES += \
    vendor.mediatek.hardware.camera.atms@1.0-impl \
    vendor.mediatek.hardware.camera.bgservice@1.1-impl \
    vendor.mediatek.hardware.camera.isphal@1.0-impl \
    libMcClient \
    libMtkSpeechEnh \
    libTEECommon \
    libcamdrv_imem \
    libcamdrv_isp \
    libcamdrv_tuning_mgr \
    libcamdrv_twin \
    libcamera_core_hwi \
    libcameracustom.camera.sensors \
    libcameracustom.eis \
    libcameracustom \
    libcustomer_cameradata \
    libdip_drv \
    libdip_imem \
    libdip_postproc \
    libeffecthal.base \
    libgz_gp_client \
    libgz_uree \
    libmtkares \
    libmtkcutils \
    libmtkhardware_legacy \
    libmtkisp_metadata \
    libmtklimiter_vendor \
    libmtknetutils \
    libmtkproperty \
    libmtkrillog \
    libmtkshifter_vendor \
    libmtkspparser \
    libmtksysutils \
    libocam_common \
    liboplus.aishutter \
    liboplus_audio_config \
    liboplus_mtkcam_lightsensorprovider \
    liboplus_platform_hwi \
    libstereoinfoaccessor_vsdof \
    vendor.mediatek.hardware.camera.atms@1.0 \
    vendor.mediatek.hardware.camera.bgservice@1.0 \
    vendor.mediatek.hardware.camera.bgservice@1.1 \
    vendor.mediatek.hardware.camera.isphal@1.0 \
    vendor.oplus.hardware.cammidasservice@1.0 \
    libfgauge_gm30 \
    gc02m1_mipi_raw_21127_IdxMgr \
    gc02m1_mipi_raw_21305_IdxMgr \
    gc02m1_mipi_raw_21651_IdxMgr \
    gc02m1b_mipi_mono20817_IdxMgr \
    gc02m1b_mipi_mono21881_IdxMgr \
    gc02m1b_mipi_mono_21127_IdxMgr \
    gc02m1b_mipi_mono_IdxMgr \
    hi846_mipi_raw_20615_IdxMgr \
    android.hardware.camera.provider@2.6-impl-mediatek \
    vendor.mediatek.hardware.camera.lomoeffect@1.0-impl \
    imx355_mipi_raw20817_IdxMgr \
    imx355_mipi_raw21015_IdxMgr \
    imx355_mipi_raw21881_IdxMgr \
    imx355_mipi_raw_21061_IdxMgr \
    imx355_mipi_raw_21305_IdxMgr \
    imx355_mipi_raw_21651_IdxMgr \
    imx355_mipi_raw_IdxMgr \
    imx471_mipi_raw_20615_IdxMgr \
    imx471_mipi_raw_20619_IdxMgr \
    imx471_mipi_raw_21127_IdxMgr \
    imx471_mipi_raw_21651_IdxMgr \
    imx471_mipi_raw_IdxMgr \
    imx615_mipi_raw20817_IdxMgr \
    imx615_mipi_raw21881_IdxMgr \
    imx615_mipi_raw_21305_IdxMgr \
    imx615_mipi_raw_IdxMgr \
    imx682_mipi_raw_20615_IdxMgr \
    imx709_mipi_raw21015_IdxMgr \
    imx709_mipi_raw_21127_IdxMgr \
    imx709_mipi_raw_21305_IdxMgr \
    imx766_mipi_raw20817_IdxMgr \
    imx766_mipi_raw21015_IdxMgr \
    imx766_mipi_raw21881_IdxMgr \
    imx766_mipi_raw_21305_IdxMgr \
    lib3a.ae.core \
    lib3a.ae \
    lib3a.ae.stat \
    lib3a.af.assist \
    lib3a.af.assist.utils \
    lib3a.af.core \
    lib3a.af \
    lib3a.aishutter.models \
    lib3a.alsflicker \
    lib3a.awb.core \
    lib3a.ccudrv \
    lib3a.ccuif \
    lib3a.custom.ae \
    lib3a.dce \
    lib3a.flash \
    lib3a.flicker \
    lib3a.gma \
    lib3a.lce \
    lib3a.log \
    lib3a.n3d3a \
    lib3a.sensors.color \
    lib3a.sensors.flicker \
    libSonyIMX230PdafLibrary \
    libSonyIMX230PdafLibraryWrapper \
    libSonyIMX338PdafLibrary \
    libSonyIMX338PdafLibraryWrapper \
    libSonyIMX386PdafLibrary \
    libSonyIMX386PdafLibraryWrapper \
    libSonyIMX519PdafLibrary \
    libSonyIMX519PdafLibraryWrapper \
    libaiawb_moon \
    libaiawb_p1ggm \
    libaiawb_sun \
    libasn1c_core \
    libasn1c_mapi \
    libasn1c_mdmi \
    libcam.afhal \
    libcam.chdr \
    libcam.feature_utils \
    libcam.hal3a.cctsvr \
    libcam.hal3a.log \
    libcam.hal3a.v3.ae \
    libcam.hal3a.v3.ai3a \
    libcam.hal3a.v3.awb \
    libcam.hal3a.v3.dng \
    libcam.hal3a.v3.fsmgr \
    libcam.hal3a.v3.lscMgr \
    libcam.hal3a.v3.lsctbl.50 \
    libcam.hal3a.v3.nvram.50 \
    libcam.hal3a.v3.platform \
    libcam.hal3a.v3.resultpool \
    libcam.hal3a.v3 \
    libcam.halisp.buf \
    libcam.halisp.common \
    libcam.halisp \
    libcam.halsensor.hwintegration \
    libcam.halsensor \
    libcam.iopipe \
    libcam.isptuning \
    libcam.pdtblgen \
    libcam.seninfn3d \
    libcam.tuning.cache \
    libcam.utils.sensorprovider \
    libcam.vhdr \
    libcamalgo.3dnr \
    libcamalgo.aibc \
    libcamalgo.aidepth \
    libcamalgo.aihdr \
    libcamalgo.ainr \
    libcamalgo.dngop \
    libcamalgo.eis \
    libcamalgo.fdft \
    libcamalgo.fsc \
    libcamalgo.gyro \
    libcamalgo.ispfeature \
    libcamalgo.lmv \
    libcamalgo.lsc \
    libcamalgo.mfnr \
    libcamalgo.n3d \
    libcamalgo.nr \
    libcamalgo.platform \
    libcamalgo.platform2 \
    libcamalgo.rotate \
    libcamalgo.utility \
    libcamalgo.vaidepth \
    libcamalgo.vsf \
    libcamalgo.warp \
    libfeature.face \
    libfeature.stereo.provider \
    libfeature.vsdof.hal \
    libfeature_3dnr \
    libfeature_eis \
    libfeature_fsc \
    libfeature_lmv \
    libfeatureiodrv_mem \
    libforkexecwrap \
    libgwsdv2-ril \
    libmtkcam.atmseventmgr \
    libmtkcam.eventcallback \
    libmtkcam.featurepipe.capture \
    libmtkcam.featurepipe.depthmap \
    libmtkcam.featurepipe.streaming \
    libmtkcam.featurepipe.vsdof_util \
    libmtkcam.logicalmodule \
    libmtkcam_3rdparty.core \
    libmtkcam_3rdparty.customer \
    libmtkcam_3rdparty.mtk \
    libmtkcam_3rdparty \
    libmtkcam_calibration_convertor \
    libmtkcam_calibration_provider \
    libmtkcam_debugutils \
    libmtkcam_device3_app \
    libmtkcam_device3_hal \
    libmtkcam_device3_hidl \
    libmtkcam_device3_hidlutils \
    libmtkcam_device3_utils \
    libmtkcam_devicesessionpolicy \
    libmtkcam_diputils \
    libmtkcam_exif \
    libmtkcam_fdvt \
    libmtkcam_featurepolicy \
    libmtkcam_featureutils \
    libmtkcam_fwkutils \
    libmtkcam_grallocutils \
    libmtkcam_hwnode \
    libmtkcam_hwutils \
    libmtkcam_imgbuf \
    libmtkcam_mapping_mgr \
    libmtkcam_metadata \
    libmtkcam_metastore \
    libmtkcam_mfb \
    libmtkcam_modulefactory_aaa \
    libmtkcam_modulefactory_custom \
    libmtkcam_modulefactory_drv \
    libmtkcam_modulefactory_utils \
    libmtkcam_modulehelper \
    libmtkcam_owe \
    libmtkcam_pipeline \
    libmtkcam_pipeline_fbm \
    libmtkcam_pipelinemodel \
    libmtkcam_pipelinemodel_adapter \
    libmtkcam_pipelinemodel_capture \
    libmtkcam_pipelinemodel_isp \
    libmtkcam_pipelinemodel_session \
    libmtkcam_pipelinemodel_utils \
    libmtkcam_pipelinemodel_zsl \
    libmtkcam_pipelinepolicy-aov \
    libmtkcam_pipelinepolicy-security \
    libmtkcam_pipelinepolicy-smvr \
    libmtkcam_pipelinepolicy \
    libmtkcam_pipelinepolicy_factory \
    libmtkcam_prerelease \
    libmtkcam_rsc \
    libmtkcam_scenariorecorder \
    libmtkcam_stdutils \
    libmtkcam_streamutils \
    libmtkcam_synchelper \
    libmtkcam_sysutils \
    libmtkcam_tuning_utils \
    libmtkcam_ulog \
    libmtkconfig \
    libmtkconfigutils \
    libmtknetcap \
    libmtkrilutils \
    libmtktinyxml \
    libmtkutils \
    libtrm \
    libmtkcam_streaminfo_plugin-p1stt \
    ov02b10_mipi_raw21015_IdxMgr \
    ov02b10_mipi_raw_20615_IdxMgr \
    ov02b10_mipi_raw_20619_IdxMgr \
    ov02b10_mipi_raw_21061_IdxMgr \
    ov02b10_mipi_raw_IdxMgr \
    ov50a_mipi_raw_21127_IdxMgr \
    ov64b_mipi_raw_20619_IdxMgr \
    ov64b_mipi_raw_21061_IdxMgr \
    ov64b_mipi_raw_21651_IdxMgr \
    ov64b_mipi_raw_IdxMgr \
    vendor.mediatek.hardware.camera.ccap@1.0 \
    vendor.mediatek.hardware.camera.frhandler@1.0 \
    vendor.mediatek.hardware.camera.isphal@1.1 \
    vendor.mediatek.hardware.camera.lomoeffect@1.0 \
    vendor.mediatek.hardware.camera.postproc@1.0 \
    vendor.mediatek.hardware.camera.security@1.0 \
    vendor.mediatek.hardware.power@2.0 \
    vendor.trustonic.tee.tui@1.0 \
    vendor.trustonic.tee@1.0 \
    vendor.trustonic.tee@1.1 \
    liblvimfs \
    liblvimfs_wrapper \
    android.hardware.graphics.allocator@2.0_odm \
    android.hardware.graphics.allocator@3.0_odm \
    android.hardware.graphics.allocator@4.0_odm \
    hi846_mipi_raw_20615_tuning \
    imx471_mipi_raw_20615_tuning \
    imx682_mipi_raw_20615_tuning \
    libBokehPre \
    libCOppLceTonemapAPI \
    libCamera_hi846mipiraw20615_Capture_Preview \
    libCamera_hi846mipiraw20615_Capture_Preview_Zoom_2x \
    libCamera_hi846mipiraw20615_FaceBeauty_Capture \
    libCamera_hi846mipiraw20615_FaceBeauty_Preview \
    libCamera_hi846mipiraw20615_Face_Capture \
    libCamera_hi846mipiraw20615_Face_SuperNight_Hand_Capture \
    libCamera_hi846mipiraw20615_Face_SuperNight_Hand_Capture_Zoom \
    libCamera_hi846mipiraw20615_Face_Zoom_Capture \
    libCamera_hi846mipiraw20615_LHDR_Face_Capture \
    libCamera_hi846mipiraw20615_LHDR_Scene_Capture \
    libCamera_hi846mipiraw20615_P1_YUV \
    libCamera_hi846mipiraw20615_Production_Capture \
    libCamera_hi846mipiraw20615_Professional_Preview_Zoom_4x \
    libCamera_hi846mipiraw20615_RHDR_Face_Capture \
    libCamera_hi846mipiraw20615_RHDR_Scene_Capture \
    libCamera_hi846mipiraw20615_Scene_Capture \
    libCamera_hi846mipiraw20615_SuperNight_Hand_Capture \
    libCamera_hi846mipiraw20615_SuperNight_Hand_Capture_Zoom \
    libCamera_hi846mipiraw20615_SuperNight_Hand_Preview \
    libCamera_hi846mipiraw20615_SuperNight_Tripod_Capture \
    libCamera_hi846mipiraw20615_SuperNight_Tripod_Capture_Zoom \
    libCamera_hi846mipiraw20615_Video_1080p_30fps \
    libCamera_hi846mipiraw20615_Video_1080p_30fps_zoom_2x \
    libCamera_hi846mipiraw20615_Video_1080p_60fps \
    libCamera_hi846mipiraw20615_Video_720p_30fps \
    libCamera_hi846mipiraw20615_Video_720p_30fps_zoom_2x \
    libCamera_hi846mipiraw20615_Video_Capture \
    libCamera_hi846mipiraw20615_Zoom_Capture \
    libCamera_hi846mipiraw20615_portrait_capture_full \
    libCamera_hi846mipiraw20615_portrait_capture_hdr \
    libCamera_hi846mipiraw20615_portrait_preview_full \
    libCamera_hi846mipiraw20615_zHDR_Face_Capture \
    libCamera_hi846mipiraw20615_zHDR_Scene_Capture \
    libCamera_imx471mipiraw20615_3HDR_Preivew \
    libCamera_imx471mipiraw20615_3rd_Capture_1080P \
    libCamera_imx471mipiraw20615_3rd_Capture_480P \
    libCamera_imx471mipiraw20615_3rd_Capture_720P \
    libCamera_imx471mipiraw20615_3rd_Preview_1080P \
    libCamera_imx471mipiraw20615_3rd_Preview_480P \
    libCamera_imx471mipiraw20615_3rd_Preview_720P \
    libCamera_imx471mipiraw20615_3rd_Preview_Other \
    libCamera_imx471mipiraw20615_AI_Video_60fps \
    libCamera_imx471mipiraw20615_FaceBeauty_Capture \
    libCamera_imx471mipiraw20615_FaceBeauty_Capture_Bining \
    libCamera_imx471mipiraw20615_FaceBeauty_Capture_NoFace \
    libCamera_imx471mipiraw20615_FaceBeauty_Scene_Preview \
    libCamera_imx471mipiraw20615_FaceUnlock \
    libCamera_imx471mipiraw20615_Face_Capture \
    libCamera_imx471mipiraw20615_Flash_Capture \
    libCamera_imx471mipiraw20615_ITS_1080P \
    libCamera_imx471mipiraw20615_Panorama \
    libCamera_imx471mipiraw20615_Production_Capture \
    libCamera_imx471mipiraw20615_Scene_Capture \
    libCamera_imx471mipiraw20615_Scene_Capture_Bining \
    libCamera_imx471mipiraw20615_Scene_Preview \
    libCamera_imx471mipiraw20615_SuperNight_Hand_Capture \
    libCamera_imx471mipiraw20615_SuperNight_Hand_Capture_Flash \
    libCamera_imx471mipiraw20615_SuperNight_Preview \
    libCamera_imx471mipiraw20615_Video_120fps \
    libCamera_imx471mipiraw20615_Video_Recording_1080P \
    libCamera_imx471mipiraw20615_Video_Recording_720P \
    libCamera_imx471mipiraw20615_WeChatQQ_Capture_1080P \
    libCamera_imx471mipiraw20615_WeChatQQ_Capture_480P \
    libCamera_imx471mipiraw20615_WeChatQQ_Capture_720P \
    libCamera_imx471mipiraw20615_WeChatQQ_Preview_1080P \
    libCamera_imx471mipiraw20615_WeChatQQ_Preview_480P \
    libCamera_imx471mipiraw20615_WeChatQQ_Preview_720P \
    libCamera_imx471mipiraw20615_YUVHDR_Capture_Binning \
    libCamera_imx471mipiraw20615_YUVHDR_FaceBeauty_Capture_Binning_NoFace \
    libCamera_imx471mipiraw20615_portrait_capture_Blurless \
    libCamera_imx471mipiraw20615_portrait_capture_Blurless_After \
    libCamera_imx471mipiraw20615_portrait_capture_full \
    libCamera_imx471mipiraw20615_portrait_capture_hdr \
    libCamera_imx471mipiraw20615_portrait_preview_full \
    libCamera_imx471mipiraw20615_zHDR_Capture_Binning \
    libCamera_imx682mipiraw20615_3rd_Capture_1080P \
    libCamera_imx682mipiraw20615_3rd_Capture_480P \
    libCamera_imx682mipiraw20615_3rd_Capture_720P \
    libCamera_imx682mipiraw20615_3rd_Preview_1080P \
    libCamera_imx682mipiraw20615_3rd_Preview_480P \
    libCamera_imx682mipiraw20615_3rd_Preview_720P \
    libCamera_imx682mipiraw20615_3rd_Video_1080P \
    libCamera_imx682mipiraw20615_AIHDR \
    libCamera_imx682mipiraw20615_AIHDR_Face \
    libCamera_imx682mipiraw20615_AIHDR_Face_Zoom \
    libCamera_imx682mipiraw20615_AIHDR_Zoom \
    libCamera_imx682mipiraw20615_AINR_Main_LLS \
    libCamera_imx682mipiraw20615_AINR_Main_LLS_Face \
    libCamera_imx682mipiraw20615_AIShutter \
    libCamera_imx682mipiraw20615_AIShutter_Face \
    libCamera_imx682mipiraw20615_Capture_Preview \
    libCamera_imx682mipiraw20615_Capture_Preview_64M \
    libCamera_imx682mipiraw20615_Capture_Preview_64M_2x \
    libCamera_imx682mipiraw20615_Capture_Preview_64M_4x \
    libCamera_imx682mipiraw20615_Capture_Preview_MF \
    libCamera_imx682mipiraw20615_Capture_Preview_PF \
    libCamera_imx682mipiraw20615_Capture_Preview_Zoom_2x \
    libCamera_imx682mipiraw20615_Capture_Preview_Zoom_4x \
    libCamera_imx682mipiraw20615_FaceBeauty_Capture \
    libCamera_imx682mipiraw20615_Face_Capture \
    libCamera_imx682mipiraw20615_Face_Capture_64M_MFNR \
    libCamera_imx682mipiraw20615_Face_Capture_64M_Single \
    libCamera_imx682mipiraw20615_Face_SuperNight_Hand_Capture \
    libCamera_imx682mipiraw20615_Face_SuperNight_Hand_Capture_Zoom \
    libCamera_imx682mipiraw20615_Face_SuperNight_Hand_Preview \
    libCamera_imx682mipiraw20615_Flash_Capture \
    libCamera_imx682mipiraw20615_HDR_Capture \
    libCamera_imx682mipiraw20615_LHDR_Face_Capture \
    libCamera_imx682mipiraw20615_LHDR_Scene_Capture \
    libCamera_imx682mipiraw20615_P1_YUV \
    libCamera_imx682mipiraw20615_Panorama_Capture \
    libCamera_imx682mipiraw20615_Production_Capture \
    libCamera_imx682mipiraw20615_Professional_Capture \
    libCamera_imx682mipiraw20615_Professional_Preview \
    libCamera_imx682mipiraw20615_RHDR_Face_Capture \
    libCamera_imx682mipiraw20615_RHDR_Scene_Capture \
    libCamera_imx682mipiraw20615_Scene_Capture \
    libCamera_imx682mipiraw20615_Scene_Capture_64M_MFNR \
    libCamera_imx682mipiraw20615_Scene_Capture_64M_Single \
    libCamera_imx682mipiraw20615_Scene_Capture_BlueSky \
    libCamera_imx682mipiraw20615_Scene_Capture_Food \
    libCamera_imx682mipiraw20615_Scene_Capture_Grass \
    libCamera_imx682mipiraw20615_Scene_Capture_Text \
    libCamera_imx682mipiraw20615_SuperNight_Hand_Capture \
    libCamera_imx682mipiraw20615_SuperNight_Hand_Capture_Zoom \
    libCamera_imx682mipiraw20615_SuperNight_Hand_Preview \
    libCamera_imx682mipiraw20615_SuperNight_Tripod_Capture \
    libCamera_imx682mipiraw20615_SuperNight_Tripod_Capture_Zoom \
    libCamera_imx682mipiraw20615_Video_1080p_120fps \
    libCamera_imx682mipiraw20615_Video_1080p_30fps \
    libCamera_imx682mipiraw20615_Video_1080p_30fps_zoom_2x \
    libCamera_imx682mipiraw20615_Video_1080p_30fps_zoom_4x \
    libCamera_imx682mipiraw20615_Video_1080p_60fps \
    libCamera_imx682mipiraw20615_Video_1080p_60fps_pro \
    libCamera_imx682mipiraw20615_Video_1080p_60fps_zoom_2x \
    libCamera_imx682mipiraw20615_Video_1080p_60fps_zoom_4x \
    libCamera_imx682mipiraw20615_Video_480p \
    libCamera_imx682mipiraw20615_Video_4k_30fps \
    libCamera_imx682mipiraw20615_Video_4k_30fps_zoom_2x \
    libCamera_imx682mipiraw20615_Video_4k_30fps_zoom_4x \
    libCamera_imx682mipiraw20615_Video_4k_60fps \
    libCamera_imx682mipiraw20615_Video_720p_240fps \
    libCamera_imx682mipiraw20615_Video_720p_30fps \
    libCamera_imx682mipiraw20615_Video_720p_30fps_zoom_2x \
    libCamera_imx682mipiraw20615_Video_720p_30fps_zoom_4x \
    libCamera_imx682mipiraw20615_Video_720p_60fps \
    libCamera_imx682mipiraw20615_Video_720p_60fps_zoom_2x \
    libCamera_imx682mipiraw20615_Video_720p_60fps_zoom_4x \
    libCamera_imx682mipiraw20615_Video_Capture \
    libCamera_imx682mipiraw20615_Video_Enhance_1080P \
    libCamera_imx682mipiraw20615_Video_Enhance_1080P_Face \
    libCamera_imx682mipiraw20615_Video_Enhance_1080P_Face_Reconfig \
    libCamera_imx682mipiraw20615_Video_Enhance_1080P_Reconfig \
    libCamera_imx682mipiraw20615_WeChatQQ_Capture \
    libCamera_imx682mipiraw20615_WeChatQQ_Preview \
    libCamera_imx682mipiraw20615_WeChatQQ_Video \
    libCamera_imx682mipiraw20615_WeChatQQ_VideoCall \
    libCamera_imx682mipiraw20615_Zoom_Capture \
    libCamera_imx682mipiraw20615_portrait_capture_Blurless \
    libCamera_imx682mipiraw20615_portrait_capture_Blurless_After \
    libCamera_imx682mipiraw20615_portrait_capture_full \
    libCamera_imx682mipiraw20615_portrait_capture_hdr \
    libCamera_imx682mipiraw20615_portrait_preview_full \
    libCamera_imx682mipiraw20615_zHDR_Face_Capture \
    libCamera_imx682mipiraw20615_zHDR_Scene_Capture \
    libCamera_ov02b10mipiraw20615_E2EHDR_Preview \
    libCamera_ov02b10mipiraw20615_E2EHDR_Video \
    libCamera_ov02b10mipiraw20615_Face_Capture \
    libCamera_ov02b10mipiraw20615_Flash_Capture \
    libCamera_ov02b10mipiraw20615_HDR_Capture \
    libCamera_ov02b10mipiraw20615_HDR_Preview \
    libCamera_ov02b10mipiraw20615_HDR_Video \
    libCamera_ov02b10mipiraw20615_N3D_Capture \
    libCamera_ov02b10mipiraw20615_N3D_Preview \
    libCamera_ov02b10mipiraw20615_N3D_Video \
    libCamera_ov02b10mipiraw20615_P1_YUV \
    libCamera_ov02b10mipiraw20615_Scene_Capture \
    libCamera_ov02b10mipiraw20615_Scene_Capture_4cell \
    libCamera_ov02b10mipiraw20615_Scene_Preview \
    libCamera_ov02b10mipiraw20615_Scene_Preview_4k \
    libCamera_ov02b10mipiraw20615_Scene_Preview_ZSD_Flash \
    libCamera_ov02b10mipiraw20615_Video_1080 \
    libCamera_ov02b10mipiraw20615_Video_4k \
    libDeVIS \
    libEIS \
    libFilterWrapper \
    libOGLManager \
    libRbsFlow \
    libRbsFlow_cap \
    libSuperTextWrapper \
    libVideoEnhance \
    libWaterMark \
    lib_rectify \
    libaiseg \
    libalCFR \
    libanc_np-loader \
    libapsexif \
    libapsjpeg \
    libapspng \
    libcamera_core_hwi_odm \
    libhyperlapse \
    libmindroid-app \
    libmindroid-framework \
    libml_util \
    libmotionblur \
    libmpbase \
    libnp-loader \
    libocam_common_odm \
    liboplus_platform_hwi_odm \
    libremosaic_wrapper \
    libremosaiclib \
    libtfavib \
    libtflite_mtk_static \
    libui_oplus \
    libwatermark_photo \
    ov02b10_mipi_raw_20615_tuning \
    vendor.oplus.hardware.cammidasservice@1.0_odm \
    manifest_oplus_cammidasservice.xml \
    camerahalserver \
    vendor.trustonic.tee@1.1-service \
    mcDriverDaemon \
    vendor.oplus.hardware.cammidasservice@1.0-service
