<?xml version="1.0" encoding="UTF-8" ?>
<root>
    <card name="mt6885mt6359" />
    <mixercontrol>
        <versioncontrol value="1.01">
        </versioncontrol>
        <!-- "kctl name" are the initial mixer settings -->
        <!-- <kctl name="XXXX" value="XXXX" /> -->
        <!--headphone output-->
        <path name="headphone_output" value="turnon">
            <kctl name="DAC In Mux" value="Normal Path" />
            <kctl name="HPL Mux" value="Audio Playback" />
            <kctl name="HPR Mux" value="Audio Playback" />
        </path>
        <path name="headphone_output" value="turnoff">
            <kctl name="HPL Mux" value="Open" />
            <kctl name="HPR Mux" value="Open" />
        </path>
        <path name="HP_IMPEDANCE" value="turnon">
            <kctl name="DAC In Mux" value="Normal Path" />
            <kctl name="HPL Mux" value="HP Impedance" />
        </path>
        <path name="HP_IMPEDANCE" value="turnoff">
            <kctl name="HPL Mux" value="Open" />
        </path>
        <!--receiver output-->
        <path name="receiver_output" value="turnon">
            <kctl name="DAC In Mux" value="Normal Path" />
            <kctl name="RCV Mux" value="Voice Playback" />
        </path>
        <path name="receiver_output" value="turnoff">
            <kctl name="RCV Mux" value="Open" />
        </path>
       <!-- 2-in-1 speaker output-->
        <path name="two_in_one_speaker_output" value="turnon">
        </path>
        <path name="two_in_one_speaker_output" value="turnoff">
        </path>
       <!--speaker output-->
        <path name="speaker_output" value="turnon">
            <kctl name="DAC In Mux" value="Normal Path" />
            <kctl name="LOL Mux" value="Playback" />
        </path>
        <path name="speaker_output" value="turnoff">
            <kctl name="LOL Mux" value="Open" />
        </path>
        <!--headhpone_speaker output-->
        <path name="headphoneSpeaker_output" value="turnon">
            <kctl name="DAC In Mux" value="Normal Path" />
            <kctl name="HPL Mux" value="Audio Playback" />
            <kctl name="HPR Mux" value="Audio Playback" />
            <kctl name="LOL Mux" value="Playback" />
        </path>
        <path name="headphoneSpeaker_output" value="turnoff">
            <kctl name="HPL Mux" value="Open" />
            <kctl name="HPR Mux" value="Open" />
            <kctl name="LOL Mux" value="Open" />
        </path>
        <!--external_speaker output-->
        <path name="ext_speaker_output" value="turnon">
            <kctl name="Ext_Speaker_Amp Switch" value="1" />
        </path>
        <path name="ext_speaker_output" value="turnoff">
            <kctl name="Ext_Speaker_Amp Switch" value="0" />
        </path>
        <!--mic setting-->
        <path name="builtin_Mic_Mic1" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="MISO1_MUX" value="UL1_CH1" />
            <kctl name="ADC_L_Mux" value="Left Preamplifier" />
            <kctl name="PGA_L_Mux" value="AIN0" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA0" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA0" />
        </path>
        <path name="builtin_Mic_Mic1" value="turnoff">
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_DMic1" value="turnon">
            <kctl name="MTKAIF_ADDA6_ONLY" value="On" />
            <kctl name="MISO2_MUX" value="UL2_CH1" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA0" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA1_L_1" />
            <kctl name="DMIC2_MUX" value="DMIC_DATA1_R" />
        </path>
        <path name="builtin_Mic_DMic1" value="turnoff">
            <kctl name="MTKAIF_ADDA6_ONLY" value="Off" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_Mic1_Inverse" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH2" />
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="ADC_R_Mux" value="Right Preamplifier" />
            <kctl name="PGA_R_Mux" value="AIN2" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA1_L_1" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA1_L_1" />
        </path>
        <path name="builtin_Mic_Mic1_Inverse" value="turnoff">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="ADC_R_Mux" value="Idle" />
            <kctl name="PGA_R_Mux" value="None" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_Mic2" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH2" />
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="ADC_R_Mux" value="Right Preamplifier" />
            <kctl name="PGA_R_Mux" value="AIN3" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA1_R" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA1_R" />
        </path>
        <path name="builtin_Mic_Mic2" value="turnoff">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="ADC_R_Mux" value="Idle" />
            <kctl name="PGA_R_Mux" value="None" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_DMic2" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="MISO1_MUX" value="UL1_CH1" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA0" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA1_L_1" />
            <kctl name="DMIC2_MUX" value="DMIC_DATA1_R" />
        </path>
        <path name="builtin_Mic_DMic2" value="turnoff">
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_Mic2_Inverse" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="MISO1_MUX" value="UL1_CH1" />
            <kctl name="ADC_L_Mux" value="Left Preamplifier" />
            <kctl name="PGA_L_Mux" value="AIN0" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA0" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA0" />
        </path>
        <path name="builtin_Mic_Mic2_Inverse" value="turnoff">
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_Mic3" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH2" />
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="ADC_R_Mux" value="Right Preamplifier" />
            <kctl name="PGA_R_Mux" value="AIN2" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA1_L_1" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA1_L_1" />
        </path>
        <path name="builtin_Mic_Mic3" value="turnoff">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="ADC_R_Mux" value="Idle" />
            <kctl name="PGA_R_Mux" value="None" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_DMic3" value="turnon">
            <kctl name="MTKAIF_ADDA6_ONLY" value="On" />
            <kctl name="MISO2_MUX" value="UL2_CH2" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA0" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA1_L_1" />
            <kctl name="DMIC2_MUX" value="DMIC_DATA1_R" />
        </path>
        <path name="builtin_Mic_DMic3" value="turnoff">
            <kctl name="MTKAIF_ADDA6_ONLY" value="Off" />
            <kctl name="MISO2_MUX" value="UL2_CH1" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_Mic3_Inverse" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH2" />
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="ADC_R_Mux" value="Right Preamplifier" />
            <kctl name="PGA_R_Mux" value="AIN3" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA1_R" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA1_R" />
        </path>
        <path name="builtin_Mic_Mic3_Inverse" value="turnoff">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="ADC_R_Mux" value="Idle" />
            <kctl name="PGA_R_Mux" value="None" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_SingleMic" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="MISO1_MUX" value="UL1_CH1" />
            <kctl name="ADC_L_Mux" value="Left Preamplifier" />
            <kctl name="PGA_L_Mux" value="AIN0" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA0" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA0" />
        </path>
        <path name="builtin_Mic_SingleMic" value="turnoff">
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_DualMic" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="ADC_L_Mux" value="Left Preamplifier" />
            <kctl name="PGA_L_Mux" value="AIN0" />
            <kctl name="ADC_R_Mux" value="Right Preamplifier" />
            <kctl name="PGA_R_Mux" value="AIN3" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA0" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA1_L_1" />
        </path>
        <path name="builtin_Mic_DualMic" value="turnoff">
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="ADC_R_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
            <kctl name="PGA_R_Mux" value="None" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_HDR_DualMic" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="MISO2_MUX" value="UL2_CH1" />
            <kctl name="ADC_L_Mux" value="Left Preamplifier" />
            <kctl name="PGA_L_Mux" value="AIN0" />
            <kctl name="ADC_R_Mux" value="Right Preamplifier" />
            <kctl name="PGA_R_Mux" value="AIN0" />
            <kctl name="ADC_3_Mux" value="Preamplifier" />
            <kctl name="PGA_3_Mux" value="AIN3" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA0" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA1_L_1" />
            <kctl name="DMIC2_MUX" value="DMIC_DATA1_R" />
        </path>
        <path name="builtin_Mic_HDR_DualMic" value="turnoff">
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="ADC_R_Mux" value="Idle" />
            <kctl name="ADC_3_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
            <kctl name="PGA_R_Mux" value="None" />
            <kctl name="PGA_3_Mux" value="None" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_DualMic_Inverse" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH2" />
            <kctl name="MISO1_MUX" value="UL1_CH1" />
            <kctl name="ADC_L_Mux" value="Left Preamplifier" />
            <kctl name="PGA_L_Mux" value="AIN0" />
            <kctl name="ADC_R_Mux" value="Right Preamplifier" />
            <kctl name="PGA_R_Mux" value="AIN3" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA1_L_1" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA0" />
        </path>
        <path name="builtin_Mic_DualMic_Inverse" value="turnoff">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="ADC_R_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
            <kctl name="PGA_R_Mux" value="None" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_ThreeMic" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="MISO2_MUX" value="UL2_CH1" />
            <kctl name="ADC_L_Mux" value="Left Preamplifier" />
            <kctl name="PGA_L_Mux" value="AIN0" />
            <kctl name="ADC_R_Mux" value="Right Preamplifier" />
            <kctl name="PGA_R_Mux" value="AIN2" />
            <kctl name="ADC_3_Mux" value="Preamplifier" />
            <kctl name="PGA_3_Mux" value="AIN3" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA0" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA1_L_1" />
            <kctl name="DMIC2_MUX" value="DMIC_DATA1_R" />
        </path>
        <path name="builtin_Mic_ThreeMic" value="turnoff">
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="ADC_R_Mux" value="Idle" />
            <kctl name="ADC_3_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
            <kctl name="PGA_R_Mux" value="None" />
            <kctl name="PGA_3_Mux" value="None" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_ThreeMic_Inverse" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH2" />
            <kctl name="MISO1_MUX" value="UL1_CH1" />
            <kctl name="MISO2_MUX" value="UL2_CH1" />
            <kctl name="ADC_L_Mux" value="Left Preamplifier" />
            <kctl name="PGA_L_Mux" value="AIN0" />
            <kctl name="ADC_R_Mux" value="Right Preamplifier" />
            <kctl name="PGA_R_Mux" value="AIN2" />
            <kctl name="ADC_3_Mux" value="Preamplifier" />
            <kctl name="PGA_3_Mux" value="AIN3" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA1_L_1" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA0" />
            <kctl name="DMIC2_MUX" value="DMIC_DATA1_R" />
        </path>
        <path name="builtin_Mic_ThreeMic_Inverse" value="turnoff">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="ADC_R_Mux" value="Idle" />
            <kctl name="ADC_3_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
            <kctl name="PGA_R_Mux" value="None" />
            <kctl name="PGA_3_Mux" value="None" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_BackMic" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH2" />
            <kctl name="MISO1_MUX" value="UL1_CH1" />
            <kctl name="ADC_L_Mux" value="Left Preamplifier" />
            <kctl name="PGA_L_Mux" value="AIN0" />
            <kctl name="ADC_R_Mux" value="Right Preamplifier" />
            <kctl name="PGA_R_Mux" value="AIN2" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA1_L_1" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA0" />
        </path>
        <path name="builtin_Mic_BackMic" value="turnoff">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="ADC_R_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
            <kctl name="PGA_R_Mux" value="None" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_HDR_BackMic" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="MISO2_MUX" value="UL2_CH1" />
            <kctl name="ADC_L_Mux" value="Left Preamplifier" />
            <kctl name="PGA_L_Mux" value="AIN0" />
            <kctl name="ADC_R_Mux" value="Right Preamplifier" />
            <kctl name="PGA_R_Mux" value="AIN0" />
            <kctl name="ADC_3_Mux" value="Preamplifier" />
            <kctl name="PGA_3_Mux" value="AIN3" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA0" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA1_L_1" />
            <kctl name="DMIC2_MUX" value="DMIC_DATA1_R" />
        </path>
        <path name="builtin_Mic_HDR_BackMic" value="turnoff">
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="ADC_R_Mux" value="Idle" />
            <kctl name="ADC_3_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
            <kctl name="PGA_R_Mux" value="None" />
            <kctl name="PGA_3_Mux" value="None" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_BackMic_Inverse" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="ADC_L_Mux" value="Left Preamplifier" />
            <kctl name="PGA_L_Mux" value="AIN0" />
            <kctl name="ADC_R_Mux" value="Right Preamplifier" />
            <kctl name="PGA_R_Mux" value="AIN2" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA0" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA1_L_1" />
        </path>
        <path name="builtin_Mic_BackMic_Inverse" value="turnoff">
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="ADC_R_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
            <kctl name="PGA_R_Mux" value="None" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_BackMic_3" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH2" />
            <kctl name="MISO1_MUX" value="UL1_CH1" />
            <kctl name="MISO2_MUX" value="UL2_CH1" />
            <kctl name="ADC_L_Mux" value="Left Preamplifier" />
            <kctl name="PGA_L_Mux" value="AIN0" />
            <kctl name="ADC_R_Mux" value="Right Preamplifier" />
            <kctl name="PGA_R_Mux" value="AIN2" />
            <kctl name="ADC_3_Mux" value="Preamplifier" />
            <kctl name="PGA_3_Mux" value="AIN3" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA1_L_1" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA0" />
            <kctl name="DMIC2_MUX" value="DMIC_DATA1_R" />
        </path>
        <path name="builtin_Mic_BackMic_3" value="turnoff">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="ADC_R_Mux" value="Idle" />
            <kctl name="ADC_3_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
            <kctl name="PGA_R_Mux" value="None" />
            <kctl name="PGA_3_Mux" value="None" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_BackMic_3_Inverse" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="MISO2_MUX" value="UL2_CH1" />
            <kctl name="ADC_L_Mux" value="Left Preamplifier" />
            <kctl name="PGA_L_Mux" value="AIN0" />
            <kctl name="ADC_R_Mux" value="Right Preamplifier" />
            <kctl name="PGA_R_Mux" value="AIN2" />
            <kctl name="ADC_3_Mux" value="Preamplifier" />
            <kctl name="PGA_3_Mux" value="AIN3" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA0" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA1_L_1" />
            <kctl name="DMIC2_MUX" value="DMIC_DATA1_R" />
        </path>
        <path name="builtin_Mic_BackMic_3_Inverse" value="turnoff">
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="ADC_R_Mux" value="Idle" />
            <kctl name="ADC_3_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
            <kctl name="PGA_R_Mux" value="None" />
            <kctl name="PGA_3_Mux" value="None" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="headset_mic_input" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="MISO1_MUX" value="UL1_CH1" />
            <kctl name="ADC_L_Mux" value="Left Preamplifier" />
            <kctl name="PGA_L_Mux" value="AIN1" />
        </path>
        <path name="headset_mic_input" value="turnoff">
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
        </path>
        <path name="headset_mic_input_HDR" value="turnon">
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="MISO1_MUX" value="UL1_CH1" />
            <kctl name="ADC_L_Mux" value="Left Preamplifier" />
            <kctl name="PGA_L_Mux" value="AIN1" />
        </path>
        <path name="headset_mic_input_HDR" value="turnoff">
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
        </path>
        <path name="builtin_Mic_Vow_Mic" value="turnon">
            <kctl name="VOW_AMIC0_MUX" value="ADC_L" />
            <kctl name="ADC_L_Mux" value="Left Preamplifier" />
            <kctl name="PGA_L_Mux" value="AIN0" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA0" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA1_L_1" />
        </path>
        <path name="builtin_Mic_Vow_Mic" value="turnoff">
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
            <kctl name="VOW_UL_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_Vow_Mic2" value="turnon">
            <kctl name="VOW_AMIC0_MUX" value="ADC_R" />
            <kctl name="ADC_R_Mux" value="Right Preamplifier" />
            <kctl name="PGA_R_Mux" value="AIN3" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA0" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA1_L_1" />
        </path>
        <path name="builtin_Mic_Vow_Mic2" value="turnoff">
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
            <kctl name="ADC_R_Mux" value="Idle" />
            <kctl name="PGA_R_Mux" value="None" />
            <kctl name="VOW_UL_SRC_MUX" value="AMIC" />
        </path>
        <path name="builtin_Mic_Vow_DualMic" value="turnon">
            <kctl name="VOW_AMIC0_MUX" value="ADC_L" />
            <kctl name="VOW_AMIC1_MUX" value="ADC_R" />
            <kctl name="ADC_L_Mux" value="Left Preamplifier" />
            <kctl name="PGA_L_Mux" value="AIN0" />
            <kctl name="ADC_R_Mux" value="Right Preamplifier" />
            <kctl name="PGA_R_Mux" value="AIN3" />
            <kctl name="DMIC0_MUX" value="DMIC_DATA0" />
            <kctl name="DMIC1_MUX" value="DMIC_DATA1_L_1" />
        </path>
        <path name="builtin_Mic_Vow_DualMic" value="turnoff">
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="ADC_R_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
            <kctl name="PGA_R_Mux" value="None" />
            <kctl name="VOW_UL_SRC_MUX" value="AMIC" />
        </path>
        <path name="headset_vow_input" value="turnon">
            <kctl name="VOW_AMIC0_MUX" value="ADC_L" />
            <kctl name="ADC_L_Mux" value="Left Preamplifier" />
            <kctl name="PGA_L_Mux" value="AIN1" />
        </path>
        <path name="headset_vow_input" value="turnoff">
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
        </path>
        <!--mic1 type setting-->
        <path name="MicTypeVOWDMICMode" value="setting">
            <kctl name="VOW_UL_SRC_MUX" value="DMIC" />
        </path>
        <path name="MicTypeDMICMode" value="setting">
            <kctl name="UL_SRC_MUX" value="DMIC" />
            <kctl name="UL2_SRC_MUX" value="DMIC" />
            <kctl name="MTKAIF_DMIC" value="On" />
        </path>
        <path name="sidetone_switch" value="turnon">
            <kctl name="Sidetone Filter Switch" value="1" />
        </path>
        <path name="sidetone_switch" value="turnoff">
            <kctl name="Sidetone Filter Switch" value="0" />
        </path>
        <!-- i2s hd control -->
        <path name="I2S0_HD_ON" value="setting">
            <kctl name="I2S0_HD_Mux" value="Low_Jitter" />
        </path>
        <path name="I2S0_HD_OFF" value="setting">
            <kctl name="I2S0_HD_Mux" value="Normal" />
        </path>
        <path name="I2S1_HD_ON" value="setting">
            <kctl name="I2S1_HD_Mux" value="Low_Jitter" />
        </path>
        <path name="I2S1_HD_OFF" value="setting">
            <kctl name="I2S1_HD_Mux" value="Normal" />
        </path>
        <path name="I2S2_HD_ON" value="setting">
            <kctl name="I2S2_HD_Mux" value="Low_Jitter" />
        </path>
        <path name="I2S2_HD_OFF" value="setting">
            <kctl name="I2S2_HD_Mux" value="Normal" />
        </path>
        <path name="I2S3_HD_ON" value="setting">
            <kctl name="I2S3_HD_Mux" value="Low_Jitter" />
        </path>
        <path name="I2S3_HD_OFF" value="setting">
            <kctl name="I2S3_HD_Mux" value="Normal" />
        </path>
        <path name="I2S5_HD_ON" value="setting">
            <kctl name="I2S5_HD_Mux" value="Low_Jitter" />
        </path>
        <path name="I2S5_HD_OFF" value="setting">
            <kctl name="I2S5_HD_Mux" value="Normal" />
        </path>
       <!--AP side control-->
        <path name="PLAYBACK1_TO_ADDA_DL" value="turnon">
            <kctl name="ADDA_DL_CH1 DL1_CH1" value="1" />
            <kctl name="ADDA_DL_CH2 DL1_CH2" value="1" />
            <kctl name="ADDA_DL_CH3 DL1_CH1" value="1" />
            <kctl name="ADDA_DL_CH4 DL1_CH2" value="1" />
        </path>
        <path name="PLAYBACK1_TO_ADDA_DL" value="turnoff">
            <kctl name="ADDA_DL_CH1 DL1_CH1" value="0" />
            <kctl name="ADDA_DL_CH2 DL1_CH2" value="0" />
            <kctl name="ADDA_DL_CH3 DL1_CH1" value="0" />
            <kctl name="ADDA_DL_CH4 DL1_CH2" value="0" />
        </path>
        <path name="PLAYBACK1_TO_I2S1" value="turnon">
            <kctl name="I2S1_CH1 DL1_CH1" value="1" />
            <kctl name="I2S1_CH2 DL1_CH2" value="1" />
        </path>
        <path name="PLAYBACK1_TO_I2S1" value="turnoff">
            <kctl name="I2S1_CH1 DL1_CH1" value="0" />
            <kctl name="I2S1_CH2 DL1_CH2" value="0" />
        </path>
        <path name="PLAYBACK1_TO_I2S3" value="turnon">
            <kctl name="I2S3_CH1 DL1_CH1" value="1" />
            <kctl name="I2S3_CH2 DL1_CH2" value="1" />
        </path>
        <path name="PLAYBACK1_TO_I2S3" value="turnoff">
            <kctl name="I2S3_CH1 DL1_CH1" value="0" />
            <kctl name="I2S3_CH2 DL1_CH2" value="0" />
        </path>
        <path name="PLAYBACK1_TO_I2S5" value="turnon">
            <kctl name="I2S5_CH1 DL1_CH1" value="1" />
            <kctl name="I2S5_CH2 DL1_CH2" value="1" />
        </path>
        <path name="PLAYBACK1_TO_I2S5" value="turnoff">
            <kctl name="I2S5_CH1 DL1_CH1" value="0" />
            <kctl name="I2S5_CH2 DL1_CH2" value="0" />
        </path>
        <path name="PLAYBACK2_TO_ADDA_DL" value="turnon">
            <kctl name="ADDA_DL_CH1 DL2_CH1" value="1" />
            <kctl name="ADDA_DL_CH2 DL2_CH2" value="1" />
            <kctl name="ADDA_DL_CH3 DL2_CH1" value="1" />
            <kctl name="ADDA_DL_CH4 DL2_CH2" value="1" />
        </path>
        <path name="PLAYBACK2_TO_ADDA_DL" value="turnoff">
            <kctl name="ADDA_DL_CH1 DL2_CH1" value="0" />
            <kctl name="ADDA_DL_CH2 DL2_CH2" value="0" />
            <kctl name="ADDA_DL_CH3 DL2_CH1" value="0" />
            <kctl name="ADDA_DL_CH4 DL2_CH2" value="0" />
        </path>
        <path name="PLAYBACK2_TO_I2S1" value="turnon">
            <kctl name="I2S1_CH1 DL2_CH1" value="1" />
            <kctl name="I2S1_CH2 DL2_CH2" value="1" />
        </path>
        <path name="PLAYBACK2_TO_I2S1" value="turnoff">
            <kctl name="I2S1_CH1 DL2_CH1" value="0" />
            <kctl name="I2S1_CH2 DL2_CH2" value="0" />
        </path>
        <path name="PLAYBACK2_TO_I2S3" value="turnon">
            <kctl name="I2S3_CH1 DL2_CH1" value="1" />
            <kctl name="I2S3_CH2 DL2_CH2" value="1" />
        </path>
        <path name="PLAYBACK2_TO_I2S3" value="turnoff">
            <kctl name="I2S3_CH1 DL2_CH1" value="0" />
            <kctl name="I2S3_CH2 DL2_CH2" value="0" />
        </path>
        <path name="PLAYBACK2_TO_I2S5" value="turnon">
            <kctl name="I2S5_CH1 DL2_CH1" value="1" />
            <kctl name="I2S5_CH2 DL2_CH2" value="1" />
        </path>
        <path name="PLAYBACK2_TO_I2S5" value="turnoff">
            <kctl name="I2S5_CH1 DL2_CH1" value="0" />
            <kctl name="I2S5_CH2 DL2_CH2" value="0" />
        </path>
        <path name="PLAYBACK12_TO_ADDA_DL" value="turnon">
            <kctl name="ADDA_DL_CH1 DL12_CH1" value="1" />
            <kctl name="ADDA_DL_CH2 DL12_CH2" value="1" />
            <kctl name="ADDA_DL_CH3 DL12_CH1" value="1" />
            <kctl name="ADDA_DL_CH4 DL12_CH2" value="1" />
        </path>
        <path name="PLAYBACK12_TO_ADDA_DL" value="turnoff">
            <kctl name="ADDA_DL_CH1 DL12_CH1" value="0" />
            <kctl name="ADDA_DL_CH2 DL12_CH2" value="0" />
            <kctl name="ADDA_DL_CH3 DL12_CH1" value="0" />
            <kctl name="ADDA_DL_CH4 DL12_CH2" value="0" />
        </path>
        <path name="PLAYBACK12_TO_I2S1" value="turnon">
            <kctl name="I2S1_CH1 DL12_CH1" value="1" />
            <kctl name="I2S1_CH2 DL12_CH2" value="1" />
        </path>
        <path name="PLAYBACK12_TO_I2S1" value="turnoff">
            <kctl name="I2S1_CH1 DL12_CH1" value="0" />
            <kctl name="I2S1_CH2 DL12_CH2" value="0" />
        </path>
        <path name="PLAYBACK12_TO_I2S3" value="turnon">
            <kctl name="I2S3_CH1 DL12_CH1" value="1" />
            <kctl name="I2S3_CH2 DL12_CH2" value="1" />
        </path>
        <path name="PLAYBACK12_TO_I2S3" value="turnoff">
            <kctl name="I2S3_CH1 DL12_CH1" value="0" />
            <kctl name="I2S3_CH2 DL12_CH2" value="0" />
        </path>
        <path name="PLAYBACK12_TO_I2S5" value="turnon">
            <kctl name="I2S5_CH1 DL12_CH1" value="1" />
            <kctl name="I2S5_CH2 DL12_CH2" value="1" />
        </path>
        <path name="PLAYBACK12_TO_I2S5" value="turnoff">
            <kctl name="I2S5_CH1 DL12_CH1" value="0" />
            <kctl name="I2S5_CH2 DL12_CH2" value="0" />
        </path>
        <path name="PLAYBACK6_TO_ADDA_DL" value="turnon">
            <kctl name="ADDA_DL_CH1 DL6_CH1" value="1" />
            <kctl name="ADDA_DL_CH2 DL6_CH2" value="1" />
            <kctl name="ADDA_DL_CH3 DL6_CH1" value="1" />
            <kctl name="ADDA_DL_CH4 DL6_CH2" value="1" />
        </path>
        <path name="PLAYBACK6_TO_ADDA_DL" value="turnoff">
            <kctl name="ADDA_DL_CH1 DL6_CH1" value="0" />
            <kctl name="ADDA_DL_CH2 DL6_CH2" value="0" />
            <kctl name="ADDA_DL_CH3 DL6_CH1" value="0" />
            <kctl name="ADDA_DL_CH4 DL6_CH2" value="0" />
        </path>
        <path name="PLAYBACK6_TO_I2S1" value="turnon">
            <kctl name="I2S1_CH1 DL6_CH1" value="1" />
            <kctl name="I2S1_CH2 DL6_CH2" value="1" />
        </path>
        <path name="PLAYBACK6_TO_I2S1" value="turnoff">
            <kctl name="I2S1_CH1 DL6_CH1" value="0" />
            <kctl name="I2S1_CH2 DL6_CH2" value="0" />
        </path>
        <path name="PLAYBACK6_TO_I2S3" value="turnon">
            <kctl name="I2S3_CH1 DL6_CH1" value="1" />
            <kctl name="I2S3_CH2 DL6_CH2" value="1" />
        </path>
        <path name="PLAYBACK6_TO_I2S3" value="turnoff">
            <kctl name="I2S3_CH1 DL6_CH1" value="0" />
            <kctl name="I2S3_CH2 DL6_CH2" value="0" />
        </path>
        <path name="PLAYBACK6_TO_I2S5" value="turnon">
            <kctl name="I2S5_CH1 DL6_CH1" value="1" />
            <kctl name="I2S5_CH2 DL6_CH2" value="1" />
        </path>
        <path name="PLAYBACK6_TO_I2S5" value="turnoff">
            <kctl name="I2S5_CH1 DL6_CH1" value="0" />
            <kctl name="I2S5_CH2 DL6_CH2" value="0" />
        </path>
        <path name="PLAYBACK3_TO_ADDA_DL" value="turnon">
            <kctl name="ADDA_DL_CH1 DL3_CH1" value="1" />
            <kctl name="ADDA_DL_CH2 DL3_CH2" value="1" />
            <kctl name="ADDA_DL_CH3 DL3_CH1" value="1" />
            <kctl name="ADDA_DL_CH4 DL3_CH2" value="1" />
        </path>
        <path name="PLAYBACK3_TO_ADDA_DL" value="turnoff">
            <kctl name="ADDA_DL_CH1 DL3_CH1" value="0" />
            <kctl name="ADDA_DL_CH2 DL3_CH2" value="0" />
            <kctl name="ADDA_DL_CH3 DL3_CH1" value="0" />
            <kctl name="ADDA_DL_CH4 DL3_CH2" value="0" />
        </path>
        <path name="PLAYBACK3_TO_I2S1" value="turnon">
            <kctl name="I2S1_CH1 DL3_CH1" value="1" />
            <kctl name="I2S1_CH2 DL3_CH2" value="1" />
        </path>
        <path name="PLAYBACK3_TO_I2S1" value="turnoff">
            <kctl name="I2S1_CH1 DL3_CH1" value="0" />
            <kctl name="I2S1_CH2 DL3_CH2" value="0" />
        </path>
        <path name="PLAYBACK3_TO_I2S3" value="turnon">
            <kctl name="I2S3_CH1 DL3_CH1" value="1" />
            <kctl name="I2S3_CH2 DL3_CH2" value="1" />
        </path>
        <path name="PLAYBACK3_TO_I2S3" value="turnoff">
            <kctl name="I2S3_CH1 DL3_CH1" value="0" />
            <kctl name="I2S3_CH2 DL3_CH2" value="0" />
        </path>
        <path name="PLAYBACK3_TO_I2S5" value="turnon">
            <kctl name="I2S5_CH1 DL3_CH1" value="1" />
            <kctl name="I2S5_CH2 DL3_CH2" value="1" />
        </path>
        <path name="PLAYBACK3_TO_I2S5" value="turnoff">
            <kctl name="I2S5_CH1 DL3_CH1" value="0" />
            <kctl name="I2S5_CH2 DL3_CH2" value="0" />
        </path>
        <path name="PLAYBACK4_TO_ADDA_DL" value="turnon">
            <kctl name="ADDA_DL_CH1 DL4_CH1" value="1" />
            <kctl name="ADDA_DL_CH2 DL4_CH2" value="1" />
            <kctl name="ADDA_DL_CH3 DL4_CH1" value="1" />
            <kctl name="ADDA_DL_CH4 DL4_CH2" value="1" />
        </path>
        <path name="PLAYBACK4_TO_ADDA_DL" value="turnoff">
            <kctl name="ADDA_DL_CH1 DL4_CH1" value="0" />
            <kctl name="ADDA_DL_CH2 DL4_CH2" value="0" />
            <kctl name="ADDA_DL_CH3 DL4_CH1" value="0" />
            <kctl name="ADDA_DL_CH4 DL4_CH2" value="0" />
        </path>
        <path name="PLAYBACK4_TO_I2S1" value="turnon">
            <kctl name="I2S1_CH1 DL4_CH1" value="1" />
            <kctl name="I2S1_CH2 DL4_CH2" value="1" />
        </path>
        <path name="PLAYBACK4_TO_I2S1" value="turnoff">
            <kctl name="I2S1_CH1 DL4_CH1" value="0" />
            <kctl name="I2S1_CH2 DL4_CH2" value="0" />
        </path>
        <path name="PLAYBACK4_TO_I2S3" value="turnon">
            <kctl name="I2S3_CH1 DL4_CH1" value="1" />
            <kctl name="I2S3_CH2 DL4_CH2" value="1" />
        </path>
        <path name="PLAYBACK4_TO_I2S3" value="turnoff">
            <kctl name="I2S3_CH1 DL4_CH1" value="0" />
            <kctl name="I2S3_CH2 DL4_CH2" value="0" />
        </path>
        <path name="PLAYBACK4_TO_I2S5" value="turnon">
            <kctl name="I2S5_CH1 DL4_CH1" value="1" />
            <kctl name="I2S5_CH2 DL4_CH2" value="1" />
        </path>
        <path name="PLAYBACK4_TO_I2S5" value="turnoff">
            <kctl name="I2S5_CH1 DL4_CH1" value="0" />
            <kctl name="I2S5_CH2 DL4_CH2" value="0" />
        </path>
        <path name="PLAYBACK5_TO_ADDA_DL" value="turnon">
            <kctl name="ADDA_DL_CH1 DL5_CH1" value="1" />
            <kctl name="ADDA_DL_CH2 DL5_CH2" value="1" />
        </path>
        <path name="PLAYBACK5_TO_ADDA_DL" value="turnoff">
            <kctl name="ADDA_DL_CH1 DL5_CH1" value="0" />
            <kctl name="ADDA_DL_CH2 DL5_CH2" value="0" />
        </path>
        <path name="PLAYBACK5_TO_I2S3" value="turnon">
            <kctl name="I2S3_CH1 DL5_CH1" value="1" />
            <kctl name="I2S3_CH2 DL5_CH2" value="1" />
        </path>
        <path name="PLAYBACK5_TO_I2S3" value="turnoff">
            <kctl name="I2S3_CH1 DL5_CH1" value="0" />
            <kctl name="I2S3_CH2 DL5_CH2" value="0" />
        </path>
        <path name="ADDA_TO_HW_GAIN2" value="turnon">
            <kctl name="HW_GAIN2_IN_CH1 ADDA_UL_CH1" value="1" />
            <kctl name="HW_GAIN2_IN_CH2 ADDA_UL_CH2" value="1" />
        </path>
        <path name="ADDA_TO_HW_GAIN2" value="turnoff">
            <kctl name="HW_GAIN2_IN_CH1 ADDA_UL_CH1" value="0" />
            <kctl name="HW_GAIN2_IN_CH2 ADDA_UL_CH2" value="0" />
        </path>
        <path name="HW_GAIN2_TO_CAPTURE7" value="turnon">
            <kctl name="UL7_CH1 HW_GAIN2_OUT_CH1" value="1" />
            <kctl name="UL7_CH2 HW_GAIN2_OUT_CH2" value="1" />
        </path>
        <path name="HW_GAIN2_TO_CAPTURE7" value="turnoff">
            <kctl name="UL7_CH1 HW_GAIN2_OUT_CH1" value="0" />
            <kctl name="UL7_CH2 HW_GAIN2_OUT_CH2" value="0" />
        </path>
        <path name="HW_GAIN2_TO_HW_SRC2" value="turnon">
            <kctl name="HW_SRC_2_IN_CH1 HW_GAIN2_OUT_CH1" value="1" />
            <kctl name="HW_SRC_2_IN_CH2 HW_GAIN2_OUT_CH2" value="1" />
        </path>
        <path name="HW_GAIN2_TO_HW_SRC2" value="turnoff">
            <kctl name="HW_SRC_2_IN_CH1 HW_GAIN2_OUT_CH1" value="0" />
            <kctl name="HW_SRC_2_IN_CH2 HW_GAIN2_OUT_CH2" value="0" />
        </path>
        <path name="HW_SRC2_TO_CAPTURE7" value="turnon">
            <kctl name="UL7_CH1 HW_SRC_2_OUT_CH1" value="1" />
            <kctl name="UL7_CH2 HW_SRC_2_OUT_CH2" value="1" />
        </path>
        <path name="HW_SRC2_TO_CAPTURE7" value="turnoff">
            <kctl name="UL7_CH1 HW_SRC_2_OUT_CH1" value="0" />
            <kctl name="UL7_CH2 HW_SRC_2_OUT_CH2" value="0" />
        </path>
        <path name="ADDA_TO_CAPTURE7" value="turnon">
            <kctl name="UL7_CH1 ADDA_UL_CH1" value="1" />
            <kctl name="UL7_CH2 ADDA_UL_CH2" value="1" />
        </path>
        <path name="ADDA_TO_CAPTURE7" value="turnoff">
            <kctl name="UL7_CH1 ADDA_UL_CH1" value="0" />
            <kctl name="UL7_CH2 ADDA_UL_CH2" value="0" />
        </path>
        <path name="ADDA_TO_CAPTURE1" value="turnon">
            <kctl name="UL1_CH1 ADDA_UL_CH1" value="1" />
            <kctl name="UL1_CH2 ADDA_UL_CH2" value="1" />
        </path>
        <path name="ADDA_TO_CAPTURE1" value="turnoff">
            <kctl name="UL1_CH1 ADDA_UL_CH1" value="0" />
            <kctl name="UL1_CH2 ADDA_UL_CH2" value="0" />
        </path>
        <path name="ADDA_TO_CAPTURE1_4CH" value="turnon">
            <kctl name="UL1_CH1 ADDA_UL_CH3" value="1" />
            <kctl name="UL1_CH2 ADDA_UL_CH3" value="1" />
            <kctl name="UL1_CH3 ADDA_UL_CH1" value="1" />
            <kctl name="UL1_CH4 ADDA_UL_CH2" value="1" />
        </path>
        <path name="ADDA_TO_CAPTURE1_4CH" value="turnoff">
            <kctl name="UL1_CH1 ADDA_UL_CH3" value="0" />
            <kctl name="UL1_CH2 ADDA_UL_CH3" value="0" />
            <kctl name="UL1_CH3 ADDA_UL_CH1" value="0" />
            <kctl name="UL1_CH4 ADDA_UL_CH2" value="0" />
        </path>
        <path name="Mic1_ADDA_TO_CAPTURE1_4CH" value="turnon">
            <kctl name="UL1_CH1 ADDA_UL_CH1" value="1" />
            <kctl name="UL1_CH2 ADDA_UL_CH1" value="1" />
            <kctl name="UL1_CH3 ADDA_UL_CH1" value="1" />
            <kctl name="UL1_CH4 ADDA_UL_CH1" value="1" />
        </path>
        <path name="Mic1_ADDA_TO_CAPTURE1_4CH" value="turnoff">
            <kctl name="UL1_CH1 ADDA_UL_CH1" value="0" />
            <kctl name="UL1_CH2 ADDA_UL_CH1" value="0" />
            <kctl name="UL1_CH3 ADDA_UL_CH1" value="0" />
            <kctl name="UL1_CH4 ADDA_UL_CH1" value="0" />
        </path>
        <path name="Mic2_ADDA_TO_CAPTURE1_4CH" value="turnon">
            <kctl name="UL1_CH1 ADDA_UL_CH2" value="1" />
            <kctl name="UL1_CH2 ADDA_UL_CH2" value="1" />
            <kctl name="UL1_CH3 ADDA_UL_CH2" value="1" />
            <kctl name="UL1_CH4 ADDA_UL_CH2" value="1" />
        </path>
        <path name="Mic2_ADDA_TO_CAPTURE1_4CH" value="turnoff">
            <kctl name="UL1_CH1 ADDA_UL_CH2" value="0" />
            <kctl name="UL1_CH2 ADDA_UL_CH2" value="0" />
            <kctl name="UL1_CH3 ADDA_UL_CH2" value="0" />
            <kctl name="UL1_CH4 ADDA_UL_CH2" value="0" />
        </path>
        <path name="Mic3_ADDA_TO_CAPTURE1_4CH" value="turnon">
            <kctl name="UL1_CH1 ADDA_UL_CH2" value="1" />
            <kctl name="UL1_CH2 ADDA_UL_CH2" value="1" />
            <kctl name="UL1_CH3 ADDA_UL_CH2" value="1" />
            <kctl name="UL1_CH4 ADDA_UL_CH2" value="1" />
        </path>
        <path name="Mic3_ADDA_TO_CAPTURE1_4CH" value="turnoff">
            <kctl name="UL1_CH1 ADDA_UL_CH2" value="0" />
            <kctl name="UL1_CH2 ADDA_UL_CH2" value="0" />
            <kctl name="UL1_CH3 ADDA_UL_CH2" value="0" />
            <kctl name="UL1_CH4 ADDA_UL_CH2" value="0" />
        </path>
        <path name="DMic1_ADDA_TO_CAPTURE1_4CH" value="turnon">
            <kctl name="UL1_CH1 ADDA_UL_CH3" value="1" />
            <kctl name="UL1_CH2 ADDA_UL_CH3" value="1" />
            <kctl name="UL1_CH3 ADDA_UL_CH3" value="1" />
            <kctl name="UL1_CH4 ADDA_UL_CH3" value="1" />
        </path>
        <path name="DMic1_ADDA_TO_CAPTURE1_4CH" value="turnoff">
            <kctl name="UL1_CH1 ADDA_UL_CH3" value="0" />
            <kctl name="UL1_CH2 ADDA_UL_CH3" value="0" />
            <kctl name="UL1_CH3 ADDA_UL_CH3" value="0" />
            <kctl name="UL1_CH4 ADDA_UL_CH3" value="0" />
        </path>
        <path name="DMic2_ADDA_TO_CAPTURE1_4CH" value="turnon">
            <kctl name="UL1_CH1 ADDA_UL_CH2" value="1" />
            <kctl name="UL1_CH2 ADDA_UL_CH2" value="1" />
            <kctl name="UL1_CH3 ADDA_UL_CH2" value="1" />
            <kctl name="UL1_CH4 ADDA_UL_CH2" value="1" />
        </path>
        <path name="DMic2_ADDA_TO_CAPTURE1_4CH" value="turnoff">
            <kctl name="UL1_CH1 ADDA_UL_CH2" value="0" />
            <kctl name="UL1_CH2 ADDA_UL_CH2" value="0" />
            <kctl name="UL1_CH3 ADDA_UL_CH2" value="0" />
            <kctl name="UL1_CH4 ADDA_UL_CH2" value="0" />
        </path>
        <path name="DMic3_ADDA_TO_CAPTURE1_4CH" value="turnon">
            <kctl name="UL1_CH1 ADDA_UL_CH3" value="1" />
            <kctl name="UL1_CH2 ADDA_UL_CH3" value="1" />
            <kctl name="UL1_CH3 ADDA_UL_CH3" value="1" />
            <kctl name="UL1_CH4 ADDA_UL_CH3" value="1" />
        </path>
        <path name="DMic3_ADDA_TO_CAPTURE1_4CH" value="turnoff">
            <kctl name="UL1_CH1 ADDA_UL_CH3" value="0" />
            <kctl name="UL1_CH2 ADDA_UL_CH3" value="0" />
            <kctl name="UL1_CH3 ADDA_UL_CH3" value="0" />
            <kctl name="UL1_CH4 ADDA_UL_CH3" value="0" />
        </path>
        <path name="CONNSYS_TO_CAPTURE3" value="turnon">
            <kctl name="UL3_CH1 CONNSYS_I2S_CH1" value="1" />
            <kctl name="UL3_CH2 CONNSYS_I2S_CH2" value="1" />
        </path>
        <path name="CONNSYS_TO_CAPTURE3" value="turnoff">
            <kctl name="UL3_CH1 CONNSYS_I2S_CH1" value="0" />
            <kctl name="UL3_CH2 CONNSYS_I2S_CH2" value="0" />
        </path>
        <path name="HW_GAIN1_TO_CAPTURE6" value="turnon">
            <kctl name="HW_GAIN1_IN_CH1 CONNSYS_I2S_CH1" value="1" />
            <kctl name="HW_GAIN1_IN_CH2 CONNSYS_I2S_CH2" value="1" />
            <kctl name="UL6_CH1 GAIN1_OUT_CH1" value="1" />
            <kctl name="UL6_CH2 GAIN1_OUT_CH2" value="1" />
        </path>
        <path name="HW_GAIN1_TO_CAPTURE6" value="turnoff">
            <kctl name="HW_GAIN1_IN_CH1 CONNSYS_I2S_CH1" value="0" />
            <kctl name="HW_GAIN1_IN_CH2 CONNSYS_I2S_CH2" value="0" />
            <kctl name="UL6_CH1 GAIN1_OUT_CH1" value="0" />
            <kctl name="UL6_CH2 GAIN1_OUT_CH2" value="0" />
        </path>
        <path name="DL_MEMIF_TO_CAPTURE2" value="turnon">
            <kctl name="UL2_CH1 DL1_CH1" value="1" />
            <kctl name="UL2_CH2 DL1_CH2" value="1" />
            <kctl name="UL2_CH1 DL2_CH1" value="1" />
            <kctl name="UL2_CH2 DL2_CH2" value="1" />
            <kctl name="UL2_CH1 DL3_CH1" value="1" />
            <kctl name="UL2_CH2 DL3_CH2" value="1" />
            <kctl name="UL2_CH1 DL12_CH1" value="1" />
            <kctl name="UL2_CH2 DL12_CH2" value="1" />
        </path>
        <path name="DL_MEMIF_TO_CAPTURE2" value="turnoff">
            <kctl name="UL2_CH1 DL1_CH1" value="0" />
            <kctl name="UL2_CH2 DL1_CH2" value="0" />
            <kctl name="UL2_CH1 DL2_CH1" value="0" />
            <kctl name="UL2_CH2 DL2_CH2" value="0" />
            <kctl name="UL2_CH1 DL3_CH1" value="0" />
            <kctl name="UL2_CH2 DL3_CH2" value="0" />
            <kctl name="UL2_CH1 DL12_CH1" value="0" />
            <kctl name="UL2_CH2 DL12_CH2" value="0" />
        </path>
        <path name="DL_MEMIF_TO_CAPTURE2_SPEAKER_HIFI3" value="turnon">
            <kctl name="UL2_CH1 DL4_CH1" value="1" />
            <kctl name="UL2_CH2 DL4_CH2" value="1" />
        </path>
        <path name="DL_MEMIF_TO_CAPTURE2_SPEAKER_HIFI3" value="turnoff">
            <kctl name="UL2_CH1 DL4_CH1" value="0" />
            <kctl name="UL2_CH2 DL4_CH2" value="0" />
        </path>
        <path name="DL_PLAYBACK_TO_CAPTURE2_NON_SPEAKER_HIFI3" value="turnon">
            <kctl name="UL2_CH1 DL2_CH1" value="1" />
            <kctl name="UL2_CH2 DL2_CH2" value="1" />
            <kctl name="UL2_CH1 DL12_CH1" value="1" />
            <kctl name="UL2_CH2 DL12_CH2" value="1" />
            <kctl name="UL2_CH1 DL4_CH1" value="1" />
            <kctl name="UL2_CH2 DL4_CH2" value="1" />
        </path>
        <path name="DL_PLAYBACK_TO_CAPTURE2_NON_SPEAKER_HIFI3" value="turnoff">
            <kctl name="UL2_CH1 DL2_CH1" value="0" />
            <kctl name="UL2_CH2 DL2_CH2" value="0" />
            <kctl name="UL2_CH1 DL12_CH1" value="0" />
            <kctl name="UL2_CH2 DL12_CH2" value="0" />
            <kctl name="UL2_CH1 DL4_CH1" value="0" />
            <kctl name="UL2_CH2 DL4_CH2" value="0" />
        </path>
        <path name="I2S0_TO_CAPTURE2" value="turnon">
            <kctl name="UL2_CH1 I2S0_CH1" value="1" />
            <kctl name="UL2_CH2 I2S0_CH2" value="1" />
        </path>
        <path name="I2S0_TO_CAPTURE2" value="turnoff">
            <kctl name="UL2_CH1 I2S0_CH1" value="0" />
            <kctl name="UL2_CH2 I2S0_CH2" value="0" />
        </path>
        <path name="I2S0_TO_CAPTURE4" value="turnon">
            <kctl name="UL4_CH1 I2S0_CH1" value="1" />
            <kctl name="UL4_CH2 I2S0_CH2" value="1" />
        </path>
        <path name="I2S0_TO_CAPTURE4" value="turnoff">
            <kctl name="UL4_CH1 I2S0_CH1" value="0" />
            <kctl name="UL4_CH2 I2S0_CH2" value="0" />
        </path>
        <path name="TINYCONN_I2S0_TO_CAPTURE4" value="turnon">
            <kctl name="UL4_TINYCONN_CH1_MUX" value="I2S0_CH1" />
            <kctl name="UL4_TINYCONN_CH2_MUX" value="I2S0_CH2" />
        </path>
        <path name="TINYCONN_I2S0_TO_CAPTURE4" value="turnoff">
            <kctl name="UL4_TINYCONN_CH1_MUX" value="NONE" />
            <kctl name="UL4_TINYCONN_CH2_MUX" value="NONE" />
        </path>
        <path name="I2S2_TO_CAPTURE2" value="turnon">
            <kctl name="UL2_CH1 I2S2_CH1" value="1" />
            <kctl name="UL2_CH2 I2S2_CH2" value="1" />
        </path>
        <path name="I2S2_TO_CAPTURE2" value="turnoff">
            <kctl name="UL2_CH1 I2S2_CH1" value="0" />
            <kctl name="UL2_CH2 I2S2_CH2" value="0" />
        </path>
        <path name="CONNSYS_TO_ADDA_DL" value="turnon">
            <kctl name="HW_GAIN1_IN_CH1 CONNSYS_I2S_CH1" value="1" />
            <kctl name="HW_GAIN1_IN_CH2 CONNSYS_I2S_CH2" value="1" />
            <kctl name="ADDA_DL_CH1 GAIN1_OUT_CH1" value="1" />
            <kctl name="ADDA_DL_CH2 GAIN1_OUT_CH2" value="1" />
            <kctl name="ADDA_DL_CH3 GAIN1_OUT_CH1" value="1" />
            <kctl name="ADDA_DL_CH4 GAIN1_OUT_CH2" value="1" />
        </path>
        <path name="CONNSYS_TO_ADDA_DL" value="turnoff">
            <kctl name="HW_GAIN1_IN_CH1 CONNSYS_I2S_CH1" value="0" />
            <kctl name="HW_GAIN1_IN_CH2 CONNSYS_I2S_CH2" value="0" />
            <kctl name="ADDA_DL_CH1 GAIN1_OUT_CH1" value="0" />
            <kctl name="ADDA_DL_CH2 GAIN1_OUT_CH2" value="0" />
            <kctl name="ADDA_DL_CH3 GAIN1_OUT_CH1" value="0" />
            <kctl name="ADDA_DL_CH4 GAIN1_OUT_CH2" value="0" />
        </path>
        <path name="CONNSYS_TO_I2S1" value="turnon">
            <kctl name="HW_GAIN1_IN_CH1 CONNSYS_I2S_CH1" value="1" />
            <kctl name="HW_GAIN1_IN_CH2 CONNSYS_I2S_CH2" value="1" />
            <kctl name="I2S1_CH1 GAIN1_OUT_CH1" value="1" />
            <kctl name="I2S1_CH2 GAIN1_OUT_CH2" value="1" />
        </path>
        <path name="CONNSYS_TO_I2S1" value="turnoff">
            <kctl name="HW_GAIN1_IN_CH1 CONNSYS_I2S_CH1" value="0" />
            <kctl name="HW_GAIN1_IN_CH2 CONNSYS_I2S_CH2" value="0" />
            <kctl name="I2S1_CH1 GAIN1_OUT_CH1" value="0" />
            <kctl name="I2S1_CH2 GAIN1_OUT_CH2" value="0" />
        </path>
        <path name="CONNSYS_TO_I2S3" value="turnon">
            <kctl name="HW_GAIN1_IN_CH1 CONNSYS_I2S_CH1" value="1" />
            <kctl name="HW_GAIN1_IN_CH2 CONNSYS_I2S_CH2" value="1" />
            <kctl name="I2S3_CH1 GAIN1_OUT_CH1" value="1" />
            <kctl name="I2S3_CH2 GAIN1_OUT_CH2" value="1" />
        </path>
        <path name="CONNSYS_TO_I2S3" value="turnoff">
            <kctl name="HW_GAIN1_IN_CH1 CONNSYS_I2S_CH1" value="0" />
            <kctl name="HW_GAIN1_IN_CH2 CONNSYS_I2S_CH2" value="0" />
            <kctl name="I2S3_CH1 GAIN1_OUT_CH1" value="0" />
            <kctl name="I2S3_CH2 GAIN1_OUT_CH2" value="0" />
        </path>
        <path name="CONNSYS_TO_I2S5" value="turnon">
            <kctl name="HW_GAIN1_IN_CH1 CONNSYS_I2S_CH1" value="1" />
            <kctl name="HW_GAIN1_IN_CH2 CONNSYS_I2S_CH2" value="1" />
            <kctl name="I2S5_CH1 GAIN1_OUT_CH1" value="1" />
            <kctl name="I2S5_CH2 GAIN1_OUT_CH2" value="1" />
        </path>
        <path name="CONNSYS_TO_I2S5" value="turnoff">
            <kctl name="HW_GAIN1_IN_CH1 CONNSYS_I2S_CH1" value="0" />
            <kctl name="HW_GAIN1_IN_CH2 CONNSYS_I2S_CH2" value="0" />
            <kctl name="I2S5_CH1 GAIN1_OUT_CH1" value="0" />
            <kctl name="I2S5_CH2 GAIN1_OUT_CH2" value="0" />
        </path>
        <path name="MRG_TO_ADDA_I2S" value="turnon">
        </path>
        <path name="MRG_TO_ADDA_I2S" value="turnoff">
        </path>
        <path name="MD1_TO_ADDA_DL" value="turnon">
            <kctl name="ADDA_DL_CH1 PCM_2_CAP_CH1" value="1" />
            <kctl name="ADDA_DL_CH2 PCM_2_CAP_CH1" value="1" />
            <kctl name="ADDA_DL_CH3 PCM_2_CAP_CH1" value="1" />
            <kctl name="ADDA_DL_CH4 PCM_2_CAP_CH1" value="1" />
        </path>
        <path name="MD1_TO_ADDA_DL" value="turnoff">
            <kctl name="ADDA_DL_CH1 PCM_2_CAP_CH1" value="0" />
            <kctl name="ADDA_DL_CH2 PCM_2_CAP_CH1" value="0" />
            <kctl name="ADDA_DL_CH3 PCM_2_CAP_CH1" value="0" />
            <kctl name="ADDA_DL_CH4 PCM_2_CAP_CH1" value="0" />
        </path>
        <path name="DMic1_MD1_TO_ADDA_DL" value="turnon">
            <kctl name="PCM_2_PB_CH1 ADDA_UL_CH3" value="1" />
            <kctl name="PCM_2_PB_CH2 ADDA_UL_CH3" value="1" />
            <kctl name="ADDA_DL_CH1 PCM_2_CAP_CH1" value="1" />
            <kctl name="ADDA_DL_CH2 PCM_2_CAP_CH1" value="1" />
            <kctl name="ADDA_DL_CH3 PCM_2_CAP_CH1" value="1" />
            <kctl name="ADDA_DL_CH4 PCM_2_CAP_CH1" value="1" />
        </path>
        <path name="DMic1_MD1_TO_ADDA_DL" value="turnoff">
            <kctl name="PCM_2_PB_CH1 ADDA_UL_CH3" value="0" />
            <kctl name="PCM_2_PB_CH2 ADDA_UL_CH3" value="0" />
            <kctl name="ADDA_DL_CH1 PCM_2_CAP_CH1" value="0" />
            <kctl name="ADDA_DL_CH2 PCM_2_CAP_CH1" value="0" />
            <kctl name="ADDA_DL_CH3 PCM_2_CAP_CH1" value="0" />
            <kctl name="ADDA_DL_CH4 PCM_2_CAP_CH1" value="0" />
        </path>
        <path name="DMic2_MD1_TO_ADDA_DL" value="turnon">
            <kctl name="PCM_2_PB_CH1 ADDA_UL_CH2" value="1" />
            <kctl name="PCM_2_PB_CH2 ADDA_UL_CH2" value="1" />
            <kctl name="ADDA_DL_CH1 PCM_2_CAP_CH1" value="1" />
            <kctl name="ADDA_DL_CH2 PCM_2_CAP_CH1" value="1" />
            <kctl name="ADDA_DL_CH3 PCM_2_CAP_CH1" value="1" />
            <kctl name="ADDA_DL_CH4 PCM_2_CAP_CH1" value="1" />
        </path>
        <path name="DMic2_MD1_TO_ADDA_DL" value="turnoff">
            <kctl name="PCM_2_PB_CH1 ADDA_UL_CH2" value="0" />
            <kctl name="PCM_2_PB_CH2 ADDA_UL_CH2" value="0" />
            <kctl name="ADDA_DL_CH1 PCM_2_CAP_CH1" value="0" />
            <kctl name="ADDA_DL_CH2 PCM_2_CAP_CH1" value="0" />
            <kctl name="ADDA_DL_CH3 PCM_2_CAP_CH1" value="0" />
            <kctl name="ADDA_DL_CH4 PCM_2_CAP_CH1" value="0" />
        </path>
        <path name="DMic3_MD1_TO_ADDA_DL" value="turnon">
            <kctl name="PCM_2_PB_CH1 ADDA_UL_CH3" value="1" />
            <kctl name="PCM_2_PB_CH2 ADDA_UL_CH3" value="1" />
            <kctl name="ADDA_DL_CH1 PCM_2_CAP_CH1" value="1" />
            <kctl name="ADDA_DL_CH2 PCM_2_CAP_CH1" value="1" />
            <kctl name="ADDA_DL_CH3 PCM_2_CAP_CH1" value="1" />
            <kctl name="ADDA_DL_CH4 PCM_2_CAP_CH1" value="1" />
        </path>
        <path name="DMic3_MD1_TO_ADDA_DL" value="turnoff">
            <kctl name="PCM_2_PB_CH1 ADDA_UL_CH3" value="0" />
            <kctl name="PCM_2_PB_CH2 ADDA_UL_CH3" value="0" />
            <kctl name="ADDA_DL_CH1 PCM_2_CAP_CH1" value="0" />
            <kctl name="ADDA_DL_CH2 PCM_2_CAP_CH1" value="0" />
            <kctl name="ADDA_DL_CH3 PCM_2_CAP_CH1" value="0" />
            <kctl name="ADDA_DL_CH4 PCM_2_CAP_CH1" value="0" />
        </path>
        <path name="MD1_TO_I2S1" value="turnon">
            <kctl name="I2S1_CH1 PCM_2_CAP_CH1" value="1" />
            <kctl name="I2S1_CH2 PCM_2_CAP_CH1" value="1" />
        </path>
        <path name="MD1_TO_I2S1" value="turnoff">
            <kctl name="I2S1_CH1 PCM_2_CAP_CH1" value="0" />
            <kctl name="I2S1_CH2 PCM_2_CAP_CH1" value="0" />
        </path>
        <path name="MD1_TO_I2S3" value="turnon">
            <kctl name="I2S3_CH1 PCM_2_CAP_CH1" value="1" />
            <kctl name="I2S3_CH2 PCM_2_CAP_CH1" value="1" />
        </path>
        <path name="MD1_TO_I2S3" value="turnoff">
            <kctl name="I2S3_CH1 PCM_2_CAP_CH1" value="0" />
            <kctl name="I2S3_CH2 PCM_2_CAP_CH1" value="0" />
        </path>
        <path name="MIC1_MD1_TO_I2S3" value="turnon">
            <kctl name="PCM_2_PB_CH1 ADDA_UL_CH1" value="1" />
            <kctl name="PCM_2_PB_CH2 ADDA_UL_CH1" value="1" />
            <kctl name="I2S3_CH1 PCM_2_CAP_CH1" value="1" />
            <kctl name="I2S3_CH2 PCM_2_CAP_CH1" value="1" />
        </path>
        <path name="MIC1_MD1_TO_I2S3" value="turnoff">
            <kctl name="PCM_2_PB_CH1 ADDA_UL_CH1" value="0" />
            <kctl name="PCM_2_PB_CH2 ADDA_UL_CH1" value="0" />
            <kctl name="I2S3_CH1 PCM_2_CAP_CH1" value="0" />
            <kctl name="I2S3_CH2 PCM_2_CAP_CH1" value="0" />
        </path>
        <path name="MIC2_MD1_TO_I2S3" value="turnon">
            <kctl name="PCM_2_PB_CH1 ADDA_UL_CH2" value="1" />
            <kctl name="PCM_2_PB_CH2 ADDA_UL_CH2" value="1" />
            <kctl name="I2S3_CH1 PCM_2_CAP_CH1" value="1" />
            <kctl name="I2S3_CH2 PCM_2_CAP_CH1" value="1" />
        </path>
        <path name="MIC2_MD1_TO_I2S3" value="turnoff">
            <kctl name="PCM_2_PB_CH1 ADDA_UL_CH2" value="0" />
            <kctl name="PCM_2_PB_CH2 ADDA_UL_CH2" value="0" />
            <kctl name="I2S3_CH1 PCM_2_CAP_CH1" value="0" />
            <kctl name="I2S3_CH2 PCM_2_CAP_CH1" value="0" />
        </path>
        <path name="MIC3_MD1_TO_I2S3" value="turnon">
            <kctl name="PCM_2_PB_CH1 ADDA_UL_CH2" value="1" />
            <kctl name="PCM_2_PB_CH2 ADDA_UL_CH2" value="1" />
            <kctl name="I2S3_CH1 PCM_2_CAP_CH1" value="1" />
            <kctl name="I2S3_CH2 PCM_2_CAP_CH1" value="1" />
        </path>
        <path name="MIC3_MD1_TO_I2S3" value="turnoff">
            <kctl name="PCM_2_PB_CH1 ADDA_UL_CH2" value="0" />
            <kctl name="PCM_2_PB_CH2 ADDA_UL_CH2" value="0" />
            <kctl name="I2S3_CH1 PCM_2_CAP_CH1" value="0" />
            <kctl name="I2S3_CH2 PCM_2_CAP_CH1" value="0" />
        </path>
        <path name="MD1_TO_I2S5" value="turnon">
            <kctl name="I2S5_CH1 PCM_2_CAP_CH1" value="1" />
            <kctl name="I2S5_CH2 PCM_2_CAP_CH1" value="1" />
        </path>
        <path name="MD1_TO_I2S5" value="turnoff">
            <kctl name="I2S5_CH1 PCM_2_CAP_CH1" value="0" />
            <kctl name="I2S5_CH2 PCM_2_CAP_CH1" value="0" />
        </path>
        <path name="MD2_TO_ADDA_DL" value="turnon">
            <kctl name="ADDA_DL_CH1 PCM_1_CAP_CH1" value="1" />
            <kctl name="ADDA_DL_CH2 PCM_1_CAP_CH1" value="1" />
            <kctl name="ADDA_DL_CH3 PCM_1_CAP_CH1" value="1" />
            <kctl name="ADDA_DL_CH4 PCM_1_CAP_CH1" value="1" />
        </path>
        <path name="MD2_TO_ADDA_DL" value="turnoff">
            <kctl name="ADDA_DL_CH1 PCM_1_CAP_CH1" value="0" />
            <kctl name="ADDA_DL_CH2 PCM_1_CAP_CH1" value="0" />
            <kctl name="ADDA_DL_CH3 PCM_1_CAP_CH1" value="0" />
            <kctl name="ADDA_DL_CH4 PCM_1_CAP_CH1" value="0" />
        </path>
        <path name="MD2_TO_I2S1" value="turnon">
            <kctl name="I2S1_CH1 PCM_1_CAP_CH1" value="1" />
            <kctl name="I2S1_CH2 PCM_1_CAP_CH1" value="1" />
        </path>
        <path name="MD2_TO_I2S1" value="turnoff">
            <kctl name="I2S1_CH1 PCM_1_CAP_CH1" value="0" />
            <kctl name="I2S1_CH2 PCM_1_CAP_CH1" value="0" />
        </path>
        <path name="MD2_TO_I2S3" value="turnon">
            <kctl name="I2S3_CH1 PCM_1_CAP_CH1" value="1" />
            <kctl name="I2S3_CH2 PCM_1_CAP_CH1" value="1" />
        </path>
        <path name="MD2_TO_I2S3" value="turnoff">
            <kctl name="I2S3_CH1 PCM_1_CAP_CH1" value="0" />
            <kctl name="I2S3_CH2 PCM_1_CAP_CH1" value="0" />
        </path>
        <path name="MD2_TO_I2S5" value="turnon">
            <kctl name="I2S5_CH1 PCM_1_CAP_CH1" value="1" />
            <kctl name="I2S5_CH2 PCM_1_CAP_CH1" value="1" />
        </path>
        <path name="MD2_TO_I2S5" value="turnoff">
            <kctl name="I2S5_CH1 PCM_1_CAP_CH1" value="0" />
            <kctl name="I2S5_CH2 PCM_1_CAP_CH1" value="0" />
        </path>
        <path name="ADDA_UL_TO_ADDA_DL" value="turnon">
            <kctl name="ADDA_DL_CH1 ADDA_UL_CH1" value="1" />
            <kctl name="ADDA_DL_CH2 ADDA_UL_CH2" value="1" />
            <kctl name="ADDA_DL_CH3 ADDA_UL_CH1" value="1" />
            <kctl name="ADDA_DL_CH4 ADDA_UL_CH2" value="1" />
        </path>
        <path name="ADDA_UL_TO_ADDA_DL" value="turnoff">
            <kctl name="ADDA_DL_CH1 ADDA_UL_CH1" value="0" />
            <kctl name="ADDA_DL_CH2 ADDA_UL_CH2" value="0" />
            <kctl name="ADDA_DL_CH3 ADDA_UL_CH1" value="0" />
            <kctl name="ADDA_DL_CH4 ADDA_UL_CH2" value="0" />
        </path>
        <path name="DMic1_ADDA_UL_TO_ADDA_DL" value="turnon">
            <kctl name="ADDA_DL_CH1 ADDA_UL_CH3" value="1" />
            <kctl name="ADDA_DL_CH2 ADDA_UL_CH3" value="1" />
            <kctl name="ADDA_DL_CH3 ADDA_UL_CH3" value="1" />
            <kctl name="ADDA_DL_CH4 ADDA_UL_CH3" value="1" />
        </path>
        <path name="DMic1_ADDA_UL_TO_ADDA_DL" value="turnoff">
            <kctl name="ADDA_DL_CH1 ADDA_UL_CH3" value="0" />
            <kctl name="ADDA_DL_CH2 ADDA_UL_CH3" value="0" />
            <kctl name="ADDA_DL_CH3 ADDA_UL_CH3" value="0" />
            <kctl name="ADDA_DL_CH4 ADDA_UL_CH3" value="0" />
        </path>
        <path name="DMic2_ADDA_UL_TO_ADDA_DL" value="turnon">
            <kctl name="ADDA_DL_CH1 ADDA_UL_CH2" value="1" />
            <kctl name="ADDA_DL_CH2 ADDA_UL_CH2" value="1" />
            <kctl name="ADDA_DL_CH3 ADDA_UL_CH2" value="1" />
            <kctl name="ADDA_DL_CH4 ADDA_UL_CH2" value="1" />
        </path>
        <path name="DMic2_ADDA_UL_TO_ADDA_DL" value="turnoff">
            <kctl name="ADDA_DL_CH1 ADDA_UL_CH2" value="0" />
            <kctl name="ADDA_DL_CH2 ADDA_UL_CH2" value="0" />
            <kctl name="ADDA_DL_CH3 ADDA_UL_CH2" value="0" />
            <kctl name="ADDA_DL_CH4 ADDA_UL_CH2" value="0" />
        </path>
        <path name="DMic3_ADDA_UL_TO_ADDA_DL" value="turnon">
            <kctl name="ADDA_DL_CH1 ADDA_UL_CH3" value="1" />
            <kctl name="ADDA_DL_CH2 ADDA_UL_CH3" value="1" />
            <kctl name="ADDA_DL_CH3 ADDA_UL_CH3" value="1" />
            <kctl name="ADDA_DL_CH4 ADDA_UL_CH3" value="1" />
        </path>
        <path name="DMic3_ADDA_UL_TO_ADDA_DL" value="turnoff">
            <kctl name="ADDA_DL_CH1 ADDA_UL_CH3" value="0" />
            <kctl name="ADDA_DL_CH2 ADDA_UL_CH3" value="0" />
            <kctl name="ADDA_DL_CH3 ADDA_UL_CH3" value="0" />
            <kctl name="ADDA_DL_CH4 ADDA_UL_CH3" value="0" />
        </path>
        <path name="ADDA_UL_TO_I2S1" value="turnon">
            <kctl name="I2S1_CH1 ADDA_UL_CH1" value="1" />
            <kctl name="I2S1_CH2 ADDA_UL_CH2" value="1" />
        </path>
        <path name="ADDA_UL_TO_I2S1" value="turnoff">
            <kctl name="I2S1_CH1 ADDA_UL_CH1" value="0" />
            <kctl name="I2S1_CH2 ADDA_UL_CH2" value="0" />
        </path>
        <path name="ADDA_UL_TO_I2S3" value="turnon">
            <kctl name="I2S3_CH1 ADDA_UL_CH1" value="1" />
            <kctl name="I2S3_CH2 ADDA_UL_CH2" value="1" />
        </path>
        <path name="ADDA_UL_TO_I2S3" value="turnoff">
            <kctl name="I2S3_CH1 ADDA_UL_CH1" value="0" />
            <kctl name="I2S3_CH2 ADDA_UL_CH2" value="0" />
        </path>
        <path name="DMic1_ADDA_UL_TO_I2S3" value="turnon">
            <kctl name="I2S3_CH1 ADDA_UL_CH3" value="1" />
            <kctl name="I2S3_CH2 ADDA_UL_CH3" value="1" />
        </path>
        <path name="DMic1_ADDA_UL_TO_I2S3" value="turnoff">
            <kctl name="I2S3_CH1 ADDA_UL_CH3" value="0" />
            <kctl name="I2S3_CH2 ADDA_UL_CH3" value="0" />
        </path>
        <path name="DMic2_ADDA_UL_TO_I2S3" value="turnon">
            <kctl name="I2S3_CH1 ADDA_UL_CH2" value="1" />
            <kctl name="I2S3_CH2 ADDA_UL_CH2" value="1" />
        </path>
        <path name="DMic2_ADDA_UL_TO_I2S3" value="turnoff">
            <kctl name="I2S3_CH1 ADDA_UL_CH2" value="0" />
            <kctl name="I2S3_CH2 ADDA_UL_CH2" value="0" />
        </path>
        <path name="DMic3_ADDA_UL_TO_I2S3" value="turnon">
            <kctl name="I2S3_CH1 ADDA_UL_CH3" value="1" />
            <kctl name="I2S3_CH2 ADDA_UL_CH3" value="1" />
        </path>
        <path name="DMic3_ADDA_UL_TO_I2S3" value="turnoff">
            <kctl name="I2S3_CH1 ADDA_UL_CH3" value="0" />
            <kctl name="I2S3_CH2 ADDA_UL_CH3" value="0" />
        </path>
        <path name="ADDA_UL_TO_I2S5" value="turnon">
            <kctl name="I2S5_CH1 ADDA_UL_CH1" value="1" />
            <kctl name="I2S5_CH2 ADDA_UL_CH2" value="1" />
        </path>
        <path name="ADDA_UL_TO_I2S5" value="turnoff">
            <kctl name="I2S5_CH1 ADDA_UL_CH1" value="0" />
            <kctl name="I2S5_CH2 ADDA_UL_CH2" value="0" />
        </path>
        <path name="SPK_INIT" value="turnon">
            <kctl name="I2S1_HD_Mux" value="Low_Jitter" />
            <kctl name="I2S1_Out_Mux" value="Dummy_Widget" />
            <kctl name="I2S3_HD_Mux" value="Low_Jitter" />
            <kctl name="I2S3_Out_Mux" value="Dummy_Widget" />
            <kctl name="I2S5_HD_Mux" value="Low_Jitter" />
            <kctl name="I2S5_Out_Mux" value="Dummy_Widget" />
        </path>
        <path name="SPK_INIT" value="turnoff">
            <kctl name="I2S1_Out_Mux" value="Normal" />
            <kctl name="I2S1_HD_Mux" value="Normal" />
            <kctl name="I2S3_Out_Mux" value="Normal" />
            <kctl name="I2S3_HD_Mux" value="Normal" />
            <kctl name="I2S5_Out_Mux" value="Normal" />
            <kctl name="I2S5_HD_Mux" value="Normal" />
        </path>
        <path name="PLAYBACK1_TO_MD1_ECHO_REF" value="turnon">
            <kctl name="PCM_2_PB_CH4 DL1_CH1" value="1" />
            <kctl name="PCM_2_PB_CH5 DL1_CH2" value="1" />
        </path>
        <path name="PLAYBACK1_TO_MD1_ECHO_REF" value="turnoff">
            <kctl name="PCM_2_PB_CH4 DL1_CH1" value="0" />
            <kctl name="PCM_2_PB_CH5 DL1_CH2" value="0" />
        </path>
        <path name="PLAYBACK4_TO_MD1_ECHO_REF" value="turnon">
            <kctl name="PCM_2_PB_CH4 DL4_CH1" value="1" />
            <kctl name="PCM_2_PB_CH5 DL4_CH2" value="1" />
        </path>
        <path name="PLAYBACK4_TO_MD1_ECHO_REF" value="turnoff">
            <kctl name="PCM_2_PB_CH4 DL4_CH1" value="0" />
            <kctl name="PCM_2_PB_CH5 DL4_CH2" value="0" />
        </path>
        <path name="MD1_ECHO_REF_I2S0_ON" value="setting">
            <kctl name="PCM_2_PB_CH4 I2S0_CH1" value="1" />
            <kctl name="PCM_2_PB_CH5 I2S0_CH2" value="1" />
        </path>
        <path name="MD1_ECHO_REF_I2S0_OFF" value="setting">
            <kctl name="PCM_2_PB_CH4 I2S0_CH1" value="0" />
            <kctl name="PCM_2_PB_CH5 I2S0_CH2" value="0" />
        </path>
        <path name="MD1_ECHO_REF_I2S2_ON" value="setting">
            <kctl name="PCM_2_PB_CH4 I2S2_CH1" value="1" />
            <kctl name="PCM_2_PB_CH5 I2S2_CH2" value="1" />
        </path>
        <path name="MD1_ECHO_REF_I2S2_OFF" value="setting">
            <kctl name="PCM_2_PB_CH4 I2S2_CH1" value="0" />
            <kctl name="PCM_2_PB_CH5 I2S2_CH2" value="0" />
        </path>
        <path name="PLAYBACK1_TO_MD2_ECHO_REF" value="turnon">
            <kctl name="PCM_1_PB_CH4 DL1_CH2" value="1" />
        </path>
        <path name="PLAYBACK1_TO_MD2_ECHO_REF" value="turnoff">
            <kctl name="PCM_1_PB_CH4 DL1_CH2" value="0" />
        </path>
        <path name="PLAYBACK4_TO_MD2_ECHO_REF" value="turnon">
            <kctl name="PCM_1_PB_CH4 DL4_CH2" value="1" />
        </path>
        <path name="PLAYBACK4_TO_MD2_ECHO_REF" value="turnoff">
            <kctl name="PCM_1_PB_CH4 DL4_CH2" value="0" />
        </path>
        <path name="MD2_ECHO_REF_I2S0_ON" value="setting">
            <kctl name="PCM_1_PB_CH4 I2S0_CH2" value="1" />
        </path>
        <path name="MD2_ECHO_REF_I2S0_OFF" value="setting">
            <kctl name="PCM_1_PB_CH4 I2S0_CH2" value="0" />
        </path>
        <path name="MD2_ECHO_REF_I2S2_ON" value="setting">
            <kctl name="PCM_1_PB_CH4 I2S2_CH2" value="1" />
        </path>
        <path name="MD2_ECHO_REF_I2S2_OFF" value="setting">
            <kctl name="PCM_1_PB_CH4 I2S2_CH2" value="0" />
        </path>
        <path name="VOW_BARGE_IN_ECHO" value="turnon">
            <kctl name="HW_SRC_1_IN_CH1 DL1_CH1" value="1" />
            <kctl name="HW_SRC_1_IN_CH2 DL1_CH2" value="1" />
            <kctl name="HW_SRC_1_IN_CH1 DL2_CH1" value="1" />
            <kctl name="HW_SRC_1_IN_CH2 DL2_CH2" value="1" />
            <kctl name="HW_SRC_1_IN_CH1 DL3_CH1" value="1" />
            <kctl name="HW_SRC_1_IN_CH2 DL3_CH2" value="1" />
            <kctl name="HW_SRC_1_IN_CH1 DL6_CH1" value="1" />
            <kctl name="HW_SRC_1_IN_CH2 DL6_CH2" value="1" />
            <kctl name="UL2_CH1 SRC_1_OUT_CH1" value="1" />
            <kctl name="UL2_CH2 SRC_1_OUT_CH2" value="1" />
        </path>
        <path name="VOW_BARGE_IN_ECHO" value="turnoff">
            <kctl name="HW_SRC_1_IN_CH1 DL1_CH1" value="0" />
            <kctl name="HW_SRC_1_IN_CH2 DL1_CH2" value="0" />
            <kctl name="HW_SRC_1_IN_CH1 DL2_CH1" value="0" />
            <kctl name="HW_SRC_1_IN_CH2 DL2_CH2" value="0" />
            <kctl name="HW_SRC_1_IN_CH1 DL3_CH1" value="0" />
            <kctl name="HW_SRC_1_IN_CH2 DL3_CH2" value="0" />
            <kctl name="HW_SRC_1_IN_CH1 DL6_CH1" value="0" />
            <kctl name="HW_SRC_1_IN_CH2 DL6_CH2" value="0" />
            <kctl name="UL2_CH1 SRC_1_OUT_CH1" value="0" />
            <kctl name="UL2_CH2 SRC_1_OUT_CH2" value="0" />
        </path>
        <path name="VOW_BARGE_IN_ECHO_SPEAKER_HIFI3" value="turnon">
            <kctl name="HW_SRC_1_IN_CH1 DL4_CH1" value="1" />
            <kctl name="HW_SRC_1_IN_CH2 DL4_CH2" value="1" />
            <kctl name="UL2_CH1 SRC_1_OUT_CH1" value="1" />
            <kctl name="UL2_CH2 SRC_1_OUT_CH2" value="1" />
        </path>
        <path name="VOW_BARGE_IN_ECHO_SPEAKER_HIFI3" value="turnoff">
            <kctl name="HW_SRC_1_IN_CH1 DL4_CH1" value="0" />
            <kctl name="HW_SRC_1_IN_CH2 DL4_CH2" value="0" />
            <kctl name="UL2_CH1 SRC_1_OUT_CH1" value="0" />
            <kctl name="UL2_CH2 SRC_1_OUT_CH2" value="0" />
        </path>
        <path name="VOW_BARGE_IN_ECHO_DSP_SMARTPA" value="turnon">
            <kctl name="HW_SRC_1_IN_CH1 I2S0_CH1" value="1" />
            <kctl name="HW_SRC_1_IN_CH2 I2S0_CH2" value="1" />
            <kctl name="UL2_CH1 SRC_1_OUT_CH1" value="1" />
            <kctl name="UL2_CH2 SRC_1_OUT_CH2" value="1" />
        </path>
        <path name="VOW_BARGE_IN_ECHO_DSP_SMARTPA" value="turnoff">
            <kctl name="HW_SRC_1_IN_CH1 I2S0_CH1" value="0" />
            <kctl name="HW_SRC_1_IN_CH2 I2S0_CH2" value="0" />
            <kctl name="UL2_CH1 SRC_1_OUT_CH1" value="0" />
            <kctl name="UL2_CH2 SRC_1_OUT_CH2" value="0" />
        </path>
        <path name="reset_device_setting" value="setting">
            <kctl name="deep_buffer_scenario" value="0" />
            <kctl name="record_xrun_assert" value="0" />
            <kctl name="fast_play_scenario" value="0" />
            <kctl name="primary_play_scenario" value="0" />
            <kctl name="voip_rx_scenario" value="0" />
            <kctl name="ADDA_UL_Mux" value="MTKAIF" />
            <kctl name="ADDA_CH34_UL_Mux" value="MTKAIF" />
            <kctl name="LOL Mux" value="Open" />
            <kctl name="HPL Mux" value="Open" />
            <kctl name="HPR Mux" value="Open" />
            <kctl name="RCV Mux" value="Open" />
            <kctl name="ADC_L_Mux" value="Idle" />
            <kctl name="ADC_R_Mux" value="Idle" />
            <kctl name="ADC_3_Mux" value="Idle" />
            <kctl name="PGA_L_Mux" value="None" />
            <kctl name="PGA_R_Mux" value="None" />
            <kctl name="PGA_3_Mux" value="None" />
            <kctl name="MISO0_MUX" value="UL1_CH1" />
            <kctl name="MISO1_MUX" value="UL1_CH2" />
            <kctl name="MISO2_MUX" value="UL2_CH1" />
            <kctl name="UL_SRC_MUX" value="AMIC" />
            <kctl name="UL2_SRC_MUX" value="AMIC" />
        </path>
        <path name="MD1_TO_CAPTURE2" value="turnon">
            <kctl name="UL2_CH1 PCM_2_CAP_CH1" value="1" />
            <kctl name="UL2_CH2 PCM_2_CAP_CH1" value="1" />
        </path>
        <path name="MD1_TO_CAPTURE2" value="turnoff">
            <kctl name="UL2_CH1 PCM_2_CAP_CH1" value="0" />
            <kctl name="UL2_CH2 PCM_2_CAP_CH1" value="0" />
        </path>
        <path name="MD2_TO_CAPTURE2" value="turnon">
            <kctl name="UL2_CH1 PCM_1_CAP_CH1" value="1" />
            <kctl name="UL2_CH2 PCM_1_CAP_CH1" value="1" />
        </path>
        <path name="MD2_TO_CAPTURE2" value="turnoff">
            <kctl name="UL2_CH1 PCM_1_CAP_CH1" value="0" />
            <kctl name="UL2_CH2 PCM_1_CAP_CH1" value="0" />
        </path>
        <path name="PLAYBACK2_TO_MD1" value="turnon">
            <kctl name="PCM_2_PB_CH1 DL2_CH1" value="1" />
            <kctl name="PCM_2_PB_CH2 DL2_CH2" value="1" />
        </path>
        <path name="PLAYBACK2_TO_MD1" value="turnoff">
            <kctl name="PCM_2_PB_CH1 DL2_CH1" value="0" />
            <kctl name="PCM_2_PB_CH2 DL2_CH2" value="0" />
        </path>
        <path name="PLAYBACK2_TO_MD2" value="turnon">
            <kctl name="PCM_1_PB_CH1 DL2_CH1" value="1" />
            <kctl name="PCM_1_PB_CH2 DL2_CH2" value="1" />
        </path>
        <path name="PLAYBACK2_TO_MD2" value="turnoff">
            <kctl name="PCM_1_PB_CH1 DL2_CH1" value="0" />
            <kctl name="PCM_1_PB_CH2 DL2_CH2" value="0" />
        </path>
        <path name="ADDA_UL_TO_MD1" value="turnon">
            <kctl name="PCM_2_PB_CH1 ADDA_UL_CH1" value="1" />
            <kctl name="PCM_2_PB_CH2 ADDA_UL_CH2" value="1" />
            <kctl name="PCM_2_PB_CH3 ADDA_UL_CH3" value="1" />
        </path>
        <path name="ADDA_UL_TO_MD1" value="turnoff">
            <kctl name="PCM_2_PB_CH1 ADDA_UL_CH1" value="0" />
            <kctl name="PCM_2_PB_CH2 ADDA_UL_CH2" value="0" />
            <kctl name="PCM_2_PB_CH3 ADDA_UL_CH3" value="0" />
        </path>
        <path name="ADDA_UL_TO_MD2" value="turnon">
            <kctl name="PCM_1_PB_CH1 ADDA_UL_CH1" value="1" />
            <kctl name="PCM_1_PB_CH2 ADDA_UL_CH2" value="1" />
        </path>
        <path name="ADDA_UL_TO_MD2" value="turnoff">
            <kctl name="PCM_1_PB_CH1 ADDA_UL_CH1" value="0" />
            <kctl name="PCM_1_PB_CH2 ADDA_UL_CH2" value="0" />
        </path>
        <path name="PLAYBACK1_TO_MD1_CH4" value="turnon">
            <kctl name="PCM_2_PB_CH4 DL1_CH1" value="1" />
        </path>
        <path name="PLAYBACK1_TO_MD1_CH4" value="turnoff">
            <kctl name="PCM_2_PB_CH4 DL1_CH1" value="0" />
        </path>
        <path name="PLAYBACK1_TO_MD2_CH4" value="turnon">
            <kctl name="PCM_1_PB_CH4 DL1_CH1" value="1" />
        </path>
        <path name="PLAYBACK1_TO_MD2_CH4" value="turnoff">
            <kctl name="PCM_1_PB_CH4 DL1_CH1" value="0" />
        </path>
        <path name="MD1_TO_CAPTURE_MONO_1" value="turnon">
            <kctl name="UL_MONO_1_CH1 PCM_2_CAP_CH1" value="1" />
        </path>
        <path name="MD1_TO_CAPTURE_MONO_1" value="turnoff">
            <kctl name="UL_MONO_1_CH1 PCM_2_CAP_CH1" value="0" />
        </path>
        <path name="MD2_TO_CAPTURE_MONO_1" value="turnon">
            <kctl name="UL_MONO_1_CH1 PCM_1_CAP_CH1" value="1" />
        </path>
        <path name="MD2_TO_CAPTURE_MONO_1" value="turnoff">
            <kctl name="UL_MONO_1_CH1 PCM_1_CAP_CH1" value="0" />
        </path>
        <path name="USB_ECHO_REF_DEBUG" value="turnon">
            <kctl name="ADDA_DL_CH1 DL2_CH1" value="1" />
            <kctl name="ADDA_DL_CH1 ADDA_UL_CH1" value="1" />
            <kctl name="ADDA_DL_CH2 DL1_CH1" value="1" />
        </path>
        <path name="USB_ECHO_REF_DEBUG" value="turnoff">
            <kctl name="ADDA_DL_CH1 DL2_CH1" value="0" />
            <kctl name="ADDA_DL_CH1 ADDA_UL_CH1" value="0" />
            <kctl name="ADDA_DL_CH2 DL1_CH1" value="0" />
        </path>
        <path name="USB_CALL_DEBUG_LOOPBACK" value="turnon">
            <kctl name="UL2_CH1 DL2_CH1" value="1" />
            <kctl name="UL2_CH2 DL2_CH2" value="1" />
        </path>
        <path name="USB_CALL_DEBUG_LOOPBACK" value="turnoff">
            <kctl name="UL2_CH1 DL2_CH1" value="0" />
            <kctl name="UL2_CH2 DL2_CH2" value="0" />
        </path>
        <path name="PLAYBACK1_TO_CAPTURE6" value="turnon">
            <kctl name="UL6_CH1 DL1_CH1" value="1" />
            <kctl name="UL6_CH2 DL1_CH2" value="1" />
        </path>
        <path name="PLAYBACK1_TO_CAPTURE6" value="turnoff">
            <kctl name="UL6_CH1 DL1_CH1" value="0" />
            <kctl name="UL6_CH2 DL1_CH2" value="0" />
        </path>
        <path name="PLAYBACK2_TO_CAPTURE6" value="turnon">
            <kctl name="UL6_CH1 DL2_CH1" value="1" />
            <kctl name="UL6_CH2 DL2_CH2" value="1" />
        </path>
        <path name="PLAYBACK2_TO_CAPTURE6" value="turnoff">
            <kctl name="UL6_CH1 DL2_CH1" value="0" />
            <kctl name="UL6_CH2 DL2_CH2" value="0" />
        </path>
        <path name="PLAYBACK3_TO_CAPTURE6" value="turnon">
            <kctl name="UL6_CH1 DL3_CH1" value="1" />
            <kctl name="UL6_CH2 DL3_CH2" value="1" />
        </path>
        <path name="PLAYBACK3_TO_CAPTURE6" value="turnoff">
            <kctl name="UL6_CH1 DL3_CH1" value="0" />
            <kctl name="UL6_CH2 DL3_CH2" value="0" />
        </path>
        <path name="PLAYBACK12_TO_CAPTURE6" value="turnon">
            <kctl name="UL6_CH1 DL12_CH1" value="1" />
            <kctl name="UL6_CH2 DL12_CH2" value="1" />
        </path>
        <path name="PLAYBACK12_TO_CAPTURE6" value="turnoff">
            <kctl name="UL6_CH1 DL12_CH1" value="0" />
            <kctl name="UL6_CH2 DL12_CH2" value="0" />
        </path>
        <path name="PLAYBACK6_TO_CAPTURE6" value="turnon">
            <kctl name="UL6_CH1 DL6_CH1" value="1" />
            <kctl name="UL6_CH2 DL6_CH2" value="1" />
        </path>
        <path name="PLAYBACK6_TO_CAPTURE6" value="turnoff">
            <kctl name="UL6_CH1 DL6_CH1" value="0" />
            <kctl name="UL6_CH2 DL6_CH2" value="0" />
        </path>
        <path name="PLAYBACK4_TO_CAPTURE6" value="turnoff">
            <kctl name="UL6_CH1 DL4_CH1" value="1" />
            <kctl name="UL6_CH2 DL4_CH2" value="1" />
        </path>
        <path name="PLAYBACK4_TO_CAPTURE6" value="turnoff">
            <kctl name="UL6_CH1 DL4_CH1" value="0" />
            <kctl name="UL6_CH2 DL4_CH2" value="0" />
        </path>
        <path name="MD1_TO_CAPTURE6" value="turnon">
            <kctl name="UL6_CH1 PCM_2_CAP_CH1" value="1" />
            <kctl name="UL6_CH2 PCM_2_CAP_CH1" value="1" />
        </path>
        <path name="MD1_TO_CAPTURE6" value="turnoff">
            <kctl name="UL6_CH1 PCM_2_CAP_CH1" value="0" />
            <kctl name="UL6_CH2 PCM_2_CAP_CH1" value="0" />
        </path>
        <path name="MD2_TO_CAPTURE6" value="turnon">
            <kctl name="UL6_CH1 PCM_1_CAP_CH1" value="1" />
            <kctl name="UL6_CH2 PCM_1_CAP_CH1" value="1" />
        </path>
        <path name="MD2_TO_CAPTURE6" value="turnoff">
            <kctl name="UL6_CH1 PCM_1_CAP_CH1" value="0" />
            <kctl name="UL6_CH2 PCM_1_CAP_CH1" value="0" />
        </path>
        <path name="PLAYBACK1_TO_DSPDL" value="turnon">
            <kctl name="DSP_DL DSP_DL1" value="1" />
        </path>
        <path name="PLAYBACK1_TO_DSPDL" value="turnoff">
            <kctl name="DSP_DL DSP_DL1" value="0" />
        </path>
        <path name="PLAYBACK2_TO_DSPDL" value="turnon">
            <kctl name="DSP_DL DSP_DL2" value="1" />
        </path>
        <path name="PLAYBACK2_TO_DSPDL" value="turnoff">
            <kctl name="DSP_DL DSP_DL2" value="0" />
        </path>
        <path name="PLAYBACK3_TO_DSPDL" value="turnon">
            <kctl name="DSP_DL DSP_DL3" value="1" />
        </path>
        <path name="PLAYBACK3_TO_DSPDL" value="turnoff">
            <kctl name="DSP_DL DSP_DL3" value="0" />
        </path>
        <path name="PLAYBACK4_TO_DSPDL" value="turnon">
            <kctl name="DSP_DL DSP_DL4" value="1" />
        </path>
        <path name="PLAYBACK4_TO_DSPDL" value="turnoff">
            <kctl name="DSP_DL DSP_DL4" value="0" />
        </path>
        <path name="PLAYBACK12_TO_DSPDL" value="turnon">
            <kctl name="DSP_DL DSP_DL12" value="1" />
        </path>
        <path name="PLAYBACK12_TO_DSPDL" value="turnoff">
            <kctl name="DSP_DL DSP_DL12" value="0" />
        </path>
        <path name="PLAYBACK6_TO_DSPDL" value="turnon">
            <kctl name="DSP_DL DSP_DL6" value="1" />
        </path>
        <path name="PLAYBACK6_TO_DSPDL" value="turnoff">
            <kctl name="DSP_DL DSP_DL6" value="0" />
        </path>
        <path name="HDMI_TDM_OUT" value="turnon">
            <kctl name="HDMI_OUT_MUX" value="Connect" />
        </path>
        <path name="HDMI_TDM_OUT" value="turnoff">
            <kctl name="HDMI_OUT_MUX" value="Disconnect" />
        </path>
        <path name="DPTX_TDM_OUT" value="turnon">
            <kctl name="DPTX_OUT_MUX" value="Connect" />
            <kctl name="DPTX_VIRTUAL_OUT_MUX" value="Connect" />
        </path>
        <path name="DPTX_TDM_OUT" value="turnoff">
            <kctl name="DPTX_OUT_MUX" value="Disconnect" />
            <kctl name="DPTX_VIRTUAL_OUT_MUX" value="Disconnect" />
        </path>
        <path name="PLAYBACK_TO_TDM_2CH" value="setting">
            <kctl name="HDMI_CH0_MUX" value="CH0" />
            <kctl name="HDMI_CH1_MUX" value="CH1" />
        </path>
        <path name="PLAYBACK_TO_TDM_4CH" value="setting">
            <kctl name="HDMI_CH0_MUX" value="CH0" />
            <kctl name="HDMI_CH1_MUX" value="CH1" />
            <kctl name="HDMI_CH2_MUX" value="CH2" />
            <kctl name="HDMI_CH3_MUX" value="CH3" />
        </path>
        <path name="PLAYBACK_TO_TDM_6CH" value="setting">
            <kctl name="HDMI_CH0_MUX" value="CH0" />
            <kctl name="HDMI_CH1_MUX" value="CH1" />
            <kctl name="HDMI_CH2_MUX" value="CH2" />
            <kctl name="HDMI_CH3_MUX" value="CH3" />
            <kctl name="HDMI_CH4_MUX" value="CH4" />
            <kctl name="HDMI_CH5_MUX" value="CH5" />
        </path>
        <path name="PLAYBACK_TO_TDM_8CH" value="setting">
            <kctl name="HDMI_CH0_MUX" value="CH0" />
            <kctl name="HDMI_CH1_MUX" value="CH1" />
            <kctl name="HDMI_CH2_MUX" value="CH2" />
            <kctl name="HDMI_CH3_MUX" value="CH3" />
            <kctl name="HDMI_CH4_MUX" value="CH4" />
            <kctl name="HDMI_CH5_MUX" value="CH5" />
            <kctl name="HDMI_CH6_MUX" value="CH6" />
            <kctl name="HDMI_CH7_MUX" value="CH7" />
        </path>
        <path name="MIC_TO_CAPTURE8" value="turnon">
            <kctl name="UL8_CH1 ADDA_UL_CH1" value="1" />
            <kctl name="UL8_CH2 ADDA_UL_CH2" value="1" />
        </path>
        <path name="MIC_TO_CAPTURE8" value="turnoff">
            <kctl name="UL8_CH1 ADDA_UL_CH1" value="0" />
            <kctl name="UL8_CH2 ADDA_UL_CH2" value="0" />
        </path>
        <path name="PLAYBACK8_TO_ADDA_DL" value="turnon">
            <kctl name="ADDA_DL_CH1 DL8_CH1" value="1" />
            <kctl name="ADDA_DL_CH2 DL8_CH2" value="1" />
        </path>
        <path name="PLAYBACK8_TO_ADDA_DL" value="turnoff">
            <kctl name="ADDA_DL_CH1 DL8_CH1" value="0" />
            <kctl name="ADDA_DL_CH2 DL8_CH2" value="0" />
        </path>
        <path name="PLAYBACK7_TO_ADDA_DL" value="turnon">
            <kctl name="ADDA_DL_CH1 DL7_CH1" value="1" />
            <kctl name="ADDA_DL_CH2 DL7_CH2" value="1" />
        </path>
        <path name="PLAYBACK7_TO_ADDA_DL" value="turnoff">
            <kctl name="ADDA_DL_CH1 DL7_CH1" value="0" />
            <kctl name="ADDA_DL_CH2 DL7_CH2" value="0" />
        </path>
        <path name="PLAYBACK7_TO_I2S3" value="turnon">
            <kctl name="I2S3_CH1 DL7_CH1" value="1" />
            <kctl name="I2S3_CH2 DL7_CH2" value="1" />
        </path>
        <path name="PLAYBACK7_TO_I2S3" value="turnoff">
            <kctl name="I2S3_CH1 DL7_CH1" value="0" />
            <kctl name="I2S3_CH2 DL7_CH2" value="0" />
        </path>
        <path name="ADDA_TO_CAPTURE5" value="turnon">
            <kctl name="UL5_CH1 ADDA_UL_CH1" value="1" />
            <kctl name="UL5_CH2 ADDA_UL_CH2" value="1" />
        </path>
        <path name="ADDA_TO_CAPTURE5" value="turnoff">
            <kctl name="UL5_CH1 ADDA_UL_CH1" value="0" />
            <kctl name="UL5_CH2 ADDA_UL_CH2" value="0" />
        </path>
        <path name="ultrasound-proximity" value="turnon">
        </path>
        <path name="ultrasound-proximity" value="turnoff">
        </path>
        <path name="ultrasound-proximity-speaker" value="turnon">
        </path>
        <path name="ultrasound-proximity-speaker" value="turnoff">
        </path>
    </mixercontrol>
</root>
