
r_dir_file(opluscamera_app, vendor_sysfs_graphics)
r_dir_file(opluscamera_app, persist_camera_file)
r_dir_file(opluscamera_app, persist_data_file)
allow opluscamera_app shell_data_file:file r_file_perms;
allow opluscamera_app shell_data_file:dir r_dir_perms;

allow opluscamera_app hal_osense_oplus_hwservice:hwservice_manager find;
allow opluscamera_app hal_performance_oplus_hwservice:hwservice_manager find;

hal_client_domain(opluscamera_app, hal_mtk_bgs)
hal_client_domain(opluscamera_app, hal_mtk_mmagent)

binder_call(opluscamera_app, mtk_hal_camera)
binder_call(opluscamera_app, hal_performance_oplus)
binder_call(opluscamera_app, mtk_hal_neuralnetworks)

allow opluscamera_app system_data_file:file r_file_perms;
allow opluscamera_app apusys_device:chr_file { ioctl read write open };
allow opluscamera_app mtk_hal_neuralnetworks:fd use;
allow opluscamera_app mtk_hal_camera:fd use;
allow opluscamera_app vpu_device:chr_file { ioctl read open };
get_prop(opluscamera_app, vendor_oplus_prop)
